#!/usr/bin/env python3
import csv
import os

def count_translated_transcripts(sra_accession):
    """
    统计指定SRA Accession对应的TPM文件中TPM值>=1的转录本数量
    """
    tpm_file = f"Transcript/{sra_accession}_final_merged_tpm.txt"
    
    if not os.path.exists(tpm_file):
        return None  # 文件不存在
    
    try:
        count = 0
        with open(tpm_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f, delimiter='\t')
            
            for row in reader:
                try:
                    tpm_value = float(row['TPM'])
                    if tpm_value >= 1.0:
                        count += 1
                except (ValueError, KeyError):
                    # 跳过无法解析的行
                    continue
        
        return count
        
    except Exception as e:
        print(f"读取文件 {tpm_file} 时出错: {e}")
        return None

def add_translated_transcripts_number():
    """
    为Sample.csv添加TRANSLATED TRANSCRIPTS NUMBER列
    """
    
    input_file = 'Sample.csv'
    output_file = 'Sample_with_translated_transcripts_number.csv'
    
    print("=== 为Sample.csv添加翻译转录本数量 ===")
    
    try:
        processed_count = 0
        found_count = 0
        not_found_count = 0
        
        with open(input_file, 'r', encoding='utf-8') as f_in, \
             open(output_file, 'w', encoding='utf-8', newline='') as f_out:
            
            reader = csv.DictReader(f_in)
            
            # 检查是否有SRA Accession列
            if 'SRA Accession' not in reader.fieldnames:
                print(f"错误: 输入文件中没有找到 'SRA Accession' 列")
                print(f"可用的列: {reader.fieldnames}")
                return False
            
            # 创建新的列名（添加TRANSLATED TRANSCRIPTS NUMBER）
            new_headers = list(reader.fieldnames) + ['TRANSLATED TRANSCRIPTS NUMBER']
            writer = csv.DictWriter(f_out, fieldnames=new_headers)
            writer.writeheader()
            
            # 处理每一行
            for row in reader:
                processed_count += 1
                sra_accession = row['SRA Accession'].strip()
                
                # 统计翻译转录本数量
                translated_count = count_translated_transcripts(sra_accession)
                
                if translated_count is not None:
                    row['TRANSLATED TRANSCRIPTS NUMBER'] = str(translated_count)
                    found_count += 1
                else:
                    row['TRANSLATED TRANSCRIPTS NUMBER'] = ''
                    not_found_count += 1
                    print(f"未找到文件: Transcript/{sra_accession}_final_merged_tpm.txt")
                
                writer.writerow(row)
                
                # 进度显示
                if processed_count % 100 == 0:
                    print(f"  已处理 {processed_count} 行...")
        
        # 输出统计信息
        print(f"\n处理完成!")
        print(f"总行数: {processed_count}")
        print(f"找到TPM文件: {found_count}")
        print(f"未找到TPM文件: {not_found_count}")
        print(f"成功率: {found_count/processed_count*100:.2f}%")
        print(f"\n结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def verify_results():
    """验证处理结果"""
    
    output_file = 'Sample_with_translated_transcripts_number.csv'
    
    print("\n=== 验证处理结果 ===")
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"新文件的列名 (共 {len(headers)} 列):")
            for i, header in enumerate(headers):
                print(f"  {i+1}. {header}")
            
            # 检查前几行
            print(f"\n前5行数据示例:")
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n行 {i+1}:")
                print(f"  SRA Accession: {row['SRA Accession']}")
                print(f"  Dataset ID: {row['Dataset ID']}")
                print(f"  Disease Category: {row['Disease Category']}")
                print(f"  TRANSLATED TRANSCRIPTS NUMBER: {row['TRANSLATED TRANSCRIPTS NUMBER']}")
            
            # 统计翻译转录本数量分布
            f.seek(0)  # 重置文件指针
            reader = csv.DictReader(f)
            next(reader)  # 跳过表头
            
            total_count = 0
            empty_count = 0
            transcript_counts = []
            
            for row in reader:
                total_count += 1
                transcript_number = row['TRANSLATED TRANSCRIPTS NUMBER'].strip()
                
                if transcript_number:
                    try:
                        count = int(transcript_number)
                        transcript_counts.append(count)
                    except ValueError:
                        empty_count += 1
                else:
                    empty_count += 1
            
            print(f"\n统计信息:")
            print(f"总行数: {total_count}")
            print(f"空值数量: {empty_count}")
            print(f"有效数据: {len(transcript_counts)}")
            
            if transcript_counts:
                print(f"翻译转录本数量统计:")
                print(f"  最小值: {min(transcript_counts)}")
                print(f"  最大值: {max(transcript_counts)}")
                print(f"  平均值: {sum(transcript_counts)/len(transcript_counts):.2f}")
                
                # 显示一些分布信息
                ranges = [
                    (0, 1000),
                    (1000, 5000),
                    (5000, 10000),
                    (10000, 20000),
                    (20000, float('inf'))
                ]
                
                print(f"  分布:")
                for min_val, max_val in ranges:
                    if max_val == float('inf'):
                        count = sum(1 for x in transcript_counts if x >= min_val)
                        print(f"    >= {min_val}: {count}")
                    else:
                        count = sum(1 for x in transcript_counts if min_val <= x < max_val)
                        print(f"    {min_val}-{max_val-1}: {count}")
        
        return True
        
    except Exception as e:
        print(f"验证结果时出错: {e}")
        return False

def main():
    """主函数"""
    
    # 添加翻译转录本数量
    if add_translated_transcripts_number():
        # 验证结果
        verify_results()
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
转录本基因信息补充脚本
对于mapping_status为not_found的转录本，从Transcript_id_with_multiple_flag.csv文件中查找基因信息
"""

import csv
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def load_multiple_flag_data(file_path):
    """加载Transcript_id_with_multiple_flag.csv文件数据"""
    transcript_data = {}

    logging.info(f"🔄 加载多值标记文件: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)

            total_rows = 0
            for row in reader:
                total_rows += 1

                transcript_id = row['Transcript'].strip()
                ensembl_gene_id = row['ensembl_gene_id'].strip()
                external_gene_name = row['external_gene_name'].strip()
                single_value = row['single_value'].strip()

                transcript_data[transcript_id] = {
                    'ensembl_gene_id': ensembl_gene_id,
                    'external_gene_name': external_gene_name,
                    'single_value': single_value
                }

                if total_rows % 50000 == 0:
                    logging.info(f"  已处理 {total_rows:,} 行...")

        logging.info(f"✅ 成功加载 {total_rows:,} 行多值标记数据")
        logging.info(f"📊 唯一转录本数量: {len(transcript_data):,}")

        return transcript_data

    except Exception as e:
        logging.error(f"❌ 加载多值标记文件失败: {e}")
        return {}

def process_not_found_transcripts():
    """处理not_found的转录本，补充基因信息"""

    print("=" * 80)
    print("🧬 转录本基因信息补充工具")
    print("📁 补充mapping_status为not_found的转录本基因信息")
    print("=" * 80)

    start_time = datetime.now()

    # 文件路径
    input_file = 'Transcript_id_with_gene_info_latest.csv'
    multiple_flag_file = 'Transcript_id_with_multiple_flag.csv'
    output_file = 'Transcript_id_with_gene_info_completed.csv'

    try:
        # 1. 加载多值标记数据
        multiple_flag_data = load_multiple_flag_data(multiple_flag_file)

        if not multiple_flag_data:
            logging.error("❌ 无法加载多值标记数据，程序退出")
            return 1

        # 2. 处理主文件
        logging.info(f"🔄 处理主文件: {input_file}")

        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:

            reader = csv.DictReader(infile)

            # 创建新的表头，添加多值标记列
            fieldnames = reader.fieldnames + ['has_multiple_values', 'data_source']
            writer = csv.DictWriter(outfile, fieldnames=fieldnames)
            writer.writeheader()

            # 统计变量
            total_rows = 0
            not_found_count = 0
            supplemented_count = 0
            still_not_found = 0

            # 处理每一行
            for row in reader:
                total_rows += 1
                transcript_id = row['Transcript'].strip()
                mapping_status = row.get('mapping_status', '').strip()

                # 初始化新字段
                row['has_multiple_values'] = 'No'
                row['data_source'] = 'original'

                if mapping_status == 'not_found':
                    not_found_count += 1

                    # 在多值标记文件中查找
                    if transcript_id in multiple_flag_data:
                        flag_data = multiple_flag_data[transcript_id]

                        # 更新基因信息
                        if flag_data['ensembl_gene_id']:
                            row['ensembl_gene_id'] = flag_data['ensembl_gene_id']
                        if flag_data['external_gene_name']:
                            row['external_gene_name'] = flag_data['external_gene_name']

                        # 检查是否有基因信息被补充
                        if flag_data['ensembl_gene_id'] or flag_data['external_gene_name']:
                            row['mapping_status'] = 'found_in_multiple_flag'
                            supplemented_count += 1
                            row['data_source'] = 'multiple_flag_file'

                            # 标记是否有多值
                            if flag_data['single_value'] == 'No':
                                row['has_multiple_values'] = 'Yes'
                        else:
                            still_not_found += 1
                    else:
                        still_not_found += 1

                writer.writerow(row)

                # 进度报告
                if total_rows % 25000 == 0:
                    logging.info(f"  已处理 {total_rows:,} 行...")

        # 最终统计
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        logging.info(f"✅ 处理完成！")
        logging.info(f"📊 处理统计:")
        logging.info(f"  总行数: {total_rows:,}")
        logging.info(f"  原始not_found数量: {not_found_count:,}")
        logging.info(f"  成功补充: {supplemented_count:,}")
        logging.info(f"  仍未找到: {still_not_found:,}")
        logging.info(f"  补充成功率: {supplemented_count/not_found_count*100:.2f}%" if not_found_count > 0 else "  补充成功率: 0%")

        print("\n" + "=" * 80)
        print("📊 最终处理报告")
        print("=" * 80)
        print(f"📅 处理时间: {processing_time:.2f} 秒")
        print(f"⚡ 处理速度: {total_rows/processing_time:.0f} 行/秒")
        print(f"📁 输出文件: {output_file}")
        print(f"📈 补充成功率: {supplemented_count/not_found_count*100:.2f}%" if not_found_count > 0 else "📈 补充成功率: 0%")
        print(f"🔍 原始not_found: {not_found_count:,}")
        print(f"✅ 成功补充: {supplemented_count:,}")
        print(f"❌ 仍未找到: {still_not_found:,}")
        print("=" * 80)
        print("🎉 处理完成！")

        return 0

    except Exception as e:
        logging.error(f"❌ 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(process_not_found_transcripts())

import pandas as pd

# 读取两个CSV文件
kegg_df = pd.read_csv('process_gene/KEGG_final.csv')
unique_genes_df = pd.read_csv('process_gene/unique_genes_with_project_ids.csv')

# 创建从ensembl_gene_id到translation_project_ids的映射字典
gene_to_project_ids = dict(zip(unique_genes_df['ensembl_gene_id'], unique_genes_df['translation_project_ids']))

# 为KEGG_final.csv添加translation_project_ids列
kegg_df['translation_project_ids'] = kegg_df['Gene_ID'].map(gene_to_project_ids)

# 显示有多少条记录成功匹配
matched_count = kegg_df['translation_project_ids'].notna().sum()
total_count = len(kegg_df)
print(f"成功匹配 {matched_count} 条记录，总共 {total_count} 条记录")

# 保存更新后的文件
kegg_df.to_csv('process_gene/KEGG_final_with_project_ids.csv', index=False)
print("文件已保存为 KEGG_final_with_project_ids.csv")

# 显示前5行作为示例
print("\n前5行数据:")
print(kegg_df.head())
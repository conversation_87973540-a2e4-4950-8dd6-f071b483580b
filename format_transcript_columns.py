#!/usr/bin/env python3
"""
处理 Transcript_id_processed_fast.csv 文件
将 ensembl_gene_id 和 external_gene_name 列从JSON格式转换为分号分隔格式
"""

import pandas as pd
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_json_column(json_str):
    """
    解析JSON字符串并转换为分号分隔的字符串
    
    Args:
        json_str: JSON格式的字符串，如 '["ENSG00000157978", "ENSG00000225643"]'
        
    Returns:
        str: 分号分隔的字符串，如 'ENSG00000157978; ENSG00000225643'
    """
    try:
        # 处理空值
        if pd.isna(json_str) or json_str == '' or json_str == '[]':
            return ''
        
        # 解析JSON
        data_list = json.loads(json_str)
        
        # 如果是空列表
        if not data_list:
            return ''
        
        # 如果只有一个值，直接返回
        if len(data_list) == 1:
            return data_list[0]
        
        # 多个值用分号分隔
        return '; '.join(data_list)
        
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"解析JSON时出错: {json_str}, 错误: {e}")
        return str(json_str)  # 如果解析失败，返回原始值

def process_transcript_file(input_file='Transcript_id_processed_fast.csv', 
                          output_file='Transcript_id_formatted.csv'):
    """
    处理转录本文件，格式化指定列
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    try:
        logger.info(f"读取文件: {input_file}")
        df = pd.read_csv(input_file)
        
        logger.info(f"文件包含 {len(df):,} 行数据")
        
        # 检查必要的列是否存在
        required_columns = ['ensembl_gene_id', 'external_gene_name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")
        
        logger.info("开始处理 ensembl_gene_id 列...")
        df['ensembl_gene_id'] = df['ensembl_gene_id'].apply(parse_json_column)
        
        logger.info("开始处理 external_gene_name 列...")
        df['external_gene_name'] = df['external_gene_name'].apply(parse_json_column)
        
        # 统计处理结果
        non_empty_ensembl = df[df['ensembl_gene_id'] != ''].shape[0]
        non_empty_external = df[df['external_gene_name'] != ''].shape[0]
        
        logger.info(f"保存结果到: {output_file}")
        df.to_csv(output_file, index=False)
        
        logger.info("=" * 50)
        logger.info("处理完成!")
        logger.info(f"总行数: {len(df):,}")
        logger.info(f"有ensembl_gene_id的行数: {non_empty_ensembl:,}")
        logger.info(f"有external_gene_name的行数: {non_empty_external:,}")
        logger.info("=" * 50)
        
        # 显示一些示例
        logger.info("处理后的示例数据:")
        sample_data = df[df['ensembl_gene_id'] != ''].head(5)
        for idx, row in sample_data.iterrows():
            logger.info(f"  {row.get('Transcript', 'N/A')} -> {row['ensembl_gene_id']} | {row['external_gene_name']}")
        
        return df
        
    except Exception as e:
        logger.error(f"处理文件时出错: {e}")
        raise

def main():
    """主函数"""
    try:
        # 处理文件
        result_df = process_transcript_file()
        
        print("\n✅ 文件处理成功完成!")
        print("📁 输出文件: Transcript_id_formatted.csv")
        
        # 显示前几行结果作为验证
        print("\n📋 前5行结果预览:")
        print(result_df.head())
        
    except FileNotFoundError:
        print("❌ 错误: 找不到文件 'Transcript_id_processed_fast.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    print("🔧 转录本文件格式化工具")
    print("📝 将JSON格式的列转换为分号分隔格式")
    print("=" * 50)
    main()

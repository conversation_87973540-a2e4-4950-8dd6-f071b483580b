#!/usr/bin/env python3

import os
import glob

print("当前目录:", os.getcwd())
print()

print("查找CSV文件:")
csv_files = glob.glob("*.csv")
for f in csv_files:
    size = os.path.getsize(f)
    print(f"  {f} ({size:,} bytes)")

print()

# 检查特定文件
files_to_check = [
    'Transcript_id.csv',
    'Transcript_id_with_multiple_flag.csv', 
    'Transcript_id_processed_fast.csv'
]

for filename in files_to_check:
    if os.path.exists(filename):
        print(f"✅ {filename} 存在")
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                print(f"   表头: {first_line}")
                
                # 读取几行数据
                lines = []
                for i, line in enumerate(f):
                    if i < 3:
                        lines.append(line.strip())
                    else:
                        break
                
                print(f"   前3行数据:")
                for i, line in enumerate(lines, 1):
                    print(f"     {i}: {line}")
                print()
        except Exception as e:
            print(f"   ❌ 读取错误: {e}")
            print()
    else:
        print(f"❌ {filename} 不存在")
        print()

#!/usr/bin/env python3
"""
统计处理结果
"""

import csv

def count_results():
    filename = 'Transcript_id_with_multiple_flag.csv'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)  # 跳过表头
            
            total_rows = 0
            yes_count = 0
            false_count = 0
            
            for row in reader:
                total_rows += 1
                if len(row) > 0:
                    last_col = row[-1]
                    if last_col == 'yes':
                        yes_count += 1
                    elif last_col == 'false':
                        false_count += 1
        
        print("=" * 50)
        print("📊 处理结果统计")
        print("=" * 50)
        print(f"总数据行数: {total_rows:,}")
        print(f"single_value = 'yes' 的行数: {yes_count:,}")
        print(f"single_value = 'false' 的行数: {false_count:,}")
        print(f"yes 的百分比: {yes_count/total_rows*100:.2f}%")
        print(f"false 的百分比: {false_count/total_rows*100:.2f}%")
        print("=" * 50)
        
    except FileNotFoundError:
        print(f"找不到文件: {filename}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    count_results()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理KEGG_pathway_results文件夹下的所有xlsx文件
根据projectId查询project_unique_info.csv文件，添加Condition和Disease Category列
"""

import pandas as pd
import os
from pathlib import Path
import time

def load_project_info():
    """
    加载项目信息CSV文件，创建projectId到Condition和Disease Category的映射
    """
    project_info_file = "/Volumes/zhy/整合的所有TPM文件/compute_gene/project_unique_info.csv"
    
    try:
        # 读取项目信息文件
        df = pd.read_csv(project_info_file)
        print(f"成功读取项目信息文件，共 {len(df)} 行")
        
        # 创建映射字典
        project_mapping = {}
        for _, row in df.iterrows():
            project_id = row['Project ID']
            condition = row['Condition']
            disease_category = row['Disease Category']
            project_mapping[project_id] = {
                'Condition': condition,
                'Disease Category': disease_category
            }
        
        print(f"创建了 {len(project_mapping)} 个项目的映射信息")
        return project_mapping
        
    except Exception as e:
        print(f"❌ 读取项目信息文件失败: {e}")
        return None

def process_single_xlsx_file(file_path, project_mapping, output_dir):
    """
    处理单个xlsx文件，添加Condition和Disease Category列
    """
    try:
        # 读取xlsx文件
        df = pd.read_excel(file_path)
        
        if df.empty:
            print(f"  文件为空，跳过")
            return False
        
        # 检查是否有projectId列
        if 'projectId' not in df.columns:
            print(f"  文件中没有找到projectId列，跳过")
            return False
        
        print(f"  原始数据: {len(df)} 行")
        
        # 添加新列
        conditions = []
        disease_categories = []
        
        matched_count = 0
        unmatched_count = 0
        
        for _, row in df.iterrows():
            project_id = str(row['projectId']).strip()
            
            if project_id in project_mapping:
                condition = project_mapping[project_id]['Condition']
                disease_category = project_mapping[project_id]['Disease Category']
                matched_count += 1
            else:
                condition = 'Unknown'
                disease_category = 'Unknown'
                unmatched_count += 1
            
            conditions.append(condition)
            disease_categories.append(disease_category)
        
        # 添加新列到DataFrame
        df['Condition'] = conditions
        df['Disease Category'] = disease_categories
        
        # 保存到输出目录
        filename = os.path.basename(file_path)
        output_path = os.path.join(output_dir, filename)
        df.to_excel(output_path, index=False, engine='openpyxl')
        
        print(f"  处理完成: 匹配 {matched_count}, 未匹配 {unmatched_count}")
        print(f"  已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 处理文件失败: {e}")
        return False

def process_kegg_pathway_files():
    """
    处理KEGG_pathway_results文件夹下的所有xlsx文件
    """
    # 文件路径配置
    input_dir = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_pathway_results"
    output_dir = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_pathway_results_with_info"
    
    start_time = time.time()
    print("=== 处理KEGG通路文件，添加项目信息 ===")
    
    # 1. 加载项目信息映射
    print("正在加载项目信息...")
    project_mapping = load_project_info()
    
    if project_mapping is None:
        print("❌ 无法加载项目信息，退出处理")
        return False
    
    # 2. 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    print(f"输出目录已创建: {output_dir}")
    
    # 3. 获取所有xlsx文件
    input_path = Path(input_dir)
    xlsx_files = list(input_path.glob("*.xlsx"))
    
    if not xlsx_files:
        print("❌ 未找到任何xlsx文件")
        return False
    
    print(f"找到 {len(xlsx_files)} 个xlsx文件")
    
    # 4. 处理每个文件
    successful_count = 0
    failed_count = 0
    
    for i, file_path in enumerate(xlsx_files, 1):
        filename = file_path.name
        print(f"\n处理文件 ({i}/{len(xlsx_files)}): {filename}")
        
        if process_single_xlsx_file(file_path, project_mapping, output_dir):
            successful_count += 1
        else:
            failed_count += 1
    
    # 5. 输出处理结果统计
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== 处理完成 ===")
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"总文件数: {len(xlsx_files)}")
    print(f"成功处理: {successful_count}")
    print(f"处理失败: {failed_count}")
    print(f"成功率: {successful_count/len(xlsx_files)*100:.2f}%")
    
    if successful_count > 0:
        print(f"\n✅ 处理完成的文件已保存到: {output_dir}")
    
    return successful_count > 0

def verify_sample_files():
    """
    验证几个处理后的文件
    """
    output_dir = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_pathway_results_with_info"
    
    print("\n=== 验证处理结果 ===")
    
    try:
        output_path = Path(output_dir)
        xlsx_files = list(output_path.glob("*.xlsx"))
        
        if not xlsx_files:
            print("未找到处理后的文件")
            return
        
        # 检查前3个文件
        sample_files = xlsx_files[:3]
        
        for file_path in sample_files:
            print(f"\n检查文件: {file_path.name}")
            
            try:
                df = pd.read_excel(file_path)
                
                print(f"  行数: {len(df)}")
                print(f"  列数: {len(df.columns)}")
                print(f"  列名: {list(df.columns)}")
                
                # 检查新添加的列
                if 'Condition' in df.columns and 'Disease Category' in df.columns:
                    print("  ✅ 新列已成功添加")
                    
                    # 显示几个样例数据
                    print("  前3行样例数据:")
                    for i in range(min(3, len(df))):
                        row = df.iloc[i]
                        print(f"    行 {i+1}: projectId={row.get('projectId', 'N/A')}, "
                              f"Condition='{row.get('Condition', 'N/A')}', "
                              f"Disease Category='{row.get('Disease Category', 'N/A')}'")
                else:
                    print("  ❌ 新列未找到")
                
            except Exception as e:
                print(f"  ❌ 读取文件失败: {e}")
        
    except Exception as e:
        print(f"验证过程出错: {e}")

def main():
    """
    主函数
    """
    # 处理文件
    if process_kegg_pathway_files():
        # 验证结果
        verify_sample_files()
        print("\n✅ 所有处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()
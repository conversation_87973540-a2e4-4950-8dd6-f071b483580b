#!/usr/bin/env python3
"""
简化版本的转录本数据处理脚本
"""

import pandas as pd
import pymysql
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'charset': 'utf8mb4'
}

def process_transcript_csv(input_file='Transcript_id.csv', output_file='Transcript_id_processed.csv'):
    """
    处理转录本CSV文件的主函数
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    connection = None
    
    try:
        # 连接数据库
        logger.info("连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        
        # 读取CSV文件
        logger.info(f"读取CSV文件: {input_file}")
        df = pd.read_csv(input_file)
        
        if 'Transcript' not in df.columns:
            raise ValueError("CSV文件中缺少 'Transcript' 列")
        
        logger.info(f"CSV文件包含 {len(df)} 行数据")
        
        # 初始化新列
        df['ensembl_gene_id'] = ''
        df['external_gene_name'] = ''
        
        # 获取所有唯一的转录本ID
        unique_transcripts = df['Transcript'].dropna().unique()
        logger.info(f"发现 {len(unique_transcripts)} 个唯一的转录本ID")
        
        # 批量查询数据库
        transcript_data = {}
        
        if len(unique_transcripts) > 0:
            # 构建批量查询SQL
            placeholders = ','.join(['%s'] * len(unique_transcripts))
            sql = f"""
            SELECT transcriptId, ensemblGeneId, externalGeneName
            FROM tpmData 
            WHERE transcriptId IN ({placeholders})
            """
            
            with connection.cursor() as cursor:
                cursor.execute(sql, unique_transcripts.tolist())
                results = cursor.fetchall()
                
                # 组织数据
                for transcript_id, ensembl_gene_id, external_gene_name in results:
                    if transcript_id not in transcript_data:
                        transcript_data[transcript_id] = {'ensembl': set(), 'external': set()}
                    
                    if ensembl_gene_id and ensembl_gene_id.strip():
                        transcript_data[transcript_id]['ensembl'].add(ensembl_gene_id.strip())
                    if external_gene_name and external_gene_name.strip():
                        transcript_data[transcript_id]['external'].add(external_gene_name.strip())
        
        logger.info(f"从数据库获取了 {len(transcript_data)} 个转录本的数据")
        
        # 填充数据到DataFrame
        logger.info("填充数据到DataFrame...")
        for index, row in df.iterrows():
            transcript_id = row['Transcript']
            
            if pd.isna(transcript_id) or transcript_id not in transcript_data:
                # 未找到数据，设置为空数组
                df.at[index, 'ensembl_gene_id'] = json.dumps([])
                df.at[index, 'external_gene_name'] = json.dumps([])
            else:
                # 转换集合为排序的列表并转为JSON
                ensembl_list = sorted(list(transcript_data[transcript_id]['ensembl']))
                external_list = sorted(list(transcript_data[transcript_id]['external']))
                
                df.at[index, 'ensembl_gene_id'] = json.dumps(ensembl_list)
                df.at[index, 'external_gene_name'] = json.dumps(external_list)
        
        # 保存结果
        logger.info(f"保存结果到: {output_file}")
        df.to_csv(output_file, index=False)
        
        # 统计信息
        non_empty_ensembl = df[df['ensembl_gene_id'] != '[]'].shape[0]
        non_empty_external = df[df['external_gene_name'] != '[]'].shape[0]
        
        logger.info("处理完成!")
        logger.info(f"总行数: {len(df)}")
        logger.info(f"找到ensembl_gene_id的行数: {non_empty_ensembl}")
        logger.info(f"找到external_gene_name的行数: {non_empty_external}")
        
        return df
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        raise
    finally:
        if connection:
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    # 运行处理函数
    try:
        result_df = process_transcript_csv()
        print("处理成功完成!")
        
        # 显示前几行结果作为示例
        print("\n前5行结果预览:")
        print(result_df.head())
        
    except FileNotFoundError:
        print("错误: 找不到 Transcript_id.csv 文件")
    except Exception as e:
        print(f"错误: {e}")

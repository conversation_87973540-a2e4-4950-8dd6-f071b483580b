# translatedTranscriptsNumber列添加处理报告

## 处理概述
成功为 `compute_gene/project_unique_info.csv` 文件添加了 `translatedTranscriptsNumber` 列，该列显示每个Project ID在 `process_transcript/translation_indices_results_grouped.csv` 文件中对应的行数（即翻译的转录本数量）。

## 输入文件信息
### 1. 项目信息文件
- **文件路径**: `compute_gene/project_unique_info.csv`
- **原始数据**: 279行，5列
- **原始列**: Project ID, Tissue/Cell Type, Cell line, Condition, Disease Category

### 2. 翻译指数文件
- **文件路径**: `process_transcript/translation_indices_results_grouped.csv`
- **数据规模**: 12,935,373行
- **使用列**: projectId
- **唯一项目数**: 279个

## 处理结果
### 输出文件
- **文件路径**: `compute_gene/project_unique_info_with_transcript_counts.csv`
- **最终数据**: 279行，6列
- **新增列**: translatedTranscriptsNumber

### 统计信息
- **总项目数**: 279
- **有transcript数据的项目**: 279 (100%)
- **无transcript数据的项目**: 0

### translatedTranscriptsNumber列统计
- **最小值**: 131
- **最大值**: 107,924
- **平均值**: 46,363.34
- **中位数**: 47,414

## 数据示例
### 前10行结果
| Project ID | Tissue/Cell Type | Cell line | Condition | Disease Category | translatedTranscriptsNumber |
|------------|------------------|-----------|-----------|------------------|----------------------------|
| TEDD00001 | | Huh7 | Adult Hepatocellular Carcinoma; Dengue Virus Infection | Infectious Disease | 38,896 |
| TEDD00002 | | Huh7 | Adult Hepatocellular Carcinoma; Dengue Virus Infection | Infectious Disease | 37,323 |
| TEDD00003 | | Huh7 | Adult Hepatocellular Carcinoma; Dengue Virus Infection | Infectious Disease | 43,722 |
| TEDD00004 | | Huh7 | Adult Hepatocellular Carcinoma; Dengue Virus Infection | Infectious Disease | 44,032 |
| TEDD00005 | | Huh7 | Adult Hepatocellular Carcinoma; Dengue Virus Infection | Infectious Disease | 13,040 |

### 转录本数量最多的前5个项目
| Project ID | translatedTranscriptsNumber |
|------------|----------------------------|
| TEDD00025 | 107,924 |
| TEDD00027 | 103,755 |
| TEDD00026 | 103,329 |
| TEDD00028 | 97,595 |
| TEDD00264 | 87,963 |

## 处理方法
1. **数据读取**: 分别读取项目信息文件和翻译指数文件
2. **计数统计**: 使用pandas的value_counts()方法统计每个projectId在翻译指数文件中的出现次数
3. **数据匹配**: 通过Project ID将计数结果匹配到项目信息文件
4. **列添加**: 为项目信息文件添加translatedTranscriptsNumber列
5. **结果保存**: 保存为新的CSV文件

## 验证结果
- ✅ 所有279个项目都成功匹配到了transcript数据
- ✅ 没有项目的translatedTranscriptsNumber为0
- ✅ 数据范围合理（131 - 107,924）
- ✅ 输出文件格式正确

## 生成文件
1. **主要输出**: `compute_gene/project_unique_info_with_transcript_counts.csv`
2. **处理日志**: `add_transcript_counts_to_project_log.txt`
3. **处理脚本**: `add_transcript_counts_to_project.py`

## 使用说明
新生成的文件可以用于：
- 分析不同项目的转录本翻译活跃度
- 比较不同疾病类别的转录本数量分布
- 进行基于转录本数量的项目筛选和分析

处理完成时间: 2025年7月30日

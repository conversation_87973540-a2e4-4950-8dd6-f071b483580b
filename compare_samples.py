#!/usr/bin/env python3
"""
脚本功能：
1. 从Gene文件夹中提取所有样本ID（第一个_前面的内容）
2. 从Sample.csv文件的第一列读取样本ID
3. 找出在Gene文件夹中但不在Sample.csv中的样本ID
4. 将结果保存到CSV文件中
"""

import os
import csv
from collections import defaultdict

def extract_sample_id(filename):
    """从文件名中提取第一个'_'前面的样本ID"""
    if filename.endswith('.txt'):
        return filename.split('_')[0]
    return None

def get_gene_sample_ids():
    """获取所有Gene文件夹下的样本ID"""
    # 检查是否存在Gene_01, Gene_02, Gene_03文件夹，如果不存在则使用Gene文件夹
    gene_folders = ['Gene_01', 'Gene_02', 'Gene_03']
    if not any(os.path.exists(folder) for folder in gene_folders):
        gene_folders = ['Gene']

    all_gene_samples = set()
    folder_samples = {}

    print("正在从Gene文件夹中提取样本ID...")
    for folder in gene_folders:
        if os.path.exists(folder):
            folder_samples[folder] = set()
            for filename in os.listdir(folder):
                if filename.endswith('.txt'):
                    sample_id = extract_sample_id(filename)
                    if sample_id:
                        all_gene_samples.add(sample_id)
                        folder_samples[folder].add(sample_id)
            print(f"  {folder}: {len(folder_samples[folder])} 个样本")
        else:
            print(f"  警告: {folder} 文件夹不存在")
            folder_samples[folder] = set()

    print(f"总共找到 {len(all_gene_samples)} 个唯一的Gene样本ID")
    return all_gene_samples, folder_samples

def get_sample_csv_ids(csv_file='Sample.csv'):
    """从Sample.csv文件的第一列读取样本ID"""
    sample_ids = set()
    
    print(f"\n正在从 {csv_file} 中读取样本ID...")
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)  # 跳过表头
            print(f"  第一列表头: {header[0]}")
            
            for row in reader:
                if row and row[0]:  # 确保行不为空且第一列有值
                    # 移除可能的引号
                    sample_id = row[0].strip().strip('"')
                    sample_ids.add(sample_id)
            
        print(f"  从Sample.csv中读取到 {len(sample_ids)} 个样本ID")
        
    except FileNotFoundError:
        print(f"  错误: 找不到文件 {csv_file}")
        return set()
    except Exception as e:
        print(f"  错误: 读取文件时出现问题 - {e}")
        return set()
    
    return sample_ids

def main():
    # 获取Gene文件夹中的样本ID
    gene_samples, folder_samples = get_gene_sample_ids()
    
    # 获取Sample.csv中的样本ID
    sample_csv_ids = get_sample_csv_ids()
    
    if not sample_csv_ids:
        print("无法读取Sample.csv文件，程序退出")
        return
    
    # 找出在Gene文件夹中但不在Sample.csv中的样本ID
    not_in_sample = gene_samples - sample_csv_ids
    
    print(f"\n比较结果:")
    print(f"  Gene文件夹中的样本总数: {len(gene_samples)}")
    print(f"  Sample.csv中的样本总数: {len(sample_csv_ids)}")
    print(f"  在Gene中但不在Sample.csv中的样本数: {len(not_in_sample)}")
    
    # 找出在Sample.csv中但不在Gene文件夹中的样本ID（额外信息）
    not_in_gene = sample_csv_ids - gene_samples
    print(f"  在Sample.csv中但不在Gene中的样本数: {len(not_in_gene)}")
    
    # 保存不在Sample.csv中的样本ID
    if not_in_sample:
        output_file = 'samples_not_in_sample_csv.csv'
        
        # 为每个样本ID标记它出现在哪些Gene文件夹中
        detailed_results = []
        for sample_id in sorted(not_in_sample):
            folders = []
            for folder, samples in folder_samples.items():
                if sample_id in samples:
                    folders.append(folder)
            
            detailed_results.append({
                'Sample_ID': sample_id,
                'Found_in_Folders': ', '.join(folders),
                'Folder_Count': len(folders)
            })
        
        # 保存到CSV文件
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow(['Sample_ID', 'Found_in_Folders', 'Folder_Count'])
            
            # 写入数据
            for result in detailed_results:
                writer.writerow([result['Sample_ID'], result['Found_in_Folders'], result['Folder_Count']])
        
        print(f"\n不在Sample.csv中的样本ID已保存到: {output_file}")
        
        # 显示前10个示例
        print(f"\n前10个不在Sample.csv中的样本ID:")
        for i, result in enumerate(detailed_results[:10]):
            print(f"  {i+1}. {result['Sample_ID']} (出现在: {result['Found_in_Folders']})")
        
        if len(detailed_results) > 10:
            print(f"  ... 还有 {len(detailed_results) - 10} 个样本")
    
    else:
        print("\n所有Gene文件夹中的样本ID都在Sample.csv中找到了！")
    
    print("\n处理完成！")

if __name__ == "__main__":
    main()

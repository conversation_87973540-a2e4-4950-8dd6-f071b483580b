#!/usr/bin/env python3
"""
使用最新的对应关系Gene_transcript.csv文件为Transcript_id.csv添加基因信息
"""

import csv
import logging
from datetime import datetime
from collections import defaultdict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gene_mapping.log'),
        logging.StreamHandler()
    ]
)

def load_gene_transcript_mapping(mapping_file):
    """
    加载基因-转录本对应关系
    返回: {transcript_id: (gene_id, gene_name)}
    """
    logging.info(f"🔄 加载基因-转录本对应关系文件: {mapping_file}")
    
    transcript_to_gene = {}
    duplicate_transcripts = defaultdict(list)
    
    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            total_rows = 0
            for row in reader:
                total_rows += 1
                
                # 提取数据
                gene_id = row['Gene stable ID'].strip()
                transcript_id = row['Transcript stable ID'].strip()
                gene_name = row['Gene name'].strip()
                
                # 检查重复的转录本ID
                if transcript_id in transcript_to_gene:
                    duplicate_transcripts[transcript_id].append((gene_id, gene_name))
                else:
                    transcript_to_gene[transcript_id] = (gene_id, gene_name)
                
                if total_rows % 50000 == 0:
                    logging.info(f"  已处理 {total_rows:,} 行...")
        
        logging.info(f"✅ 成功加载 {total_rows:,} 行对应关系")
        logging.info(f"📊 唯一转录本数量: {len(transcript_to_gene):,}")
        
        if duplicate_transcripts:
            logging.warning(f"⚠️  发现 {len(duplicate_transcripts)} 个重复的转录本ID")
            # 记录前几个重复的例子
            for i, (transcript_id, gene_list) in enumerate(duplicate_transcripts.items()):
                if i < 5:  # 只显示前5个
                    logging.warning(f"  {transcript_id}: {len(gene_list)+1} 个基因")
        
        return transcript_to_gene, duplicate_transcripts
        
    except Exception as e:
        logging.error(f"❌ 加载对应关系文件失败: {e}")
        raise

def process_transcript_file(input_file, output_file, transcript_to_gene, duplicate_transcripts):
    """
    处理转录本文件，添加基因信息
    """
    logging.info(f"🔄 处理转录本文件: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            
            reader = csv.reader(infile)
            writer = csv.writer(outfile)
            
            # 写入新的表头
            header = next(reader)
            new_header = header + ['ensembl_gene_id', 'external_gene_name', 'mapping_status']
            writer.writerow(new_header)
            
            # 统计变量
            total_transcripts = 0
            found_single = 0
            found_multiple = 0
            not_found = 0
            
            # 处理每一行
            for row in reader:
                total_transcripts += 1
                transcript_id = row[0].strip()
                
                # 查找基因信息
                if transcript_id in transcript_to_gene:
                    gene_id, gene_name = transcript_to_gene[transcript_id]
                    
                    if transcript_id in duplicate_transcripts:
                        # 多重映射
                        mapping_status = 'multiple'
                        found_multiple += 1
                        logging.debug(f"多重映射: {transcript_id}")
                    else:
                        # 单一映射
                        mapping_status = 'single'
                        found_single += 1
                    
                    new_row = row + [gene_id, gene_name, mapping_status]
                else:
                    # 未找到
                    new_row = row + ['', '', 'not_found']
                    not_found += 1
                
                writer.writerow(new_row)
                
                # 进度报告
                if total_transcripts % 25000 == 0:
                    logging.info(f"  已处理 {total_transcripts:,} 个转录本...")
        
        # 最终统计
        logging.info(f"✅ 处理完成！")
        logging.info(f"📊 处理统计:")
        logging.info(f"  总转录本数: {total_transcripts:,}")
        logging.info(f"  单一映射: {found_single:,} ({found_single/total_transcripts*100:.2f}%)")
        logging.info(f"  多重映射: {found_multiple:,} ({found_multiple/total_transcripts*100:.2f}%)")
        logging.info(f"  未找到: {not_found:,} ({not_found/total_transcripts*100:.2f}%)")
        logging.info(f"  成功映射率: {(found_single+found_multiple)/total_transcripts*100:.2f}%")
        
        return {
            'total': total_transcripts,
            'single': found_single,
            'multiple': found_multiple,
            'not_found': not_found
        }
        
    except Exception as e:
        logging.error(f"❌ 处理转录本文件失败: {e}")
        raise

def main():
    """主函数"""
    print("=" * 80)
    print("🧬 转录本基因信息添加工具")
    print("📁 使用最新的对应关系Gene_transcript.csv")
    print("=" * 80)
    
    # 文件路径
    mapping_file = '最新的对应关系Gene_transcript.csv'
    input_file = 'Transcript_id.csv'
    output_file = 'Transcript_id_with_gene_info_latest.csv'
    
    start_time = datetime.now()
    
    try:
        # 1. 加载基因-转录本对应关系
        transcript_to_gene, duplicate_transcripts = load_gene_transcript_mapping(mapping_file)
        
        # 2. 处理转录本文件
        stats = process_transcript_file(input_file, output_file, transcript_to_gene, duplicate_transcripts)
        
        # 3. 生成报告
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print("\n" + "=" * 80)
        print("📊 最终处理报告")
        print("=" * 80)
        print(f"📅 处理时间: {processing_time:.2f} 秒")
        print(f"⚡ 处理速度: {stats['total']/processing_time:.0f} 行/秒")
        print(f"📁 输出文件: {output_file}")
        print(f"📈 成功映射率: {(stats['single']+stats['multiple'])/stats['total']*100:.2f}%")
        print("=" * 80)
        print("🎉 处理完成！")
        
    except Exception as e:
        logging.error(f"❌ 程序执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

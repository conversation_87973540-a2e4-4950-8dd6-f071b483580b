#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import pandas as pd

def check_file():
    """检查文件是否存在并显示基本信息"""
    
    # 检查当前目录
    print("当前工作目录:", os.getcwd())
    
    # 检查compute_gene目录
    if os.path.exists("compute_gene"):
        print("\ncompute_gene目录存在")
        files = os.listdir("compute_gene")
        gene_count_files = [f for f in files if "gene_count" in f]
        print(f"包含'gene_count'的文件: {gene_count_files}")
    else:
        print("compute_gene目录不存在")
    
    # 尝试找到目标文件
    target_file = "compute_gene/gene_count_by_project_results_with_translation_indices_and_project_info.csv"
    
    if os.path.exists(target_file):
        print(f"\n找到目标文件: {target_file}")
        
        # 获取文件大小
        file_size = os.path.getsize(target_file)
        print(f"文件大小: {file_size} bytes")
        
        try:
            # 尝试读取文件
            df = pd.read_csv(target_file, nrows=5)  # 只读取前5行
            print(f"文件可以正常读取")
            print(f"列数: {len(df.columns)}")
            print(f"列名: {list(df.columns)}")
            print("\n前5行数据:")
            print(df)
            
        except Exception as e:
            print(f"读取文件时出错: {e}")
            
    else:
        print(f"\n目标文件不存在: {target_file}")
        
        # 搜索相似的文件
        print("\n搜索相似文件...")
        for root, dirs, files in os.walk("."):
            for file in files:
                if "gene_count" in file and "translation_indices" in file and "project_info" in file:
                    print(f"找到相似文件: {os.path.join(root, file)}")

if __name__ == "__main__":
    check_file()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查process_gene/unique_genes.csv文件中哪些ensembl_gene_id
不在process_gene/ensembl_gene_id_with_full_info_split_2_processed.csv文件的GENE ID列中
"""

import pandas as pd
import os
from datetime import datetime

def compare_gene_ids():
    """比较两个文件中的基因ID"""
    
    file1 = "process_gene/unique_genes.csv"
    file2 = "process_gene/ensembl_gene_id_with_full_info_split_2_processed.csv"
    
    try:
        print("=" * 80)
        print("基因ID比较分析")
        print("=" * 80)
        
        # 检查文件是否存在
        if not os.path.exists(file1):
            raise FileNotFoundError(f"文件不存在: {file1}")
        if not os.path.exists(file2):
            raise FileNotFoundError(f"文件不存在: {file2}")
        
        print(f"文件1: {file1}")
        print(f"文件2: {file2}")
        
        # 读取第一个文件 (unique_genes.csv)
        print(f"\n读取文件1...")
        df1 = pd.read_csv(file1)
        print(f"文件1信息:")
        print(f"  行数: {len(df1):,}")
        print(f"  列名: {list(df1.columns)}")
        
        # 读取第二个文件 (ensembl_gene_id_with_full_info_split_2_processed.csv)
        print(f"\n读取文件2...")
        df2 = pd.read_csv(file2)
        print(f"文件2信息:")
        print(f"  行数: {len(df2):,}")
        print(f"  列名: {list(df2.columns)}")
        
        # 提取基因ID集合
        genes_in_file1 = set(df1['ensembl_gene_id'].dropna())
        genes_in_file2 = set(df2['GENE ID'].dropna())
        
        print(f"\n基因ID统计:")
        print(f"  文件1中的唯一基因ID数: {len(genes_in_file1):,}")
        print(f"  文件2中的唯一基因ID数: {len(genes_in_file2):,}")
        
        # 找出在文件1中但不在文件2中的基因ID
        missing_genes = genes_in_file1 - genes_in_file2
        
        # 找出在文件2中但不在文件1中的基因ID
        extra_genes = genes_in_file2 - genes_in_file1
        
        # 找出两个文件都有的基因ID
        common_genes = genes_in_file1 & genes_in_file2
        
        print(f"\n比较结果:")
        print(f"  两个文件都有的基因ID数: {len(common_genes):,}")
        print(f"  只在文件1中的基因ID数: {len(missing_genes):,}")
        print(f"  只在文件2中的基因ID数: {len(extra_genes):,}")
        
        # 保存不匹配的基因ID
        if missing_genes:
            print(f"\n处理在文件1中但不在文件2中的基因ID...")
            
            # 获取这些基因的完整信息
            missing_df = df1[df1['ensembl_gene_id'].isin(missing_genes)].copy()
            missing_df = missing_df.sort_values('ensembl_gene_id')
            
            # 保存结果
            output_file = f"process_gene/genes_missing_in_file2_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            missing_df.to_csv(output_file, index=False)
            
            print(f"✅ 已保存不匹配的基因ID到: {output_file}")
            print(f"✅ 不匹配基因数量: {len(missing_genes):,}")
            
            # 显示前20个不匹配的基因
            print(f"\n前20个不匹配的基因ID:")
            for i, (_, row) in enumerate(missing_df.head(20).iterrows(), 1):
                print(f"  {i:2d}. {row['ensembl_gene_id']} - {row['external_gene_name']}")
            
            if len(missing_genes) > 20:
                print(f"  ... 还有 {len(missing_genes) - 20} 个基因")
        else:
            print(f"\n✅ 所有文件1中的基因ID都在文件2中找到了!")
        
        # 保存额外的基因ID（可选）
        if extra_genes:
            print(f"\n处理只在文件2中的基因ID...")
            extra_df = df2[df2['GENE ID'].isin(extra_genes)][['GENE ID', 'GENE symbol']].copy()
            extra_df = extra_df.sort_values('GENE ID')
            extra_df.columns = ['ensembl_gene_id', 'external_gene_name']
            
            output_file2 = f"process_gene/genes_only_in_file2_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            extra_df.to_csv(output_file2, index=False)
            
            print(f"✅ 已保存只在文件2中的基因ID到: {output_file2}")
            print(f"✅ 额外基因数量: {len(extra_genes):,}")
        
        # 统计摘要
        print(f"\n" + "=" * 80)
        print("统计摘要:")
        print(f"  文件1总基因数: {len(genes_in_file1):,}")
        print(f"  文件2总基因数: {len(genes_in_file2):,}")
        print(f"  共同基因数: {len(common_genes):,}")
        print(f"  文件1独有基因数: {len(missing_genes):,}")
        print(f"  文件2独有基因数: {len(extra_genes):,}")
        print(f"  匹配率: {len(common_genes)/len(genes_in_file1)*100:.2f}%")
        print("=" * 80)
        
        return missing_genes, extra_genes, common_genes
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None, None, None

def main():
    """主函数"""
    missing_genes, extra_genes, common_genes = compare_gene_ids()
    
    if missing_genes is not None:
        print(f"\n✅ 分析完成!")
        if missing_genes:
            print(f"⚠️  发现 {len(missing_genes)} 个基因ID在文件2中缺失")
        else:
            print(f"✅ 所有基因ID都匹配!")
    else:
        print(f"\n❌ 分析失败!")

if __name__ == "__main__":
    main()

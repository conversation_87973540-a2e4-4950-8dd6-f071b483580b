import pandas as pd

# 读取文件
df = pd.read_csv('process_gene/GO_annotation_final_processed_with_project_ids.csv')

print(f"原始文件行数: {len(df)}")
print(f"唯一GO_Term数量: {df['GO_Term'].nunique()}")

# 按GO_Term分组，计算translation_project_ids的并集
def merge_project_ids(project_ids_series):
    """合并多个项目ID字符串，去重并排序"""
    all_ids = set()
    
    for ids_str in project_ids_series:
        if pd.notna(ids_str):  # 检查不是NaN
            # 分割项目ID并添加到集合中
            ids = ids_str.split(';')
            all_ids.update(ids)
    
    # 排序并合并
    sorted_ids = sorted(all_ids)
    return ';'.join(sorted_ids)

print("正在按GO_Term分组并计算并集...")

# 按GO_Term分组并应用合并函数
result_df = df.groupby('GO_Term')['translation_project_ids'].apply(merge_project_ids).reset_index()

print(f"分组后唯一GO_Term数量: {len(result_df)}")

# 保存结果
output_file = 'process_gene/GO_Term_with_merged_project_ids.csv'
result_df.to_csv(output_file, index=False)

print(f"结果已保存到: {output_file}")

# 显示前几行结果
print("\n前5行结果:")
print(result_df.head())
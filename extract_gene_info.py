#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys

def extract_unique_gene_info():
    """
    从translation_indices_results_grouped.csv文件中提取geneId的唯一值及其对应的geneSymbol
    """
    
    # 文件路径
    input_file = 'process_transcript/translation_indices_results_grouped.csv'
    output_file = 'unique_gene_info.csv'
    
    try:
        print(f"正在读取文件: {input_file}")
        
        # 检查文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 文件 {input_file} 不存在")
            return False
            
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"文件读取成功! 数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(df.head())
        
        # 检查是否包含geneId和geneSymbol列
        required_columns = ['geneId', 'geneSymbol']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: 缺少必需的列: {missing_columns}")
            print("可用的列:", list(df.columns))
            return False
        
        # 提取geneId和geneSymbol的唯一组合
        print("\n正在提取唯一的geneId和geneSymbol组合...")
        
        # 去除空值
        df_clean = df[['geneId', 'geneSymbol']].dropna()
        
        # 获取唯一组合
        unique_genes = df_clean.drop_duplicates()
        
        print(f"原始数据行数: {len(df)}")
        print(f"去除空值后行数: {len(df_clean)}")
        print(f"唯一基因组合数: {len(unique_genes)}")
        
        # 检查是否有一个geneId对应多个geneSymbol的情况
        gene_symbol_counts = df_clean.groupby('geneId')['geneSymbol'].nunique()
        multiple_symbols = gene_symbol_counts[gene_symbol_counts > 1]
        
        if len(multiple_symbols) > 0:
            print(f"\n警告: 发现 {len(multiple_symbols)} 个geneId对应多个geneSymbol:")
            print(multiple_symbols.head(10))
            
            # 对于有多个symbol的geneId，选择第一个出现的symbol
            unique_genes = df_clean.groupby('geneId').first().reset_index()
            print(f"处理后的唯一基因数: {len(unique_genes)}")
        
        # 按geneId排序
        unique_genes = unique_genes.sort_values('geneId')
        
        # 保存结果
        unique_genes.to_csv(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 显示结果统计
        print(f"\n结果统计:")
        print(f"- 总共提取了 {len(unique_genes)} 个唯一基因")
        print(f"- geneId范围: {unique_genes['geneId'].min()} - {unique_genes['geneId'].max()}")
        
        # 显示前10行结果
        print(f"\n前10行结果:")
        print(unique_genes.head(10))
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = extract_unique_gene_info()
    if success:
        print("\n处理完成!")
    else:
        print("\n处理失败!")
        sys.exit(1)

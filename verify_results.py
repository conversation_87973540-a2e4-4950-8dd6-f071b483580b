#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_results():
    """
    验证处理结果
    """
    
    print("验证处理结果...")
    
    # 读取结果文件
    try:
        df = pd.read_csv('process_gene/unique_genes_with_project_info.csv')
        print(f"成功读取结果文件，共{len(df)}行")
        print("列名:", df.columns.tolist())
    except Exception as e:
        print(f"读取结果文件失败: {e}")
        return
    
    # 显示前几行的完整信息
    print("\n前3行完整信息:")
    for idx in range(min(3, len(df))):
        row = df.iloc[idx]
        print(f"\n第{idx+1}行:")
        print(f"  基因ID: {row['ensembl_gene_id']}")
        print(f"  基因名: {row['external_gene_name']}")
        print(f"  Tissue/Cell Type: {row['Tissue/Cell Type']}")
        print(f"  Cell line: {row['Cell line']}")
        print(f"  Disease: {row['Disease']}")
    
    # 统计信息
    print(f"\n统计信息:")
    print(f"总基因数: {len(df)}")
    
    # 检查有多少基因有非空的信息
    tissue_non_empty = len(df[df['Tissue/Cell Type'] != '{}'])
    cell_line_non_empty = len(df[df['Cell line'] != '{}'])
    disease_non_empty = len(df[df['Disease'] != '{}'])
    
    print(f"有Tissue/Cell Type信息的基因数: {tissue_non_empty}")
    print(f"有Cell line信息的基因数: {cell_line_non_empty}")
    print(f"有Disease信息的基因数: {disease_non_empty}")
    
    # 显示一些具体的例子
    print(f"\n一些具体的Tissue/Cell Type例子:")
    unique_tissues = set()
    for idx, row in df.iterrows():
        tissue_str = row['Tissue/Cell Type']
        if tissue_str != '{}':
            # 提取花括号内的内容
            tissue_content = tissue_str.strip('{}')
            if tissue_content:
                tissues = [t.strip() for t in tissue_content.split(',')]
                unique_tissues.update(tissues)
        if len(unique_tissues) >= 20:  # 只显示前20个
            break
    
    print("唯一的Tissue/Cell Type值（前20个）:")
    for tissue in sorted(list(unique_tissues)[:20]):
        print(f"  - {tissue}")
    
    print("\n验证完成！")

if __name__ == "__main__":
    verify_results()

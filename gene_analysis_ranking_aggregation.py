import pandas as pd
import numpy as np
from scipy.stats import zscore
import warnings
warnings.filterwarnings('ignore')

# 疾病大类与condition的映射关系
DISEASE_CATEGORIES = {
    "Infectious Disease": [
        "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
        "Adult Hepatocellular Carcinoma; HCV Transfection",
        "B95-8 Epstein-Barr Virus Infection",
        "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
        "Human Cytomegalovirus Infection",
        "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
        "IAV Transfection",
        "Lung Adenocarcinoma; IAV Infection",
        "Lung Adenocarcinoma; SARS-CoV-2 Infection",
        "M81 Epstein-Barr Virus Infection",
        "Toxoplasma Infection"
    ],
    "Gastrointestinal System Cancer": [
        "Adult Hepatocellular Carcinoma",
        "Childhood Hepatocellular Carcinoma",
        "Colon Adenocarcinoma",
        "Colon Carcinoma",
        "Esophageal Squamous Cell Carcinoma",
        "Hepatoblastoma",
        "Hepatocellular Carcinoma",
        "Intrahepatic Cholangiocarcinoma"
    ],
    "Musculoskeletal System Cancer": [
        "Osteosarcoma"
    ],
    "Reproductive Organ Cancer": [
        "Human Papillomavirus-related Endocervical Adenocarcinoma",
        "Ovarian Cancer",
        "Ovarian Endometrioid Carcinoma",
        "Prostate Cancer",
        "Prostate Carcinoma"
    ],
    "Respiratory System Cancer": [
        "Lung Adenocarcinoma",
        "Lung Large Cell Carcinoma"
    ],
    "Urinary System Cancer": [
        "Kidney Rhabdoid Cancer",
        "Kidney Tumor",
        "Renal Cell Carcinoma"
    ],
    "Genetic Disease": [
        "Duchenne Muscular Dystrophy",
        "Hbs1L Deficiency",
        "Roberts Syndrome",
        "Treacher Collins Syndrome",
        "Tuberous Sclerosis Complex"
    ],
    "Other": [
        "Cancer-derived"
    ],
    "Nervous System Cancer": [
        "Astrocytoma",
        "Brain Glioma",
        "Neuroblastoma"
    ],
    "Hematologic Cancer": [
        "Acute Myeloid Leukemia",
        "Adult Acute Myeloid Leukemia",
        "Childhood T Acute Lymphoblastic Leukemia",
        "Childhood T Lymphoblastic Lymphoma",
        "Chronic Myeloid Leukemia",
        "Multiple Myeloma"
    ],
    "Breast Cancer": [
        "Breast Adenocarcinoma",
        "Breast Carcinoma"
    ],
    "Head And Neck Cancer": [
        "Head And Neck Squamous Cell Carcinoma",
        "Tongue Squamous Cell Carcinoma"
    ],
    "Endocrine Gland Cancer": [
        "Pancreatic Adenocarcinoma"
    ]
}

def create_condition_mapping():
    """创建condition到disease category的映射字典"""
    mapping = {}
    for category, conditions in DISEASE_CATEGORIES.items():
        for condition in conditions:
            mapping[condition] = category
    return mapping

def calculate_ranking_scores(pivot_zscore, cols, method='improved_borda'):
    """
    计算排名聚合分数，处理缺失值问题
    
    Parameters:
    pivot_zscore: DataFrame with transcript as index and conditions as columns
    cols: list of column names to consider
    method: str, aggregation method ('standard_borda', 'improved_borda', 'weighted')
    
    Returns:
    dict: transcript_id -> score
    """
    
    if method == 'standard_borda':
        return _standard_borda(pivot_zscore, cols)
    elif method == 'improved_borda':
        return _improved_borda(pivot_zscore, cols)
    elif method == 'weighted':
        return _weighted_ranking(pivot_zscore, cols)
    else:
        raise ValueError(f"Unknown method: {method}")

def _standard_borda(pivot_zscore, cols):
    """标准Borda Count方法"""
    transcript_scores = {}
    n_transcripts = len(pivot_zscore)
    
    # 对每个条件单独排名
    condition_rankings = {}
    for col in cols:
        if col in pivot_zscore.columns:
            ranks = pivot_zscore[col].rank(method='dense', ascending=False, na_option='keep')
            condition_rankings[col] = ranks
    
    # 计算Borda分数
    for transcript in pivot_zscore.index:
        borda_score = 0
        valid_conditions = 0
        
        for col in cols:
            if col in condition_rankings and not pd.isna(pivot_zscore.loc[transcript, col]):
                rank = condition_rankings[col][transcript]
                borda_score += (n_transcripts - rank + 1)
                valid_conditions += 1
        
        # 标准化分数
        if valid_conditions > 0:
            transcript_scores[transcript] = borda_score / valid_conditions
        else:
            transcript_scores[transcript] = 0
    
    return transcript_scores

def _improved_borda(pivot_zscore, cols):
    """改进的Borda Count方法，处理缺失值"""
    transcript_scores = {}
    
    # 对每个条件单独排名（只对有效数据排名）
    condition_rankings = {}
    condition_valid_counts = {}
    
    for col in cols:
        if col in pivot_zscore.columns:
            valid_data = pivot_zscore[col].dropna()
            if len(valid_data) > 0:
                # 只对有效数据排名
                ranks = valid_data.rank(method='dense', ascending=False)
                condition_rankings[col] = ranks
                condition_valid_counts[col] = len(valid_data)
    
    # 计算改进的Borda分数
    for transcript in pivot_zscore.index:
        borda_score = 0
        valid_conditions = 0
        
        for col in cols:
            if (col in condition_rankings and 
                transcript in condition_rankings[col].index and 
                not pd.isna(pivot_zscore.loc[transcript, col])):
                
                rank = condition_rankings[col][transcript]
                n_valid_in_condition = condition_valid_counts[col]
                
                # 标准化到[0,1]区间
                normalized_score = (n_valid_in_condition - rank + 1) / n_valid_in_condition
                borda_score += normalized_score
                valid_conditions += 1
        
        # 计算平均分和覆盖度权重
        if valid_conditions > 0:
            coverage_weight = valid_conditions / len(cols)  # 覆盖度权重
            avg_score = borda_score / valid_conditions
            # 综合考虑得分和覆盖度
            transcript_scores[transcript] = avg_score * (0.8 + 0.2 * coverage_weight)
        else:
            transcript_scores[transcript] = 0
    
    return transcript_scores

def _weighted_ranking(pivot_zscore, cols):
    """加权排名方法，根据数据质量加权"""
    transcript_scores = {}
    
    # 计算每个条件的权重（基于非空值数量）
    condition_weights = {}
    total_weight = 0
    
    for col in cols:
        if col in pivot_zscore.columns:
            weight = pivot_zscore[col].notna().sum()
            condition_weights[col] = weight
            total_weight += weight
    
    # 标准化权重
    if total_weight > 0:
        for col in condition_weights:
            condition_weights[col] /= total_weight
    
    # 对每个条件排名
    condition_rankings = {}
    condition_valid_counts = {}
    
    for col in cols:
        if col in pivot_zscore.columns:
            valid_data = pivot_zscore[col].dropna()
            if len(valid_data) > 0:
                ranks = valid_data.rank(method='dense', ascending=False)
                condition_rankings[col] = ranks
                condition_valid_counts[col] = len(valid_data)
    
    # 计算加权分数
    for transcript in pivot_zscore.index:
        weighted_score = 0
        total_weight_used = 0
        
        for col in cols:
            if (col in condition_rankings and 
                transcript in condition_rankings[col].index and 
                not pd.isna(pivot_zscore.loc[transcript, col])):
                
                rank = condition_rankings[col][transcript]
                n_valid_in_condition = condition_valid_counts[col]
                weight = condition_weights.get(col, 0)
                
                # 标准化分数
                normalized_score = (n_valid_in_condition - rank + 1) / n_valid_in_condition
                weighted_score += normalized_score * weight
                total_weight_used += weight
        
        # 计算最终分数
        if total_weight_used > 0:
            transcript_scores[transcript] = weighted_score / total_weight_used
        else:
            transcript_scores[transcript] = 0
    
    return transcript_scores

def process_metric_batch_ranking(df, metric, condition_mapping, method='improved_borda'):
    """使用排名聚合方法批量处理单个指标的所有基因"""
    print(f"处理 {metric.upper()} 数据（排名聚合方法: {method}）...")
    
    # 过滤有效数据
    metric_data = df[df[metric].notna()].copy()
    if metric_data.empty:
        print(f"没有找到 {metric.upper()} 数据")
        return [], []
    
    print(f"{metric.upper()} 有效数据: {len(metric_data)} 条，涉及 {metric_data['geneId'].nunique()} 个基因")
    
    # 添加疾病类别映射
    metric_data['DISEASE_CATEGORY'] = metric_data['Condition'].map(condition_mapping).fillna('Unknown')
    
    all_condition_results = []
    all_disease_results = []
    
    # 按基因分组批量处理
    gene_groups = metric_data.groupby('geneId')
    total_genes = len(gene_groups)
    
    for i, (gene_id, gene_data) in enumerate(gene_groups, 1):
        if i % 1000 == 0:
            print(f"  处理进度: {i}/{total_genes} 基因")
        
        gene_symbol = gene_data['geneSymbol'].iloc[0]
        
        try:
            # 创建透视表
            pivot_data = gene_data.pivot_table(
                index='transcriptId',
                columns='CONDITION_DATASET_ID',
                values=metric,
                aggfunc='mean'
            )
            
            # 移除全NaN的行和列
            pivot_data = pivot_data.dropna(axis=0, how='all').dropna(axis=1, how='all')
            
            if pivot_data.empty:
                continue
            
            # z-score标准化（向量化操作）
            pivot_zscore = pivot_data.apply(lambda x: (x - x.mean()) / x.std() if x.std() > 0 else x * 0, axis=0)
            pivot_zscore = pivot_zscore.fillna(0)
            
            # 预计算condition和category的列映射
            condition_col_map = {}
            category_col_map = {}
            
            for condition in gene_data['Condition'].unique():
                cols = [col for col in pivot_zscore.columns if col.startswith(condition + '_')]
                if cols:
                    condition_col_map[condition] = cols
            
            for category in gene_data['DISEASE_CATEGORY'].unique():
                category_conditions = gene_data[gene_data['DISEASE_CATEGORY'] == category]['Condition'].unique()
                cols = []
                for cond in category_conditions:
                    cols.extend([col for col in pivot_zscore.columns if col.startswith(cond + '_')])
                if cols:
                    category_col_map[category] = cols
            
            # 批量处理conditions
            for condition, cols in condition_col_map.items():
                condition_data = gene_data[gene_data['Condition'] == condition]
                
                # 使用排名聚合方法
                transcript_scores = calculate_ranking_scores(pivot_zscore, cols, method)
                
                if transcript_scores:
                    max_transcript = max(transcript_scores.keys(), key=lambda x: transcript_scores[x])
                    max_ranking_score = transcript_scores[max_transcript]
                    
                    # 计算覆盖度
                    coverage = pivot_zscore.loc[max_transcript, cols].notna().sum() / len(cols)
                    
                    # 获取原始表达值
                    original_expr = condition_data[condition_data['transcriptId'] == max_transcript][metric].sum()
                    
                    all_condition_results.append({
                        'GENE_ID': gene_id,
                        'GENE_SYMBOL': gene_symbol,
                        'CONDITION': condition,
                        'CONDITION_MAX_TRANSCRIPT_ID': max_transcript,
                        'CONDITION_MAX_EXPRESSION': original_expr,
                        'CONDITION_RANKING_SCORE': max_ranking_score,
                        'CONDITION_COVERAGE': coverage,
                        'DISEASE_CATEGORY': condition_data['DISEASE_CATEGORY'].iloc[0],
                        'CONDITION_DATASET_COUNT': condition_data['projectId'].nunique(),
                        'TOTAL_TRANSCRIPTS_IN_CONDITION': condition_data['transcriptId'].nunique(),
                        'METRIC_TYPE': metric.upper(),
                        'RANKING_METHOD': method
                    })
            
            # 批量处理disease categories
            for category, cols in category_col_map.items():
                # 使用排名聚合方法
                transcript_scores = calculate_ranking_scores(pivot_zscore, cols, method)
                
                if transcript_scores:
                    max_transcript = max(transcript_scores.keys(), key=lambda x: transcript_scores[x])
                    max_ranking_score = transcript_scores[max_transcript]
                    
                    # 计算覆盖度
                    coverage = pivot_zscore.loc[max_transcript, cols].notna().sum() / len(cols)
                    
                    # 获取原始表达值
                    category_data = gene_data[gene_data['DISEASE_CATEGORY'] == category]
                    original_expr = category_data[category_data['transcriptId'] == max_transcript][metric].sum()
                    
                    all_disease_results.append({
                        'GENE_ID': gene_id,
                        'GENE_SYMBOL': gene_symbol,
                        'DISEASE_CATEGORY': category,
                        'CATEGORY_MAX_TRANSCRIPT_ID': max_transcript,
                        'CATEGORY_MAX_EXPRESSION': original_expr,
                        'CATEGORY_RANKING_SCORE': max_ranking_score,
                        'CATEGORY_COVERAGE': coverage,
                        'CONDITION_COUNT_IN_CATEGORY': category_data['Condition'].nunique(),
                        'TOTAL_TRANSCRIPTS_IN_CATEGORY': category_data['transcriptId'].nunique(),
                        'METRIC_TYPE': metric.upper(),
                        'RANKING_METHOD': method
                    })
                    
        except Exception as e:
            # 跳过有问题的基因
            continue
    
    print(f"{metric.upper()} 处理完成: {len(all_condition_results)} 条condition记录, {len(all_disease_results)} 条disease记录")
    return all_condition_results, all_disease_results

def process_gene_data_ranking(gene_ids, method='improved_borda'):
    """使用排名聚合方法处理基因数据"""
    
    print(f"正在读取translation indices数据...")
    print(f"使用排名聚合方法: {method}")
    
    # 使用更大的chunk size和并行读取
    chunk_size = 100000
    filtered_data = []
    gene_set = set(gene_ids)
    
    for chunk in pd.read_csv('process_transcript/translation_indices_results_grouped.csv', chunksize=chunk_size):
        gene_chunk = chunk[chunk['geneId'].isin(gene_set)]
        if not gene_chunk.empty:
            filtered_data.append(gene_chunk)
    
    if not filtered_data:
        print("未找到匹配的基因数据")
        return
    
    df = pd.concat(filtered_data, ignore_index=True)
    print(f"找到 {len(df)} 条基因数据记录，涉及 {df['geneId'].nunique()} 个基因")
    
    # 预处理
    condition_mapping = create_condition_mapping()
    df['CONDITION_DATASET_ID'] = df['Condition'] + '_' + df['projectId']
    
    # 批量转换数值类型
    for col in ['te', 'tr', 'evi']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 分别处理三种指标
    all_condition_results = []
    all_disease_results = []
    
    for metric in ['te', 'tr', 'evi']:
        condition_results, disease_results = process_metric_batch_ranking(df, metric, condition_mapping, method)
        all_condition_results.extend(condition_results)
        all_disease_results.extend(disease_results)
    
    # 保存结果
    method_suffix = method.replace('_', '')
    
    if all_condition_results:
        condition_df = pd.DataFrame(all_condition_results)
        condition_df = condition_df.sort_values(['GENE_ID', 'METRIC_TYPE', 'DISEASE_CATEGORY', 'CONDITION'])
        filename = f'gene_analysis_results_by_condition_{method_suffix}.csv'
        condition_df.to_csv(filename, index=False, encoding='utf-8')
        
        print(f"\n按CONDITION分组的结果已保存到 '{filename}'")
        print(f"CONDITION文件: {len(condition_df)} 条记录，涉及 {condition_df['GENE_ID'].nunique()} 个基因")
    
    if all_disease_results:
        disease_df = pd.DataFrame(all_disease_results)
        disease_df = disease_df.sort_values(['GENE_ID', 'METRIC_TYPE', 'DISEASE_CATEGORY'])
        filename = f'gene_analysis_results_by_disease_category_{method_suffix}.csv'
        disease_df.to_csv(filename, index=False, encoding='utf-8')
        
        print(f"按DISEASE_CATEGORY分组的结果已保存到 '{filename}'")
        print(f"DISEASE_CATEGORY文件: {len(disease_df)} 条记录，涉及 {disease_df['GENE_ID'].nunique()} 个基因")
    
    # 统计信息
    if all_condition_results:
        stats_df = pd.DataFrame(all_condition_results)
        print(f"\n各指标类型统计:")
        metric_stats = stats_df.groupby('METRIC_TYPE').agg({
            'GENE_ID': 'nunique',
            'CONDITION': 'count',
            'CONDITION_MAX_EXPRESSION': 'mean',
            'CONDITION_RANKING_SCORE': 'mean',
            'CONDITION_COVERAGE': 'mean'
        }).round(3)
        metric_stats.columns = ['Unique_Genes', 'Total_Records', 'Avg_Max_Expression', 'Avg_Ranking_Score', 'Avg_Coverage']
        print(metric_stats)
        
        print(f"\n结果预览:")
        preview_cols = ['GENE_ID', 'GENE_SYMBOL', 'CONDITION', 'CONDITION_MAX_TRANSCRIPT_ID', 
                       'CONDITION_MAX_EXPRESSION', 'CONDITION_RANKING_SCORE', 'CONDITION_COVERAGE', 'METRIC_TYPE']
        print(condition_df.head(5)[preview_cols].to_string(index=False))
    
    print(f"\n所有分析完成！")

def main():
    """主函数，支持多种排名聚合方法"""
    # 读取基因信息
    print("正在读取基因信息...")
    gene_info = pd.read_csv('hotmap/unique_gene_info.csv')
    gene_ids = gene_info['geneId'].tolist()
    
    print(f"共找到 {len(gene_ids)} 个基因")
    
    # 可选的排名聚合方法
    methods = ['standard_borda', 'weighted']
    
    # 默认使用改进的Borda Count方法
    method = 'standard_borda'
    
    print(f"\n=== 使用 {method} 方法 ===")
    process_gene_data_ranking(gene_ids, method)
    
    # 如果需要比较多种方法，可以取消注释以下代码
    for method in methods:
        print(f"\n=== 使用 {method} 方法 ===")
        process_gene_data_ranking(gene_ids, method)

if __name__ == "__main__":
    main()
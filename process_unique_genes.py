#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from collections import defaultdict
import sys
import os

def process_unique_genes():
    """
    处理process_gene/unique_genes.csv文件，根据ensembl_gene_id查询两个文件，
    获取project_id的唯一值，并比较两个文件的结果是否一致
    """

    print("开始处理unique_genes.csv文件...")

    # 读取unique_genes.csv文件
    try:
        unique_genes_df = pd.read_csv('process_gene/unique_genes.csv')
        print(f"成功读取unique_genes.csv，共{len(unique_genes_df)}行")
        print("列名:", unique_genes_df.columns.tolist())
        print("前5行数据:")
        print(unique_genes_df.head())
    except Exception as e:
        print(f"读取unique_genes.csv失败: {e}")
        return

    # 读取translation_indices_results_grouped_cleaned.csv文件
    try:
        print("正在读取translation_indices文件...")
        translation_df = pd.read_csv('process_transcript/translation_indices_results_grouped_cleaned.csv',
                                   dtype={'projectId': str, 'geneId': str}, low_memory=False)
        print(f"成功读取translation_indices_results_grouped_cleaned.csv，共{len(translation_df)}行")
        print("列名:", translation_df.columns.tolist())
        print("前5行数据:")
        print(translation_df.head())
    except Exception as e:
        print(f"读取translation_indices_results_grouped_cleaned.csv失败: {e}")
        return

    # 读取gene_count_by_project_results_cleaned.csv文件
    try:
        print("正在读取gene_count文件...")
        gene_count_df = pd.read_csv('process_gene/gene_count_by_project_results_cleaned.csv',
                                  dtype={'project_id': str, 'ensembl_gene_id': str}, low_memory=False)
        print(f"成功读取gene_count_by_project_results_cleaned.csv，共{len(gene_count_df)}行")
        print("列名:", gene_count_df.columns.tolist())
        print("前5行数据:")
        print(gene_count_df.head())
    except Exception as e:
        print(f"读取gene_count_by_project_results_cleaned.csv失败: {e}")
        return

    print("\n预处理数据，创建查找字典...")

    # 创建translation_df的查找字典，提高查询效率
    translation_dict = defaultdict(set)
    for _, row in translation_df.iterrows():
        gene_id = row['geneId']
        project_id = row['projectId']
        if pd.notna(gene_id) and pd.notna(project_id):
            translation_dict[gene_id].add(project_id)

    # 创建gene_count_df的查找字典
    gene_count_dict = defaultdict(set)
    for _, row in gene_count_df.iterrows():
        gene_id = row['ensembl_gene_id']
        project_id = row['project_id']
        if pd.notna(gene_id) and pd.notna(project_id):
            gene_count_dict[gene_id].add(project_id)

    print(f"translation_dict包含{len(translation_dict)}个基因")
    print(f"gene_count_dict包含{len(gene_count_dict)}个基因")

    # 创建结果DataFrame
    result_df = unique_genes_df.copy()

    # 初始化新列
    result_df['translation_project_ids'] = ''
    result_df['gene_count_project_ids'] = ''
    result_df['project_ids_match'] = False

    print("\n开始处理每个基因...")

    # 处理每个基因
    for idx, row in unique_genes_df.iterrows():
        ensembl_gene_id = row['ensembl_gene_id']

        if idx % 1000 == 0:
            print(f"处理进度: {idx}/{len(unique_genes_df)}")

        # 从字典中获取project_ids
        translation_project_ids = translation_dict.get(ensembl_gene_id, set())
        gene_count_project_ids = gene_count_dict.get(ensembl_gene_id, set())

        # 将集合转换为排序的字符串
        translation_ids_str = ';'.join(sorted(translation_project_ids)) if translation_project_ids else ''
        gene_count_ids_str = ';'.join(sorted(gene_count_project_ids)) if gene_count_project_ids else ''

        # 比较两个集合是否相等
        ids_match = translation_project_ids == gene_count_project_ids

        # 更新结果DataFrame
        result_df.at[idx, 'translation_project_ids'] = translation_ids_str
        result_df.at[idx, 'gene_count_project_ids'] = gene_count_ids_str
        result_df.at[idx, 'project_ids_match'] = ids_match
    
    # 保存结果
    output_file = 'process_gene/unique_genes_with_project_ids.csv'
    result_df.to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    # 统计信息
    total_genes = len(result_df)
    genes_with_translation_data = len(result_df[result_df['translation_project_ids'] != ''])
    genes_with_gene_count_data = len(result_df[result_df['gene_count_project_ids'] != ''])
    genes_with_matching_ids = len(result_df[result_df['project_ids_match'] == True])
    
    print(f"\n统计信息:")
    print(f"总基因数: {total_genes}")
    print(f"在translation文件中找到数据的基因数: {genes_with_translation_data}")
    print(f"在gene_count文件中找到数据的基因数: {genes_with_gene_count_data}")
    print(f"两个文件project_id完全匹配的基因数: {genes_with_matching_ids}")
    print(f"匹配率: {genes_with_matching_ids/total_genes*100:.2f}%")
    
    # 显示一些不匹配的例子
    mismatched = result_df[result_df['project_ids_match'] == False]
    if not mismatched.empty:
        print(f"\n不匹配的基因示例（前10个）:")
        print(mismatched[['ensembl_gene_id', 'external_gene_name', 'translation_project_ids', 'gene_count_project_ids']].head(10))
    
    print("\n处理完成！")

if __name__ == "__main__":
    process_unique_genes()

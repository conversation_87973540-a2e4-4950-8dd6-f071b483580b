#!/usr/bin/env python3
"""
测试CSV文件读取
"""
import pandas as pd
import os

def test_csv_reading():
    """测试CSV文件读取"""
    
    input_file = "process_gene/missing_genes.csv"
    
    print("测试CSV文件读取...")
    print(f"输入文件: {input_file}")
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return False
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"✅ 成功读取CSV文件")
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        print(f"   列名: {list(df.columns)}")
        
        # 显示前几行
        print(f"\n前5行数据:")
        print(df.head().to_string(index=False))
        
        # 检查基因ID格式
        gene_ids = df['ensembl_gene_id'].tolist()
        print(f"\n基因ID格式检查:")
        print(f"   第一个基因ID: {gene_ids[0]}")
        print(f"   最后一个基因ID: {gene_ids[-1]}")
        
        # 检查是否有空值
        null_counts = df.isnull().sum()
        print(f"\n空值检查:")
        for col, count in null_counts.items():
            print(f"   {col}: {count} 个空值")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return False

if __name__ == "__main__":
    test_csv_reading()

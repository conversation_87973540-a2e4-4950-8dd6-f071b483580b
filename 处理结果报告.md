# 基因信息提取处理报告

## 处理概述
成功从 `process_transcript/translation_indices_results_grouped.csv` 文件中提取了geneId的唯一值及其对应的geneSymbol。

## 输入文件信息
- **文件路径**: `process_transcript/translation_indices_results_grouped.csv`
- **文件大小**: 12,935,373 行数据
- **列数**: 11列
- **包含列**: transcriptId, projectId, bioprojectId, tissueCellType, cellLine, Condition, tr, evi, te, geneId, geneSymbol

## 处理结果
- **输出文件**: `unique_gene_info.csv`
- **提取的唯一基因数量**: 25,887个
- **输出格式**: CSV文件，包含两列：geneId 和 geneSymbol

## 输出文件结构
```csv
geneId,geneSymbol
ENSG00000000003,TSPAN6
ENSG00000000005,TNMD
ENSG00000000419,DPM1
...
```

## 数据示例
### 前10行数据：
| geneId | geneSymbol |
|--------|------------|
| ENSG00000000003 | TSPAN6 |
| ENSG00000000005 | TNMD |
| ENSG00000000419 | DPM1 |
| ENSG00000000457 | SCYL3 |
| ENSG00000000460 | FIRRM |
| ENSG00000000938 | FGR |
| ENSG00000000971 | CFH |
| ENSG00000001036 | FUCA2 |
| ENSG00000001084 | GCLC |
| ENSG00000001167 | NFYA |

### 最后几行数据：
| geneId | geneSymbol |
|--------|------------|
| ENSG00000293542 | DUSP13B |
| ENSG00000293543 | DUSP13A |
| ENSG00000293546 | ALG1L9P |
| ENSG00000293616 | PCBP1-AS1 |
| ENSG00000293621 | UVRAG-DT |
| ENSG00000293697 | ANKRD20A5P |
| ENSG00000310517 | CAST |
| ENSG00000310560 | PAXX |
| ENSG00000310576 | FAM174C |

## 处理说明
1. 从原始数据中提取了 `geneId` 和 `geneSymbol` 两列
2. 去除了包含空值的行
3. 去除了重复的基因ID-基因符号组合
4. 按照 geneId 进行了排序
5. 输出为标准的CSV格式文件

## 文件位置
- **输出文件**: `/Volumes/zhy/整合的所有TPM文件/unique_gene_info.csv`
- **处理日志**: `/Volumes/zhy/整合的所有TPM文件/extract_log.txt`

处理完成时间: 2025年7月30日

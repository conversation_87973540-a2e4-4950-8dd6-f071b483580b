#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将ensembl_gene_id_with_full_info_split_2.csv文件中不存在于
ensembl_gene_id_with_full_info_2.csv文件中的基因ID行添加到第一个文件中
"""

import pandas as pd
import os
import shutil
from datetime import datetime

def merge_missing_genes():
    """合并缺失的基因"""
    
    # 文件路径
    target_file = "process_gene/ensembl_gene_id_with_full_info_split_2.csv"  # 目标文件（要添加到的文件）
    source_file = "process_gene/ensembl_gene_id_with_full_info_2.csv"  # 源文件（提供数据的文件）
    backup_file = f"process_gene/ensembl_gene_id_with_full_info_2_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    try:
        print("=" * 80)
        print("合并缺失基因到目标文件")
        print("=" * 80)
        
        # 检查文件是否存在
        if not os.path.exists(target_file):
            raise FileNotFoundError(f"目标文件不存在: {target_file}")
        if not os.path.exists(source_file):
            raise FileNotFoundError(f"源文件不存在: {source_file}")
        
        print(f"目标文件: {target_file}")
        print(f"源文件: {source_file}")
        print(f"备份文件: {backup_file}")
        
        # 创建备份
        print(f"\n创建备份文件...")
        shutil.copy2(target_file, backup_file)
        print(f"✅ 备份已创建: {backup_file}")
        
        # 读取目标文件
        print(f"\n读取目标文件...")
        df_target = pd.read_csv(target_file)
        print(f"目标文件信息:")
        print(f"  行数: {len(df_target):,}")
        print(f"  列数: {len(df_target.columns)}")
        print(f"  列名: {list(df_target.columns)}")
        
        # 读取源文件
        print(f"\n读取源文件...")
        df_source = pd.read_csv(source_file)
        print(f"源文件信息:")
        print(f"  行数: {len(df_source):,}")
        print(f"  列数: {len(df_source.columns)}")
        print(f"  列名: {list(df_source.columns)}")
        
        # 检查列结构是否匹配
        target_cols = set(df_target.columns)
        source_cols = set(df_source.columns)
        
        if target_cols != source_cols:
            print(f"\n⚠️  警告：列结构不完全匹配")
            print(f"  目标文件独有列: {target_cols - source_cols}")
            print(f"  源文件独有列: {source_cols - target_cols}")
            
            # 确保两个文件都有ensembl_gene_id列
            if 'ensembl_gene_id' not in target_cols or 'ensembl_gene_id' not in source_cols:
                raise ValueError("两个文件都必须包含 ensembl_gene_id 列")
            
            # 使用共同的列
            common_cols = list(target_cols & source_cols)
            print(f"  将使用共同列: {common_cols}")
            
            # 重新排列列顺序，确保ensembl_gene_id在第一位
            if 'ensembl_gene_id' in common_cols:
                common_cols.remove('ensembl_gene_id')
                common_cols = ['ensembl_gene_id'] + sorted(common_cols)
            
            df_target = df_target[common_cols]
            df_source = df_source[common_cols]
        
        # 提取基因ID集合
        target_genes = set(df_target['ensembl_gene_id'])
        source_genes = set(df_source['ensembl_gene_id'])
        
        print(f"\n基因ID分析:")
        print(f"  目标文件中的基因数: {len(target_genes):,}")
        print(f"  源文件中的基因数: {len(source_genes):,}")
        
        # 找出源文件中但不在目标文件中的基因
        missing_genes = source_genes - target_genes
        common_genes = target_genes & source_genes
        
        print(f"  共同基因数: {len(common_genes):,}")
        print(f"  需要添加的基因数: {len(missing_genes):,}")
        
        if not missing_genes:
            print(f"\n✅ 没有需要添加的基因，所有基因都已存在于目标文件中")
            return True
        
        # 显示一些将要添加的基因
        print(f"\n将要添加的基因 (前10个):")
        missing_list = sorted(list(missing_genes))
        for i, gene_id in enumerate(missing_list[:10]):
            gene_info = df_source[df_source['ensembl_gene_id'] == gene_id]
            if not gene_info.empty:
                gene_symbol = gene_info['GENE symbol'].iloc[0] if 'GENE symbol' in gene_info.columns else 'N/A'
                print(f"  {i+1:2d}. {gene_id} - {gene_symbol}")
        
        if len(missing_genes) > 10:
            print(f"  ... 还有 {len(missing_genes) - 10:,} 个基因")
        
        # 确认添加
        confirm = input(f"\n确认添加 {len(missing_genes):,} 个基因到目标文件吗? (y/N): ")
        if confirm.lower() != 'y':
            print("用户取消操作")
            return False
        
        # 提取需要添加的行
        print(f"\n提取需要添加的数据...")
        df_to_add = df_source[df_source['ensembl_gene_id'].isin(missing_genes)].copy()
        df_to_add = df_to_add.sort_values('ensembl_gene_id')
        
        print(f"提取到 {len(df_to_add):,} 行数据")
        
        # 合并数据
        print(f"\n合并数据...")
        df_merged = pd.concat([df_target, df_to_add], ignore_index=True)
        
        # 按基因ID排序
        df_merged = df_merged.sort_values('ensembl_gene_id')
        
        print(f"合并后文件信息:")
        print(f"  行数: {len(df_merged):,}")
        print(f"  列数: {len(df_merged.columns)}")
        
        # 验证合并结果
        final_genes = set(df_merged['ensembl_gene_id'])
        expected_genes = target_genes | source_genes
        
        if final_genes == expected_genes:
            print(f"✅ 合并验证通过")
        else:
            print(f"❌ 合并验证失败")
            print(f"  期望基因数: {len(expected_genes):,}")
            print(f"  实际基因数: {len(final_genes):,}")
        
        # 保存合并后的文件
        print(f"\n保存合并后的文件...")
        df_merged.to_csv(target_file, index=False)
        
        print(f"✅ 合并完成!")
        print(f"✅ 已更新文件: {target_file}")
        print(f"✅ 备份文件: {backup_file}")
        print(f"✅ 添加了 {len(missing_genes):,} 个基因")
        print(f"✅ 最终文件包含 {len(df_merged):,} 个基因")
        
        # 显示一些统计信息
        if 'transcript_count' in df_merged.columns:
            print(f"\n转录本统计:")
            transcript_stats = df_merged['transcript_count'].describe()
            print(f"  平均转录本数: {transcript_stats['mean']:.2f}")
            print(f"  最大转录本数: {int(transcript_stats['max'])}")
            print(f"  最小转录本数: {int(transcript_stats['min'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合并失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = merge_missing_genes()
    
    if success:
        print(f"\n" + "=" * 80)
        print("✅ 任务完成!")
        print("缺失的基因已成功添加到目标文件中")
    else:
        print(f"\n" + "=" * 80)
        print("❌ 任务失败!")

if __name__ == "__main__":
    main()

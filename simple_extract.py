#!/usr/bin/env python3
import pandas as pd
import sys

try:
    # 读取文件
    df = pd.read_csv('process_transcript/translation_indices_results_grouped.csv')
    
    # 写入日志
    with open('extract_log.txt', 'w') as f:
        f.write(f"文件形状: {df.shape}\n")
        f.write(f"列名: {list(df.columns)}\n")
        f.write("前5行数据:\n")
        f.write(str(df.head()) + "\n")
    
    # 检查必需的列
    if 'geneId' in df.columns and 'geneSymbol' in df.columns:
        # 提取唯一组合
        unique_genes = df[['geneId', 'geneSymbol']].dropna().drop_duplicates()
        unique_genes = unique_genes.sort_values('geneId')
        
        # 保存结果
        unique_genes.to_csv('unique_gene_info.csv', index=False)
        
        # 写入成功日志
        with open('extract_log.txt', 'a') as f:
            f.write(f"成功提取 {len(unique_genes)} 个唯一基因\n")
            f.write("前10行结果:\n")
            f.write(str(unique_genes.head(10)) + "\n")
        
        print("SUCCESS")
    else:
        with open('extract_log.txt', 'a') as f:
            f.write("错误: 未找到必需的列\n")
        print("ERROR: Missing columns")
        
except Exception as e:
    with open('extract_log.txt', 'w') as f:
        f.write(f"错误: {str(e)}\n")
    print(f"ERROR: {str(e)}")

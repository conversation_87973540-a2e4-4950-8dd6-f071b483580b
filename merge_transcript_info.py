#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将transcript_count和transcripts列添加到ensembl_gene_id_with_full_info_2.csv文件中
通过ensembl_gene_id列从ensembl_gene_id_with_transcript_info_2.csv文件中获取数据
"""

import pandas as pd
import os
import shutil
from datetime import datetime

def merge_transcript_info():
    """合并转录本信息"""
    
    # 文件路径
    base_file = "process_gene/ensembl_gene_id_with_full_info_2.csv"
    transcript_file = "process_gene/ensembl_gene_id_with_transcript_info_2.csv"
    backup_file = f"process_gene/ensembl_gene_id_with_full_info_2_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    try:
        print("=" * 80)
        print("合并转录本信息到基因信息文件")
        print("=" * 80)
        
        # 检查文件是否存在
        if not os.path.exists(base_file):
            raise FileNotFoundError(f"基础文件不存在: {base_file}")
        if not os.path.exists(transcript_file):
            raise FileNotFoundError(f"转录本文件不存在: {transcript_file}")
        
        print(f"基础文件: {base_file}")
        print(f"转录本文件: {transcript_file}")
        print(f"备份文件: {backup_file}")
        
        # 创建备份
        print(f"\n创建备份文件...")
        shutil.copy2(base_file, backup_file)
        print(f"✅ 备份已创建: {backup_file}")
        
        # 读取基础文件
        print(f"\n读取基础文件...")
        df_base = pd.read_csv(base_file)
        print(f"基础文件信息:")
        print(f"  行数: {len(df_base):,}")
        print(f"  列数: {len(df_base.columns)}")
        print(f"  列名: {list(df_base.columns)}")
        
        # 读取转录本文件
        print(f"\n读取转录本文件...")
        df_transcript = pd.read_csv(transcript_file)
        print(f"转录本文件信息:")
        print(f"  行数: {len(df_transcript):,}")
        print(f"  列数: {len(df_transcript.columns)}")
        print(f"  列名: {list(df_transcript.columns)}")
        
        # 检查必要的列是否存在
        if 'ensembl_gene_id' not in df_base.columns:
            raise ValueError("基础文件中缺少 ensembl_gene_id 列")
        if 'ensembl_gene_id' not in df_transcript.columns:
            raise ValueError("转录本文件中缺少 ensembl_gene_id 列")
        if 'transcript_count' not in df_transcript.columns:
            raise ValueError("转录本文件中缺少 transcript_count 列")
        if 'transcripts' not in df_transcript.columns:
            raise ValueError("转录本文件中缺少 transcripts 列")
        
        # 检查基因ID匹配情况
        base_genes = set(df_base['ensembl_gene_id'])
        transcript_genes = set(df_transcript['ensembl_gene_id'])
        
        common_genes = base_genes & transcript_genes
        missing_genes = base_genes - transcript_genes
        
        print(f"\n基因ID匹配分析:")
        print(f"  基础文件中的基因数: {len(base_genes):,}")
        print(f"  转录本文件中的基因数: {len(transcript_genes):,}")
        print(f"  匹配的基因数: {len(common_genes):,}")
        print(f"  缺失的基因数: {len(missing_genes):,}")
        print(f"  匹配率: {len(common_genes)/len(base_genes)*100:.2f}%")
        
        if missing_genes:
            print(f"\n缺失的基因ID (前10个):")
            missing_list = sorted(list(missing_genes))
            for i, gene_id in enumerate(missing_list[:10]):
                print(f"  {i+1}. {gene_id}")
            if len(missing_genes) > 10:
                print(f"  ... 还有 {len(missing_genes) - 10} 个基因")
        
        # 执行左连接合并
        print(f"\n执行数据合并...")
        
        # 选择需要合并的列
        merge_columns = ['ensembl_gene_id', 'transcript_count', 'transcripts']
        df_to_merge = df_transcript[merge_columns].copy()
        
        # 执行左连接
        df_merged = df_base.merge(
            df_to_merge,
            on='ensembl_gene_id',
            how='left'
        )
        
        print(f"合并后文件信息:")
        print(f"  行数: {len(df_merged):,}")
        print(f"  列数: {len(df_merged.columns)}")
        print(f"  列名: {list(df_merged.columns)}")
        
        # 检查合并结果
        null_transcript_count = df_merged['transcript_count'].isnull().sum()
        null_transcripts = df_merged['transcripts'].isnull().sum()
        
        print(f"\n合并结果检查:")
        print(f"  transcript_count列空值数: {null_transcript_count}")
        print(f"  transcripts列空值数: {null_transcripts}")
        
        # 对于缺失的数据，填充默认值
        if null_transcript_count > 0 or null_transcripts > 0:
            print(f"  填充缺失值...")
            df_merged['transcript_count'] = df_merged['transcript_count'].fillna(0)
            df_merged['transcripts'] = df_merged['transcripts'].fillna('[]')
            print(f"  ✅ 已填充缺失值 (transcript_count=0, transcripts='[]')")
        
        # 保存合并后的文件
        print(f"\n保存合并后的文件...")
        df_merged.to_csv(base_file, index=False)
        
        print(f"✅ 合并完成!")
        print(f"✅ 已更新文件: {base_file}")
        print(f"✅ 备份文件: {backup_file}")
        
        # 显示合并后的前几行
        print(f"\n合并后的文件前3行:")
        print(df_merged.head(3).to_string(index=False, max_colwidth=50))
        
        # 显示一些统计信息
        print(f"\n转录本统计:")
        transcript_stats = df_merged['transcript_count'].describe()
        print(f"  平均转录本数: {transcript_stats['mean']:.2f}")
        print(f"  最大转录本数: {int(transcript_stats['max'])}")
        print(f"  最小转录本数: {int(transcript_stats['min'])}")
        
        # 显示转录本数量分布
        count_distribution = df_merged['transcript_count'].value_counts().head(10)
        print(f"\n转录本数量分布 (前10):")
        for count, freq in count_distribution.items():
            print(f"  {int(count)} 个转录本: {freq} 个基因")
        
        return True
        
    except Exception as e:
        print(f"❌ 合并失败: {e}")
        return False

def main():
    """主函数"""
    success = merge_transcript_info()
    
    if success:
        print(f"\n" + "=" * 80)
        print("✅ 任务完成!")
        print("transcript_count 和 transcripts 列已成功添加到文件中")
    else:
        print(f"\n" + "=" * 80)
        print("❌ 任务失败!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def process_csv_file():
    """
    处理CSV文件，删除TR,EVI,TE同时为空的行，并保存指定列到新文件
    """
    input_file = "compute_gene/gene_count_by_project_results_with_translation_indices_and_project_info.csv"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return
    
    try:
        # 读取CSV文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_csv(input_file)
        
        print(f"原始数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(df.head())
        
        # 检查是否存在TR, EVI, TE列
        required_cols = ['TR', 'EVI', 'TE']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"警告：缺少以下列: {missing_cols}")
            print("可用的列名:")
            for i, col in enumerate(df.columns):
                print(f"{i}: {col}")
            return
        
        # 找出TR, EVI, TE同时为空的行
        # 检查空值（包括NaN, None, 空字符串等）
        tr_empty = df['TR'].isna() | (df['TR'] == '') | (df['TR'] == 0)
        evi_empty = df['EVI'].isna() | (df['EVI'] == '') | (df['EVI'] == 0)
        te_empty = df['TE'].isna() | (df['TE'] == '') | (df['TE'] == 0)
        
        # 同时为空的行
        all_empty = tr_empty & evi_empty & te_empty
        
        print(f"\nTR, EVI, TE同时为空的行数: {all_empty.sum()}")
        
        # 提取这些行的指定列
        target_cols = ['ensembl_gene_id', 'external_gene_name', 'project_id']
        missing_target_cols = [col for col in target_cols if col not in df.columns]
        
        if missing_target_cols:
            print(f"警告：缺少目标列: {missing_target_cols}")
            print("尝试查找相似的列名...")
            for col in df.columns:
                for target in missing_target_cols:
                    if target.lower() in col.lower() or col.lower() in target.lower():
                        print(f"可能的匹配: {col} -> {target}")
            return
        
        # 获取需要删除的行的指定列数据
        rows_to_delete = df[all_empty][target_cols].copy()
        
        print(f"\n将要删除的行数: {len(rows_to_delete)}")
        
        if len(rows_to_delete) > 0:
            # 保存要删除的行到新文件
            output_file = "compute_gene/deleted_rows_tr_evi_te_empty.csv"
            rows_to_delete.to_csv(output_file, index=False)
            print(f"已保存要删除的行到: {output_file}")
            
            # 显示前几行要删除的数据
            print("\n要删除的行示例（前5行）:")
            print(rows_to_delete.head())
            
            # 从原数据中删除这些行
            df_cleaned = df[~all_empty].copy()
            
            print(f"\n清理后的数据形状: {df_cleaned.shape}")
            print(f"删除了 {len(df) - len(df_cleaned)} 行")
            
            # 保存清理后的数据
            cleaned_output_file = "compute_gene/gene_count_by_project_results_cleaned.csv"
            df_cleaned.to_csv(cleaned_output_file, index=False)
            print(f"已保存清理后的数据到: {cleaned_output_file}")
            
        else:
            print("没有找到TR, EVI, TE同时为空的行")
            
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_csv_file()

import pandas as pd

# 读取两个CSV文件
print("读取文件...")
go_annotation = pd.read_csv('process_gene/GO_annotation_final.csv')
unique_genes = pd.read_csv('process_gene/unique_genes.csv')

print(f"GO_annotation_final.csv原始行数: {len(go_annotation)}")
print(f"unique_genes.csv行数: {len(unique_genes)}")

# 创建ensembl_gene_id到external_gene_name的映射字典
gene_mapping = dict(zip(unique_genes['ensembl_gene_id'], unique_genes['external_gene_name']))

# 1. 检查Gene_ID是否在unique_genes的ensembl_gene_id中，如果不在则删除该行
print("\n检查Gene_ID是否存在于unique_genes中...")
before_filter = len(go_annotation)
go_annotation_filtered = go_annotation[go_annotation['Gene_ID'].isin(unique_genes['ensembl_gene_id'])]
after_filter = len(go_annotation_filtered)
print(f"删除了 {before_filter - after_filter} 行不匹配的Gene_ID")

# 2. 对于存在的Gene_ID，检查Gene_symbol是否与external_gene_name一致，如果不一致则更新
print("\n检查Gene_symbol是否需要更新...")
updated_count = 0
for idx, row in go_annotation_filtered.iterrows():
    gene_id = row['Gene_ID']
    current_symbol = row['Gene_symbol']
    correct_symbol = gene_mapping.get(gene_id)
    
    if correct_symbol and current_symbol != correct_symbol:
        go_annotation_filtered.loc[idx, 'Gene_symbol'] = correct_symbol
        updated_count += 1

print(f"更新了 {updated_count} 个Gene_symbol")

# 保存处理后的文件
output_file = 'process_gene/GO_annotation_final_processed.csv'
go_annotation_filtered.to_csv(output_file, index=False)
print(f"\n处理完成！结果保存到: {output_file}")
print(f"最终文件行数: {len(go_annotation_filtered)}")

# 显示一些统计信息
print(f"\n统计信息:")
print(f"- 原始行数: {before_filter}")
print(f"- 删除不匹配的Gene_ID后: {after_filter}")
print(f"- 更新的Gene_symbol数量: {updated_count}")
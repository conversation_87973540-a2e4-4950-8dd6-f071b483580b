import pandas as pd
import numpy as np
from scipy.stats import zscore
import warnings
warnings.filterwarnings('ignore')

# 疾病大类与condition的映射关系
DISEASE_CATEGORIES = {
    "Infectious Disease": [
        "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
        "Adult Hepatocellular Carcinoma; HCV Transfection",
        "B95-8 Epstein-Barr Virus Infection",
        "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
        "Human Cytomegalovirus Infection",
        "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
        "IAV Transfection",
        "Lung Adenocarcinoma; IAV Infection",
        "Lung Adenocarcinoma; SARS-CoV-2 Infection",
        "M81 Epstein-Barr Virus Infection",
        "Toxoplasma Infection"
    ],
    "Gastrointestinal System Cancer": [
        "Adult Hepatocellular Carcinoma",
        "Childhood Hepatocellular Carcinoma",
        "Colon Adenocarcinoma",
        "Colon Carcinoma",
        "Esophageal Squamous Cell Carcinoma",
        "Hepatoblastoma",
        "Hepatocellular Carcinoma",
        "Intrahepatic Cholangiocarcinoma"
    ],
    "Musculoskeletal System Cancer": [
        "Osteosarcoma"
    ],
    "Reproductive Organ Cancer": [
        "Human Papillomavirus-related Endocervical Adenocarcinoma",
        "Ovarian Cancer",
        "Ovarian Endometrioid Carcinoma",
        "Prostate Cancer",
        "Prostate Carcinoma"
    ],
    "Respiratory System Cancer": [
        "Lung Adenocarcinoma",
        "Lung Large Cell Carcinoma"
    ],
    "Urinary System Cancer": [
        "Kidney Rhabdoid Cancer",
        "Kidney Tumor",
        "Renal Cell Carcinoma"
    ],
    "Genetic Disease": [
        "Duchenne Muscular Dystrophy",
        "Hbs1L Deficiency",
        "Roberts Syndrome",
        "Treacher Collins Syndrome",
        "Tuberous Sclerosis Complex"
    ],
    "Other": [
        "Cancer-derived"
    ],
    "Nervous System Cancer": [
        "Astrocytoma",
        "Brain Glioma",
        "Neuroblastoma"
    ],
    "Hematologic Cancer": [
        "Acute Myeloid Leukemia",
        "Adult Acute Myeloid Leukemia",
        "Childhood T Acute Lymphoblastic Leukemia",
        "Childhood T Lymphoblastic Lymphoma",
        "Chronic Myeloid Leukemia",
        "Multiple Myeloma"
    ],
    "Breast Cancer": [
        "Breast Adenocarcinoma",
        "Breast Carcinoma"
    ],
    "Head And Neck Cancer": [
        "Head And Neck Squamous Cell Carcinoma",
        "Tongue Squamous Cell Carcinoma"
    ],
    "Endocrine Gland Cancer": [
        "Pancreatic Adenocarcinoma"
    ]
}

def create_condition_mapping():
    """创建condition到disease category的映射字典"""
    mapping = {}
    for category, conditions in DISEASE_CATEGORIES.items():
        for condition in conditions:
            mapping[condition] = category
    return mapping

def process_single_gene_analysis(gene_data, gene_id, gene_symbol, metric):
    """为单个基因的单个指标进行z-score标准化分析，基于标准化后的数据选择最大转录本"""
    
    # 过滤有效数据
    metric_data = gene_data[gene_data[metric].notna()].copy()
    if metric_data.empty:
        return [], []
    
    # 创建透视表
    pivot_data = metric_data.pivot_table(
        index='transcriptId',
        columns='CONDITION_DATASET_ID',
        values=metric,
        aggfunc='mean'
    )
    
    # 移除包含NaN的行和列
    pivot_data = pivot_data.dropna(axis=0, how='all').dropna(axis=1, how='all')
    
    if pivot_data.empty:
        return [], []
    
    # 对每列进行z-score标准化
    try:
        pivot_data_zscore = pivot_data.apply(zscore, axis=0, nan_policy='omit')
        # 替换无穷大和NaN值为0
        pivot_data_zscore = pivot_data_zscore.replace([np.inf, -np.inf], 0).fillna(0)
    except:
        print(f"  警告: {gene_id} {metric.upper()} z-score计算失败，跳过该基因")
        return [], []
    
    # 为每个CONDITION基于z-score数据计算累加和，选出最大的TRANSCRIPT ID
    condition_results = []
    
    for condition in metric_data['Condition'].unique():
        condition_data = metric_data[metric_data['Condition'] == condition]
        if condition_data.empty:
            continue
            
        # 找到该条件对应的所有列
        condition_columns = [col for col in pivot_data_zscore.columns if col.startswith(condition + '_')]
        
        if condition_columns:
            # 基于z-score标准化数据计算每个转录本的累加和
            condition_zscore_data = pivot_data_zscore[condition_columns]
            transcript_zscore_sums = condition_zscore_data.sum(axis=1)
            
            # 选择累加和最大的转录本
            max_transcript = transcript_zscore_sums.idxmax()
            max_zscore_sum = transcript_zscore_sums.max()
            
            # 获取该转录本的原始表达值总和
            original_expr = metric_data[(metric_data['Condition'] == condition) & 
                                      (metric_data['transcriptId'] == max_transcript)][metric].sum()
        else:
            # 如果没有找到对应列，跳过
            continue
        
        disease_category = condition_data['DISEASE_CATEGORY'].iloc[0]
        
        condition_results.append({
            'GENE_ID': gene_id,
            'GENE_SYMBOL': gene_symbol,
            'CONDITION': condition,
            'CONDITION_MAX_TRANSCRIPT_ID': max_transcript,
            'CONDITION_MAX_EXPRESSION': original_expr,
            'CONDITION_MAX_ZSCORE_SUM': max_zscore_sum,
            'DISEASE_CATEGORY': disease_category,
            'CONDITION_DATASET_COUNT': len(condition_data['projectId'].unique()),
            'TOTAL_TRANSCRIPTS_IN_CONDITION': len(condition_data['transcriptId'].unique()),
            'METRIC_TYPE': metric.upper()
        })
    
    # 为每个DISEASE_CATEGORY基于z-score数据计算累加和，选出最大的TRANSCRIPT ID
    disease_results = []
    
    for category in metric_data['DISEASE_CATEGORY'].unique():
        category_data = metric_data[metric_data['DISEASE_CATEGORY'] == category]
        if category_data.empty:
            continue
            
        # 找到该疾病类别对应的所有条件和列
        category_conditions = category_data['Condition'].unique()
        category_columns = []
        for cond in category_conditions:
            category_columns.extend([col for col in pivot_data_zscore.columns if col.startswith(cond + '_')])
        
        if category_columns:
            # 基于z-score标准化数据计算每个转录本的累加和
            category_zscore_data = pivot_data_zscore[category_columns]
            transcript_zscore_sums = category_zscore_data.sum(axis=1)
            
            # 选择累加和最大的转录本
            max_transcript = transcript_zscore_sums.idxmax()
            max_zscore_sum = transcript_zscore_sums.max()
            
            # 获取该转录本的原始表达值总和
            original_expr = metric_data[(metric_data['DISEASE_CATEGORY'] == category) & 
                                      (metric_data['transcriptId'] == max_transcript)][metric].sum()
        else:
            # 如果没有找到对应列，跳过
            continue
        
        disease_results.append({
            'GENE_ID': gene_id,
            'GENE_SYMBOL': gene_symbol,
            'DISEASE_CATEGORY': category,
            'CATEGORY_MAX_TRANSCRIPT_ID': max_transcript,
            'CATEGORY_MAX_EXPRESSION': original_expr,
            'CATEGORY_MAX_ZSCORE_SUM': max_zscore_sum,
            'CONDITION_COUNT_IN_CATEGORY': len(category_data['Condition'].unique()),
            'TOTAL_TRANSCRIPTS_IN_CATEGORY': len(category_data['transcriptId'].unique()),
            'METRIC_TYPE': metric.upper()
        })
    
    return condition_results, disease_results

def process_gene_data_fast(gene_ids):
    """高效处理基因数据，为每个基因单独进行z-score分析"""
    
    print("正在读取translation indices数据...")
    
    # 一次性读取所有目标基因数据
    chunk_size = 50000
    filtered_data = []
    gene_set = set(gene_ids)
    
    for chunk in pd.read_csv('process_transcript/translation_indices_results_grouped.csv', chunksize=chunk_size):
        gene_chunk = chunk[chunk['geneId'].isin(gene_set)]
        if not gene_chunk.empty:
            filtered_data.append(gene_chunk)
    
    if not filtered_data:
        print("未找到匹配的基因数据")
        return
    
    df = pd.concat(filtered_data, ignore_index=True)
    print(f"找到 {len(df)} 条基因数据记录，涉及 {df['geneId'].nunique()} 个基因")
    
    # 预处理
    condition_mapping = create_condition_mapping()
    df['DISEASE_CATEGORY'] = df['Condition'].map(condition_mapping).fillna('Unknown')
    df['CONDITION_DATASET_ID'] = df['Condition'] + '_' + df['projectId']
    
    # 转换数值类型
    for col in ['te', 'tr', 'evi']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 为每个基因单独分析
    all_condition_results = []
    all_disease_results = []
    
    unique_genes = df['geneId'].unique()
    total_genes = len(unique_genes)
    
    for i, gene_id in enumerate(unique_genes, 1):
        print(f"处理基因 {i}/{total_genes}: {gene_id}")
        
        gene_data = df[df['geneId'] == gene_id].copy()
        gene_symbol = gene_data['geneSymbol'].iloc[0]
        
        # 分别处理三种指标
        for metric in ['te', 'tr', 'evi']:
            if gene_data[metric].notna().sum() == 0:
                continue
                
            condition_results, disease_results = process_single_gene_analysis(
                gene_data, gene_id, gene_symbol, metric
            )
            
            all_condition_results.extend(condition_results)
            all_disease_results.extend(disease_results)
    
    # 保存结果
    if all_condition_results:
        condition_df = pd.DataFrame(all_condition_results)
        condition_df = condition_df.sort_values(['GENE_ID', 'METRIC_TYPE', 'DISEASE_CATEGORY', 'CONDITION'])
        condition_df.to_csv('gene_analysis_results_by_condition_zscore.csv', index=False, encoding='utf-8')
        
        print(f"\n按CONDITION分组的结果已保存到 'gene_analysis_results_by_condition_zscore.csv'")
        print(f"CONDITION文件: {len(condition_df)} 条记录，涉及 {condition_df['GENE_ID'].nunique()} 个基因")
    
    if all_disease_results:
        disease_df = pd.DataFrame(all_disease_results)
        disease_df = disease_df.sort_values(['GENE_ID', 'METRIC_TYPE', 'DISEASE_CATEGORY'])
        disease_df.to_csv('gene_analysis_results_by_disease_category_zscore.csv', index=False, encoding='utf-8')
        
        print(f"按DISEASE_CATEGORY分组的结果已保存到 'gene_analysis_results_by_disease_category_zscore.csv'")
        print(f"DISEASE_CATEGORY文件: {len(disease_df)} 条记录，涉及 {disease_df['GENE_ID'].nunique()} 个基因")
    
    # 统计信息
    if all_condition_results:
        stats_df = pd.DataFrame(all_condition_results)
        print(f"\n各指标类型统计:")
        metric_stats = stats_df.groupby('METRIC_TYPE').agg({
            'GENE_ID': 'nunique',
            'CONDITION': 'count',
            'CONDITION_MAX_EXPRESSION': 'mean',
            'CONDITION_MAX_ZSCORE_SUM': 'mean'
        }).round(3)
        metric_stats.columns = ['Unique_Genes', 'Total_Records', 'Avg_Max_Expression', 'Avg_Max_Zscore_Sum']
        print(metric_stats)
        
        print(f"\n结果预览:")
        print(condition_df.head(10)[['GENE_ID', 'GENE_SYMBOL', 'CONDITION', 'CONDITION_MAX_TRANSCRIPT_ID', 
                                   'CONDITION_MAX_EXPRESSION', 'CONDITION_MAX_ZSCORE_SUM', 'METRIC_TYPE']].to_string(index=False))
    
    print(f"\n所有分析完成！")

def main():
    # 读取基因信息
    print("正在读取基因信息...")
    gene_info = pd.read_csv('hotmap/unique_gene_info.csv')
    gene_ids = gene_info['geneId'].tolist()
    
    print(f"共找到 {len(gene_ids)} 个基因")
    
    # 处理基因数据
    process_gene_data_fast(gene_ids)

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
import pandas as pd

# 读取结果文件
df = pd.read_csv('compute_gene/project_unique_info_with_transcript_counts.csv')

print('=== 处理结果验证 ===')
print(f'总项目数: {len(df)}')
print(f'新增列: translatedTranscriptsNumber')
print()

print('translatedTranscriptsNumber列统计:')
print(f'  最小值: {df["translatedTranscriptsNumber"].min()}')
print(f'  最大值: {df["translatedTranscriptsNumber"].max()}')
print(f'  平均值: {df["translatedTranscriptsNumber"].mean():.2f}')
print(f'  中位数: {df["translatedTranscriptsNumber"].median()}')
print()

print('最多transcript的前5个项目:')
top5 = df.nlargest(5, 'translatedTranscriptsNumber')[['Project ID', 'translatedTranscriptsNumber']]
for _, row in top5.iterrows():
    print(f'  {row["Project ID"]}: {row["translatedTranscriptsNumber"]:,}')
print()

print('最少transcript的前5个项目:')
bottom5 = df.nsmallest(5, 'translatedTranscriptsNumber')[['Project ID', 'translatedTranscriptsNumber']]
for _, row in bottom5.iterrows():
    print(f'  {row["Project ID"]}: {row["translatedTranscriptsNumber"]:,}')
print()

print('随机验证几个项目的数据:')
sample_projects = df.sample(3)[['Project ID', 'translatedTranscriptsNumber']]
for _, row in sample_projects.iterrows():
    print(f'  {row["Project ID"]}: {row["translatedTranscriptsNumber"]:,}')

print('\n处理完成! 结果文件: compute_gene/project_unique_info_with_transcript_counts.csv')

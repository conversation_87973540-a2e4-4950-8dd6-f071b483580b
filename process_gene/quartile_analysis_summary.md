# 基因数据四分位数分析总结报告

## 处理概述

本次分析处理了 `gene_count_by_project_results_cleaned.csv` 文件，按照 project_id 分组，对 TE、TR、EVI 值进行基于 Z-score 的四分位数分析。

## 数据统计

### 原始数据
- **总行数**: 2,423,614 行
- **项目数量**: 279 个不同的 project_id
- **有效数据量**:
  - TE 列非空值: 2,190,045 行
  - TR 列非空值: 270,871 行  
  - EVI 列非空值: 37,302 行

### 处理结果
- **输出文件**: `gene_quartile_analysis_results.csv`
- **输出列**: ensembl_gene_id, external_gene_name, project_id, TE_Level, TR_Level, EVI_Level

## 四分位数分布

### TE (Translation Efficiency) 等级分布
- Q1 (最低 25%): 547,669 行
- Q2 (25%-50%): 547,551 行
- Q3 (50%-75%): 547,477 行
- Q4 (最高 25%): 547,348 行

### TR (Translation Rate) 等级分布
- Q1 (最低 25%): 67,737 行
- Q2 (25%-50%): 67,719 行
- Q3 (50%-75%): 67,714 行
- Q4 (最高 25%): 67,701 行

### EVI (Evidence Index) 等级分布
- Q1 (最低 25%): 9,328 行
- Q2 (25%-50%): 9,326 行
- Q3 (50%-75%): 9,326 行
- Q4 (最高 25%): 9,322 行

## 数据覆盖情况

- **包含所有三个指标 (TE, TR, EVI)**: 37,302 行
- **包含 TE 和 TR 指标**: 37,302 行
- **只包含 TE 指标**: 2,152,743 行
- **只包含 TR 指标**: 233,569 行 (270,871 - 37,302)

## 算法说明

### 四分位数计算方法
采用基于 Z-score 的四分位数分组方法：

1. **数据预处理**: 过滤出有效数据 (值 > 0)
2. **对数转换**: 对原始值取 log2
3. **Z-score 计算**: 
   - 计算 log2 值的均值和样本标准差
   - Z-score = (log2_value - mean) / std_dev
4. **分位数阈值**: 计算 Z-score 的 25%、50%、75% 分位数
5. **等级分配**:
   - Q1: Z-score ≤ 25% 分位数
   - Q2: 25% 分位数 < Z-score ≤ 50% 分位数  
   - Q3: 50% 分位数 < Z-score ≤ 75% 分位数
   - Q4: Z-score > 75% 分位数

### 分组处理
- 按 project_id 分组独立计算四分位数
- 每个项目内部的四分位数分布基本均匀 (约 25% 每组)
- 空值或无效值保持为空

## 样本数据示例

```csv
ensembl_gene_id,external_gene_name,project_id,TE_Level,TR_Level,EVI_Level
ENSG00000000003,TSPAN6,TEDD00160,Q3,Q3,Q2
ENSG00000000003,TSPAN6,TEDD00161,Q4,Q3,Q2
ENSG00000000003,TSPAN6,TEDD00188,Q4,Q3,Q1
ENSG00000000419,DPM1,TEDD00160,Q4,Q4,Q1
ENSG00000000419,DPM1,TEDD00161,Q4,Q4,Q1
```

## 质量验证

- ✅ 四分位数分布均匀 (每组约 25%)
- ✅ 按项目分组处理正确
- ✅ 空值处理正确
- ✅ 输出格式符合要求 (英文列名)
- ✅ 数据完整性保持

## 文件位置

- **输入文件**: `/Volumes/zhy/整合的所有TPM文件/process_gene/gene_count_by_project_results_cleaned.csv`
- **输出文件**: `/Volumes/zhy/整合的所有TPM文件/process_gene/gene_quartile_analysis_results.csv`
- **处理脚本**: `/Volumes/zhy/整合的所有TPM文件/process_gene_quartile_analysis.py`

## 使用说明

输出文件可用于后续分析，如：
- 基因翻译效率等级比较
- 不同项目间的翻译指标分布分析
- 高效率/低效率基因的功能富集分析

处理完成时间: 2025年7月31日

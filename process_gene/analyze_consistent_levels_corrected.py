import pandas as pd
import numpy as np

print("正在读取CSV文件...")
df = pd.read_csv('pathway_gene_expression_results_with_condition.csv')
print(f"数据行数: {len(df)}")
print(f"数据列: {df.columns.tolist()}")

# 初始化结果列表
condition_consistent_rows = []
disease_category_consistent_rows = []

print("\n开始按Pathway_Description分组分析...")

# 按Pathway_Description分组
pathway_groups = df.groupby('Pathway_Description')
total_pathways = len(pathway_groups)
processed_pathways = 0

for pathway_name, pathway_group in pathway_groups:
    processed_pathways += 1
    if processed_pathways % 50 == 0:
        print(f"已处理 {processed_pathways}/{total_pathways} 个通路...")
    
    # 分析Condition的一致性
    if not pathway_group['Condition'].isna().all():
        # 按Condition和Gene_ID进一步分组
        condition_gene_groups = pathway_group.groupby(['Condition', 'Gene_ID'])
        
        for (condition_name, gene_id), condition_gene_group in condition_gene_groups:
            if pd.isna(condition_name):  # 跳过NaN的condition
                continue
                
            # 检查TE_Level一致性
            te_levels = condition_gene_group['TE_Level'].dropna()
            if len(te_levels) > 1 and te_levels.nunique() == 1:
                condition_consistent_rows.extend(condition_gene_group.index.tolist())
                continue
            
            # 检查TR_Level一致性
            tr_levels = condition_gene_group['TR_Level'].dropna()
            if len(tr_levels) > 1 and tr_levels.nunique() == 1:
                condition_consistent_rows.extend(condition_gene_group.index.tolist())
                continue
            
            # 检查EVI_Level一致性
            evi_levels = condition_gene_group['EVI_Level'].dropna()
            if len(evi_levels) > 1 and evi_levels.nunique() == 1:
                condition_consistent_rows.extend(condition_gene_group.index.tolist())
    
    # 分析Disease Category的一致性
    if not pathway_group['Disease Category'].isna().all():
        # 按Disease Category和Gene_ID进一步分组
        disease_gene_groups = pathway_group.groupby(['Disease Category', 'Gene_ID'])
        
        for (disease_name, gene_id), disease_gene_group in disease_gene_groups:
            if pd.isna(disease_name):  # 跳过NaN的disease category
                continue
                
            # 检查TE_Level一致性
            te_levels = disease_gene_group['TE_Level'].dropna()
            if len(te_levels) > 1 and te_levels.nunique() == 1:
                disease_category_consistent_rows.extend(disease_gene_group.index.tolist())
                continue
            
            # 检查TR_Level一致性
            tr_levels = disease_gene_group['TR_Level'].dropna()
            if len(tr_levels) > 1 and tr_levels.nunique() == 1:
                disease_category_consistent_rows.extend(disease_gene_group.index.tolist())
                continue
            
            # 检查EVI_Level一致性
            evi_levels = disease_gene_group['EVI_Level'].dropna()
            if len(evi_levels) > 1 and evi_levels.nunique() == 1:
                disease_category_consistent_rows.extend(disease_gene_group.index.tolist())

print(f"分析完成！")

# 去重并获取结果数据
condition_consistent_rows = list(set(condition_consistent_rows))
disease_category_consistent_rows = list(set(disease_category_consistent_rows))

print(f"Condition一致性分析结果行数: {len(condition_consistent_rows)}")
print(f"Disease Category一致性分析结果行数: {len(disease_category_consistent_rows)}")

# 生成结果DataFrame
if condition_consistent_rows:
    condition_result_df = df.iloc[condition_consistent_rows].copy()
    condition_output_file = 'pathway_condition_consistent_levels_corrected.csv'
    condition_result_df.to_csv(condition_output_file, index=False)
    print(f"Condition一致性结果已保存到: {condition_output_file}")
    
    # 显示一些统计信息
    print(f"Condition结果统计:")
    print(f"  涉及通路数量: {condition_result_df['Pathway_Description'].nunique()}")
    print(f"  涉及基因数量: {condition_result_df['Gene_ID'].nunique()}")
    print(f"  涉及Condition数量: {condition_result_df['Condition'].nunique()}")
    print(f"  前5个通路:")
    condition_pathway_counts = condition_result_df['Pathway_Description'].value_counts().head()
    for pathway, count in condition_pathway_counts.items():
        print(f"    {pathway}: {count}行")
else:
    print("没有找到Condition表达水平一致的数据")

if disease_category_consistent_rows:
    disease_result_df = df.iloc[disease_category_consistent_rows].copy()
    disease_output_file = 'pathway_disease_category_consistent_levels_corrected.csv'
    disease_result_df.to_csv(disease_output_file, index=False)
    print(f"\nDisease Category一致性结果已保存到: {disease_output_file}")
    
    # 显示一些统计信息
    print(f"Disease Category结果统计:")
    print(f"  涉及通路数量: {disease_result_df['Pathway_Description'].nunique()}")
    print(f"  涉及基因数量: {disease_result_df['Gene_ID'].nunique()}")
    print(f"  涉及Disease Category数量: {disease_result_df['Disease Category'].nunique()}")
    print(f"  前5个通路:")
    disease_pathway_counts = disease_result_df['Pathway_Description'].value_counts().head()
    for pathway, count in disease_pathway_counts.items():
        print(f"    {pathway}: {count}行")
else:
    print("没有找到Disease Category表达水平一致的数据")

print("\n修正后的分析完成！")
# 基因数据四分位数分析（包含Z-score值）总结报告

## 处理概述

成功修改了 `process_gene_quartile_analysis.py` 脚本，添加了经过log2和z-score转换后的值。新脚本 `process_gene_with_zscore.py` 处理了完整的数据集。

## 新增功能

### 添加的三列
1. **TE_log2_zscore**: TE值经过log2转换后的Z-score值
2. **TR_log2_zscore**: TR值经过log2转换后的Z-score值  
3. **EVI_log2_zscore**: EVI值经过log2转换后的Z-score值

### 计算方法
对于每个project_id内的每个指标（TE、TR、EVI）：
1. 过滤有效数据（值 > 0）
2. 对原始值取log2
3. 计算log2值的均值和样本标准差
4. 计算Z-score = (log2_value - mean) / std_dev
5. 同时分配四分位数等级（Q1-Q4）

## 输出文件结构

### 文件名
- **新输出文件**: `gene_quartile_analysis_with_zscore_results.csv`
- **原输出文件**: `gene_quartile_analysis_results.csv`（仍保留）

### 列结构（9列）
1. `ensembl_gene_id`: 基因ID
2. `external_gene_name`: 基因名称
3. `project_id`: 项目ID
4. `TE_Level`: TE翻译效率等级 (Q1/Q2/Q3/Q4)
5. `TR_Level`: TR翻译率等级 (Q1/Q2/Q3/Q4)
6. `EVI_Level`: EVI证据指数等级 (Q1/Q2/Q3/Q4)
7. `TE_log2_zscore`: TE的log2 Z-score值
8. `TR_log2_zscore`: TR的log2 Z-score值
9. `EVI_log2_zscore`: EVI的log2 Z-score值

## 处理结果统计

### 数据量
- **总行数**: 2,423,614 行
- **项目数**: 279 个project_id

### 四分位数分布（与之前一致）
- **TE等级**: Q1(547,669), Q2(547,551), Q3(547,477), Q4(547,348)
- **TR等级**: Q1(67,737), Q2(67,719), Q3(67,714), Q4(67,701)
- **EVI等级**: Q1(9,328), Q2(9,326), Q3(9,326), Q4(9,322)

### Z-score统计
- **TE_log2_zscore**: 
  - 有效值数量: 2,190,045
  - 均值: -0.0000 (接近0)
  - 标准差: 0.9999 (接近1)
  
- **TR_log2_zscore**: 
  - 有效值数量: 270,871
  - 均值: 0.0000 (接近0)
  - 标准差: 1.0000 (接近1)
  
- **EVI_log2_zscore**: 
  - 有效值数量: 37,302
  - 均值: 0.0000 (接近0)
  - 标准差: 0.9999 (接近1)

## 质量验证

### Z-score特性验证 ✅
- 均值接近0：符合Z-score标准化特性
- 标准差接近1：符合Z-score标准化特性
- 按项目分组计算：确保了组内标准化的正确性

### 数据完整性 ✅
- 原有的四分位数分布保持不变
- 新增Z-score列正确对应有效数据
- 无效数据的Z-score值为NaN

## 使用场景

### 新增的Z-score值可用于：
1. **连续性分析**: 相比离散的Q1-Q4等级，Z-score提供连续的数值
2. **跨项目比较**: 标准化后的值便于不同项目间比较
3. **统计建模**: Z-score值可直接用于回归分析等统计模型
4. **异常值检测**: |Z-score| > 2或3可识别异常值
5. **数据可视化**: Z-score值适合制作热图、散点图等

### 示例数据
```csv
ensembl_gene_id,external_gene_name,project_id,TE_Level,TR_Level,EVI_Level,TE_log2_zscore,TR_log2_zscore,EVI_log2_zscore
ENSG00000000003,TSPAN6,TEDD00160,Q3,Q3,Q2,0.234,-0.156,0.789
ENSG00000000419,DPM1,TEDD00161,Q4,Q4,Q1,1.567,2.134,-1.234
```

## 文件位置

- **输入文件**: `process_gene/gene_count_by_project_results_cleaned.csv`
- **输出文件**: `process_gene/gene_quartile_analysis_with_zscore_results.csv`
- **处理脚本**: `process_gene_with_zscore.py`
- **原始脚本**: `process_gene_quartile_analysis.py`（已修改）

## 技术说明

### 修改的函数
- `calculate_quartile_groups()` → `calculate_quartile_groups_and_zscores()`
- 返回值从单一列表改为元组：`(quartile_groups, zscore_values)`

### 新增处理逻辑
- 同时计算四分位数等级和Z-score值
- 保持原有算法的准确性
- 添加Z-score列的初始化和赋值

处理完成时间: 2025年7月31日

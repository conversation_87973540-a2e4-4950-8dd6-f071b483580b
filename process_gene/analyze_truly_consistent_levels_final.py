import pandas as pd
import numpy as np

print("正在读取CSV文件...")
df = pd.read_csv('pathway_gene_expression_results_with_condition.csv')
print(f"数据行数: {len(df)}")

# 初始化结果列表
condition_consistent_rows = []
disease_category_consistent_rows = []

print("\n开始按Pathway_Description分组分析...")

# 按Pathway_Description分组
pathway_groups = df.groupby('Pathway_Description')
total_pathways = len(pathway_groups)
processed_pathways = 0

for pathway_name, pathway_group in pathway_groups:
    processed_pathways += 1
    if processed_pathways % 50 == 0:
        print(f"已处理 {processed_pathways}/{total_pathways} 个通路...")
    
    # 分析Condition的一致性
    if not pathway_group['Condition'].isna().all():
        condition_groups = pathway_group.groupby('Condition')
        
        for condition_name, condition_group in condition_groups:
            if pd.isna(condition_name):  # 跳过NaN的condition
                continue
            
            # 检查该条件下是否有足够的数据进行分析
            if len(condition_group) <= 1:
                continue
                
            # 检查TE_Level是否完全一致（只考虑非空值）
            te_mask = condition_group['TE_Level'].notna()
            te_data = condition_group[te_mask]
            if len(te_data) > 1:
                te_levels = te_data['TE_Level']
                if te_levels.nunique() == 1:
                    print(f"发现Condition TE一致性: 通路={pathway_name}, 条件={condition_name}, TE_Level={te_levels.iloc[0]}, 一致行数={len(te_data)}")
                    condition_consistent_rows.extend(te_data.index.tolist())
                    continue  # 找到一致性后，跳过其他检查
            
            # 检查TR_Level是否完全一致（只考虑非空值）
            tr_mask = condition_group['TR_Level'].notna()
            tr_data = condition_group[tr_mask]
            if len(tr_data) > 1:
                tr_levels = tr_data['TR_Level']
                if tr_levels.nunique() == 1:
                    print(f"发现Condition TR一致性: 通路={pathway_name}, 条件={condition_name}, TR_Level={tr_levels.iloc[0]}, 一致行数={len(tr_data)}")
                    condition_consistent_rows.extend(tr_data.index.tolist())
                    continue  # 找到一致性后，跳过其他检查
            
            # 检查EVI_Level是否完全一致（只考虑非空值）
            evi_mask = condition_group['EVI_Level'].notna()
            evi_data = condition_group[evi_mask]
            if len(evi_data) > 1:
                evi_levels = evi_data['EVI_Level']
                if evi_levels.nunique() == 1:
                    print(f"发现Condition EVI一致性: 通路={pathway_name}, 条件={condition_name}, EVI_Level={evi_levels.iloc[0]}, 一致行数={len(evi_data)}")
                    condition_consistent_rows.extend(evi_data.index.tolist())
    
    # 分析Disease Category的一致性  
    if not pathway_group['Disease Category'].isna().all():
        disease_groups = pathway_group.groupby('Disease Category')
        
        for disease_name, disease_group in disease_groups:
            if pd.isna(disease_name):  # 跳过NaN的disease category
                continue
            
            # 检查该疾病类别下是否有足够的数据进行分析
            if len(disease_group) <= 1:
                continue
                
            # 检查TE_Level是否完全一致（只考虑非空值）
            te_mask = disease_group['TE_Level'].notna()
            te_data = disease_group[te_mask]
            if len(te_data) > 1:
                te_levels = te_data['TE_Level']
                if te_levels.nunique() == 1:
                    print(f"发现Disease Category TE一致性: 通路={pathway_name}, 疾病类别={disease_name}, TE_Level={te_levels.iloc[0]}, 一致行数={len(te_data)}")
                    disease_category_consistent_rows.extend(te_data.index.tolist())
                    continue  # 找到一致性后，跳过其他检查
            
            # 检查TR_Level是否完全一致（只考虑非空值）
            tr_mask = disease_group['TR_Level'].notna()
            tr_data = disease_group[tr_mask]
            if len(tr_data) > 1:
                tr_levels = tr_data['TR_Level']
                if tr_levels.nunique() == 1:
                    print(f"发现Disease Category TR一致性: 通路={pathway_name}, 疾病类别={disease_name}, TR_Level={tr_levels.iloc[0]}, 一致行数={len(tr_data)}")
                    disease_category_consistent_rows.extend(tr_data.index.tolist())
                    continue  # 找到一致性后，跳过其他检查
            
            # 检查EVI_Level是否完全一致（只考虑非空值）
            evi_mask = disease_group['EVI_Level'].notna()
            evi_data = disease_group[evi_mask]
            if len(evi_data) > 1:
                evi_levels = evi_data['EVI_Level']
                if evi_levels.nunique() == 1:
                    print(f"发现Disease Category EVI一致性: 通路={pathway_name}, 疾病类别={disease_name}, EVI_Level={evi_levels.iloc[0]}, 一致行数={len(evi_data)}")
                    disease_category_consistent_rows.extend(evi_data.index.tolist())

print(f"\n分析完成！")

# 去重并获取结果数据
condition_consistent_rows = list(set(condition_consistent_rows))
disease_category_consistent_rows = list(set(disease_category_consistent_rows))

print(f"Condition一致性分析结果行数: {len(condition_consistent_rows)}")
print(f"Disease Category一致性分析结果行数: {len(disease_category_consistent_rows)}")

# 生成结果DataFrame
if condition_consistent_rows:
    condition_result_df = df.iloc[condition_consistent_rows].copy()
    condition_output_file = 'pathway_condition_truly_consistent_levels.csv'
    condition_result_df.to_csv(condition_output_file, index=False)
    print(f"Condition一致性结果已保存到: {condition_output_file}")
    
    # 显示一些统计信息
    print(f"Condition结果统计:")
    print(f"  涉及通路数量: {condition_result_df['Pathway_Description'].nunique()}")
    print(f"  涉及基因数量: {condition_result_df['Gene_ID'].nunique()}")
    print(f"  涉及Condition数量: {condition_result_df['Condition'].nunique()}")
else:
    print("没有找到Condition表达水平完全一致的数据")

if disease_category_consistent_rows:
    disease_result_df = df.iloc[disease_category_consistent_rows].copy()
    disease_output_file = 'pathway_disease_category_truly_consistent_levels.csv'
    disease_result_df.to_csv(disease_output_file, index=False)
    print(f"Disease Category一致性结果已保存到: {disease_output_file}")
    
    # 显示一些统计信息
    print(f"Disease Category结果统计:")
    print(f"  涉及通路数量: {disease_result_df['Pathway_Description'].nunique()}")
    print(f"  涉及基因数量: {disease_result_df['Gene_ID'].nunique()}")
    print(f"  涉及Disease Category数量: {disease_result_df['Disease Category'].nunique()}")
else:
    print("没有找到Disease Category表达水平完全一致的数据")

print("\n最终修正后的分析完成！")
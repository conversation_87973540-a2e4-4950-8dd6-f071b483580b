#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import logging
import sys
import os
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from threading import Lock

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
    handlers=[
        logging.FileHandler('ensembl_gene_info_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 全局变量
progress_lock = Lock()
processed_count = 0
success_count = 0

class EnsemblGeneInfoScraper:
    """Ensembl基因信息爬取器"""
    
    def __init__(self, max_workers=4, timeout=10, delay=0.5, batch_size=500, max_retries=3):
        self.max_workers = max_workers
        self.timeout = timeout
        self.delay = delay
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def extract_gene_info(self, html_content):
        """从HTML内容中提取基因信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        gene_info = {
            'GENE symbol': '',
            'Approved Name': '',
            'Locus Type': '',
            'Chromosome': ''
        }
        
        try:
            # 从页面title标签提取基因符号
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text()
                # 格式通常是 "Gene: M6PR (ENSG00000003056) - Summary - Homo_sapiens - Ensembl genome browser"
                match = re.search(r'Gene:\s*([A-Z0-9\-_]{1,20})\s*\(', title_text)
                if match:
                    gene_info['GENE symbol'] = match.group(1)
            
            # 如果从title没有找到，尝试从页面标题h1
            if not gene_info['GENE symbol']:
                h1_tags = soup.find_all('h1')
                for h1 in h1_tags:
                    h1_text = h1.get_text(strip=True)
                    # 格式可能是 "Gene: M6PRENSG00000003056"
                    match = re.search(r'Gene:\s*([A-Z0-9\-_]{1,20})ENS', h1_text)
                    if match:
                        gene_info['GENE symbol'] = match.group(1)
                        break
            
            # 这种页面通常是一个重定向或错误页面，我们需要找到真正的基因页面
            # 查找是否有重定向链接
            redirect_links = soup.find_all('a', href=True)
            for link in redirect_links:
                href = link.get('href')
                if '/Gene/Summary?' in href:
                    # 这是一个指向真正基因页面的链接
                    # 我们应该跟随这个链接
                    return self.follow_redirect_and_extract(href)
            
            # 如果这确实是基因页面，尝试从可能的地方提取信息
            # 查找所有div元素寻找基因信息
            all_divs = soup.find_all('div')
            for div in all_divs:
                div_text = div.get_text(strip=True)
                
                # 查找基因类型信息
                if 'protein coding' in div_text.lower() or 'gene type' in div_text.lower():
                    if 'protein coding' in div_text.lower():
                        gene_info['Locus Type'] = 'Protein coding'
                
                # 查找染色体信息
                chr_match = re.search(r'Chromosome (\w+)', div_text)
                if chr_match:
                    gene_info['Chromosome'] = chr_match.group(1)
                
                # 查找描述信息
                if '[Source:' in div_text:
                    desc = div_text.split('[Source:')[0].strip()
                    if len(desc) > 10 and not gene_info['Approved Name']:
                        gene_info['Approved Name'] = desc
        
        except Exception as e:
            logging.warning(f"解析HTML时出错: {e}")
        
        return gene_info
    
    def follow_redirect_and_extract(self, href):
        """跟随重定向链接并提取信息"""
        try:
            # 构建完整URL
            if href.startswith('/'):
                full_url = f"https://ensembl.org{href}"
            else:
                full_url = href
                
            response = self.session.get(full_url, timeout=self.timeout)
            response.raise_for_status()
            
            return self.extract_gene_info_from_summary_page(response.text)
            
        except Exception as e:
            logging.warning(f"跟随重定向失败: {e}")
            return {
                'GENE symbol': '',
                'Approved Name': '',
                'Locus Type': '',
                'Chromosome': ''
            }
    
    def extract_gene_info_from_summary_page(self, html_content):
        """从Summary页面提取基因信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        gene_info = {
            'GENE symbol': '',
            'Approved Name': '',
            'Locus Type': '',
            'Chromosome': ''
        }
        
        try:
            # 方法1: 从页面title提取基因符号
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text()
                match = re.search(r'Gene:\s*([A-Z0-9\-_]{1,20})\s*\(', title_text)
                if match:
                    gene_info['GENE symbol'] = match.group(1)
            
            # 方法2: 从h1标签提取基因符号 (备用方法)
            if not gene_info['GENE symbol']:
                h1_tags = soup.find_all('h1', class_='summary-heading')
                for h1 in h1_tags:
                    h1_text = h1.get_text(strip=True)
                    match = re.search(r'Gene:\s*([A-Z0-9\-_]{1,20})\s*ENS', h1_text)
                    if match:
                        gene_info['GENE symbol'] = match.group(1)
                        break
            
            # 方法3: 直接查找summary_panel和twocol结构
            # 这是实际存在的HTML结构
            summary_panels = soup.find_all('div', class_='summary_panel')
            
            for summary_panel in summary_panels:
                # 查找twocol结构
                twocol = summary_panel.find('div', class_='twocol')
                if twocol:
                    rows = twocol.find_all('div', class_='row')
                    
                    for row in rows:
                        lhs = row.find('div', class_='lhs')
                        rhs = row.find('div', class_='rhs')
                        
                        if lhs and rhs:
                            label = lhs.get_text(strip=True)
                            
                            # Description -> Approved Name
                            if label == 'Description':
                                desc_text = rhs.get_text(strip=True)
                                # 移除 [Source:...] 部分
                                if '[Source:' in desc_text:
                                    desc = desc_text.split('[Source:')[0].strip()
                                else:
                                    desc = desc_text.strip()
                                gene_info['Approved Name'] = desc
                            
                            # Location -> Chromosome
                            elif label == 'Location':
                                location_text = rhs.get_text(strip=True)
                                
                                # 查找链接元素
                                location_link = rhs.find('a', class_='constant dynamic-link')
                                if location_link:
                                    # 获取链接文本和href
                                    link_text = location_link.get_text(strip=True)
                                    href = location_link.get('href', '')
                                    
                                    # 构建完整URL
                                    if href.startswith('/'):
                                        full_url = f"https://ensembl.org{href}"
                                    else:
                                        full_url = href
                                    
                                    # 格式化为key-value形式：名称:链接
                                    gene_info['Chromosome'] = f"{link_text}:{full_url}"
                                else:
                                    # 如果没有找到链接，仍然尝试提取染色体号
                                    match = re.search(r'Chromosome\s+(\w+):', location_text)
                                    if match:
                                        gene_info['Chromosome'] = match.group(1)
            
            # 方法4: 在整个页面中查找Gene type信息
            # 查找所有可能包含基因类型的地方
            all_text_elements = soup.find_all(string=re.compile('Gene type|Protein coding|protein coding', re.IGNORECASE))
            
            for text_element in all_text_elements:
                parent = text_element.parent
                if parent:
                    parent_text = parent.get_text(strip=True)
                    # 查找包含"Gene type"的行
                    if 'Gene type' in parent_text:
                        # 尝试提取基因类型
                        match = re.search(r'Gene type[:\s]+([^\.]+)', parent_text, re.IGNORECASE)
                        if match:
                            gene_type = match.group(1).strip()
                            gene_info['Locus Type'] = gene_type
                            break
                    # 或者查找"Protein coding"文本
                    elif 'Protein coding' in parent_text and not gene_info['Locus Type']:
                        gene_info['Locus Type'] = 'Protein coding'
            
            # 方法5: 查找所有row结构，不限制在特定面板内
            if not any([gene_info['Approved Name'], gene_info['Chromosome'], gene_info['Locus Type']]):
                all_rows = soup.find_all('div', class_='row')
                
                for row in all_rows:
                    lhs = row.find('div', class_='lhs')
                    rhs = row.find('div', class_='rhs')
                    
                    if lhs and rhs:
                        label = lhs.get_text(strip=True)
                        
                        # Description -> Approved Name
                        if label == 'Description' and not gene_info['Approved Name']:
                            desc_text = rhs.get_text(strip=True)
                            if '[Source:' in desc_text:
                                desc = desc_text.split('[Source:')[0].strip()
                            else:
                                desc = desc_text.strip()
                            gene_info['Approved Name'] = desc
                        
                        # Location -> Chromosome
                        elif label == 'Location' and not gene_info['Chromosome']:
                            location_text = rhs.get_text(strip=True)
                            
                            # 查找链接元素
                            location_link = rhs.find('a', class_='constant dynamic-link')
                            if location_link:
                                # 获取链接文本和href
                                link_text = location_link.get_text(strip=True)
                                href = location_link.get('href', '')
                                
                                # 构建完整URL
                                if href.startswith('/'):
                                    full_url = f"https://ensembl.org{href}"
                                else:
                                    full_url = href
                                
                                # 格式化为key-value形式：名称:链接
                                gene_info['Chromosome'] = f"{link_text}:{full_url}"
                            else:
                                # 如果没有找到链接，仍然尝试提取染色体号
                                match = re.search(r'Chromosome\s+(\w+):', location_text)
                                if match:
                                    gene_info['Chromosome'] = match.group(1)
                        
                        # Gene type -> Locus Type
                        elif label == 'Gene type' and not gene_info['Locus Type']:
                            gene_type = rhs.get_text(strip=True)
                            gene_info['Locus Type'] = gene_type
                        
                        # Name -> GENE symbol (如果之前没有获取到)
                        elif label == 'Name' and not gene_info['GENE symbol']:
                            link = rhs.find('a')
                            if link:
                                gene_symbol = link.get_text(strip=True)
                                if re.match(r'^[A-Z0-9\-_]{1,20}$', gene_symbol):
                                    gene_info['GENE symbol'] = gene_symbol
            
            # 方法6: 如果还没有获取到基因符号，尝试从其他地方提取
            if not gene_info['GENE symbol']:
                all_links = soup.find_all('a', href=True)
                for link in all_links:
                    link_text = link.get_text(strip=True)
                    if re.match(r'^[A-Z0-9\-_]{2,20}$', link_text) and len(link_text) <= 15:
                        href = link.get('href', '')
                        if 'genenames.org' in href or 'HGNC' in str(link.parent):
                            gene_info['GENE symbol'] = link_text
                            break
        
        except Exception as e:
            logging.warning(f"从Summary页面解析HTML时出错: {e}")
        
        return gene_info
    
    def scrape_single_gene(self, gene_id, total_genes):
        """爬取单个基因的信息"""
        global processed_count, success_count
        
        # 使用主域名避免亚洲镜像的延迟问题
        url = f"https://ensembl.org/id/{gene_id}"
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                time.sleep(self.delay)  # 添加延迟避免请求过于频繁
                
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                gene_info = self.extract_gene_info(response.text)
                
                # 检查是否获取到有效信息
                has_info = any(gene_info.values())
                
                with progress_lock:
                    processed_count += 1
                    if has_info:
                        success_count += 1
                    
                    if processed_count % 50 == 0:
                        success_rate = (success_count / processed_count * 100) if processed_count > 0 else 0
                        logging.info(f"进度: {processed_count}/{total_genes} ({processed_count/total_genes*100:.1f}%) - 成功: {success_count} ({success_rate:.1f}%)")
                
                return gene_id, gene_info
                
            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries - 1:
                    logging.warning(f"请求基因 {gene_id} 失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                    time.sleep(min(2 ** attempt, 10))  # 指数退避
                    continue
                else:
                    logging.warning(f"请求基因 {gene_id} 最终失败: {e}")
                    
                    with progress_lock:
                        processed_count += 1
                        if processed_count % 50 == 0:
                            success_rate = (success_count / processed_count * 100) if processed_count > 0 else 0
                            logging.info(f"进度: {processed_count}/{total_genes} ({processed_count/total_genes*100:.1f}%) - 成功: {success_count} ({success_rate:.1f}%)")
                    
                    return gene_id, {
                        'GENE symbol': '',
                        'Approved Name': '',
                        'Locus Type': '',
                        'Chromosome': ''
                    }
            
            except Exception as e:
                if attempt < self.max_retries - 1:
                    logging.warning(f"处理基因 {gene_id} 出错 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                    time.sleep(min(2 ** attempt, 10))  # 指数退避
                    continue
                else:
                    logging.error(f"处理基因 {gene_id} 最终失败: {e}")
                    
                    with progress_lock:
                        processed_count += 1
                        if processed_count % 50 == 0:
                            success_rate = (success_count / processed_count * 100) if processed_count > 0 else 0
                            logging.info(f"进度: {processed_count}/{total_genes} ({processed_count/total_genes*100:.1f}%) - 成功: {success_count} ({success_rate:.1f}%)")
                    
                    return gene_id, {
                        'GENE symbol': '',
                        'Approved Name': '',
                        'Locus Type': '',
                        'Chromosome': ''
                    }
    
    def process_genes_batch(self, gene_ids):
        """批量处理基因"""
        global processed_count, success_count
        processed_count = 0
        success_count = 0
        total_genes = len(gene_ids)
        
        logging.info(f"开始使用 {self.max_workers} 个线程处理 {total_genes} 个基因")
        logging.info(f"请求延迟: {self.delay}秒, 超时时间: {self.timeout}秒")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_gene = {
                executor.submit(self.scrape_single_gene, gene_id, total_genes): gene_id 
                for gene_id in gene_ids
            }
            
            # 收集结果
            for future in as_completed(future_to_gene):
                gene_id = future_to_gene[future]
                try:
                    gene_id_result, gene_info = future.result()
                    results[gene_id_result] = gene_info
                except Exception as e:
                    logging.error(f"处理基因 {gene_id} 时出错: {e}")
                    results[gene_id] = {
                        'GENE symbol': '',
                        'Approved Name': '',
                        'Locus Type': '',
                        'Chromosome': ''
                    }
        
        return results
    
    def run(self, input_file="process_gene/missing_genes.csv", 
            output_file="ensembl_gene_id_with_basic_info.csv"):
        """运行批量处理"""
        
        # 检查输入文件
        if not os.path.exists(input_file):
            logging.error(f"输入文件不存在: {input_file}")
            return False
        
        # 读取输入文件
        logging.info(f"读取输入文件: {input_file}")
        df = pd.read_csv(input_file)
        
        if 'ensembl_gene_id' not in df.columns:
            logging.error("输入文件缺少 'ensembl_gene_id' 列")
            return False
        
        gene_ids = df['ensembl_gene_id'].tolist()
        logging.info(f"需要处理 {len(gene_ids)} 个基因")
        
        try:
            # 批量处理基因
            results = self.process_genes_batch(gene_ids)
            
            # 更新DataFrame
            logging.info("更新数据框...")
            for col in ['GENE symbol', 'Approved Name', 'Locus Type', 'Chromosome']:
                df[col] = ''
            
            for gene_id, info in results.items():
                if gene_id in df['ensembl_gene_id'].values:
                    idx = df[df['ensembl_gene_id'] == gene_id].index[0]
                    for col, value in info.items():
                        df.at[idx, col] = value
            
            # 保存最终结果
            logging.info(f"保存最终结果到: {output_file}")
            df.to_csv(output_file, index=False)
            
        except Exception as e:
            logging.error(f"处理过程中出错: {e}")
            return False
        
        # 统计信息
        total_genes = len(df)
        successful_genes = len(df[df['GENE symbol'] != ''])
        
        logging.info("\n" + "="*50)
        logging.info("最终统计结果")
        logging.info("="*50)
        logging.info(f"总基因数: {total_genes:,}")
        logging.info(f"成功获取信息的基因数: {successful_genes:,}")
        logging.info(f"失败的基因数: {total_genes - successful_genes:,}")
        logging.info(f"成功率: {(successful_genes/total_genes*100):.2f}%")
        
        # 显示前几个结果示例
        logging.info("\n结果示例:")
        successful_df = df[df['GENE symbol'] != ''].head(5)
        for _, row in successful_df.iterrows():
            gene_id = row['ensembl_gene_id']
            gene_symbol = row['GENE symbol']
            approved_name = row['Approved Name']
            locus_type = row['Locus Type']
            chromosome = row['Chromosome']
            logging.info(f"  {gene_id} ({gene_symbol}): {approved_name[:50]}...")
            logging.info(f"    类型: {locus_type}, 染色体: {chromosome}")
        
        return True
    
    def scrape_genes_from_file(self, input_file, output_file, start_from=0):
        """从文件爬取基因信息，支持断点续传"""
        
        # 检查输入文件
        if not os.path.exists(input_file):
            logging.error(f"输入文件不存在: {input_file}")
            return False
        
        # 读取输入文件
        logging.info(f"读取输入文件: {input_file}")
        df = pd.read_csv(input_file)
        
        if 'ensembl_gene_id' not in df.columns:
            logging.error("输入文件缺少 'ensembl_gene_id' 列")
            return False
        
        # 检查是否存在输出文件(断点续传)
        if os.path.exists(output_file) and start_from == 0:
            logging.info(f"发现已存在的输出文件: {output_file}")
            try:
                existing_df = pd.read_csv(output_file)
                if len(existing_df) > 0:
                    # 找到已处理的行数
                    completed_genes = existing_df[existing_df['GENE symbol'] != '']
                    start_from = len(existing_df) if len(completed_genes) == len(existing_df) else len(completed_genes)
                    logging.info(f"自动检测断点续传位置: 第{start_from}行")
            except Exception as e:
                logging.warning(f"读取现有输出文件失败: {e}")
        
        total_genes = len(df)
        if start_from >= total_genes:
            logging.info("所有基因已处理完成")
            return True
            
        # 从指定位置开始处理
        genes_to_process = df.iloc[start_from:]['ensembl_gene_id'].tolist()
        logging.info(f"需要处理 {len(genes_to_process)} 个基因 (从第{start_from}行开始)")
        
        try:
            # 批量处理基因
            results = self.process_genes_batch(genes_to_process)
            
            # 准备输出DataFrame
            if start_from > 0 and os.path.exists(output_file):
                # 断点续传：读取现有文件
                output_df = pd.read_csv(output_file)
            else:
                # 新开始：创建新DataFrame
                output_df = df.copy()
                for col in ['GENE symbol', 'Approved Name', 'Locus Type', 'Chromosome']:
                    output_df[col] = ''
            
            # 更新结果
            logging.info("更新数据框...")
            for gene_id, info in results.items():
                if gene_id in output_df['ensembl_gene_id'].values:
                    idx = output_df[output_df['ensembl_gene_id'] == gene_id].index[0]
                    for col, value in info.items():
                        output_df.at[idx, col] = value
            
            # 保存最终结果
            logging.info(f"保存最终结果到: {output_file}")
            output_df.to_csv(output_file, index=False)
            
        except Exception as e:
            logging.error(f"处理过程中出错: {e}")
            return False
        
        # 统计信息
        total_processed = len(genes_to_process)
        successful_genes = sum(1 for _, info in results.items() if info.get('GENE symbol', ''))
        
        logging.info("\n" + "="*50)
        logging.info("批次处理统计结果")
        logging.info("="*50)
        logging.info(f"本批次处理基因数: {total_processed:,}")
        logging.info(f"本批次成功获取信息的基因数: {successful_genes:,}")
        logging.info(f"本批次失败的基因数: {total_processed - successful_genes:,}")
        logging.info(f"本批次成功率: {(successful_genes/total_processed*100):.2f}%")
        
        # 总体统计
        total_in_file = len(output_df)
        total_successful = len(output_df[output_df['GENE symbol'] != ''])
        logging.info("\n总体统计结果:")
        logging.info(f"文件总基因数: {total_in_file:,}")
        logging.info(f"总成功数: {total_successful:,}")
        logging.info(f"总体成功率: {(total_successful/total_in_file*100):.2f}%")
        
        # 显示结果示例
        if successful_genes > 0:
            logging.info("\n本批次结果示例:")
            successful_results = [(gene_id, info) for gene_id, info in results.items() if info.get('GENE symbol', '')]
            for gene_id, info in successful_results[:3]:
                gene_symbol = info['GENE symbol']
                approved_name = info['Approved Name']
                locus_type = info['Locus Type']
                chromosome = info['Chromosome']
                logging.info(f"  {gene_id} ({gene_symbol}): {approved_name[:50]}...")
                logging.info(f"    类型: {locus_type}, 染色体: {chromosome[:50]}...")
        
        return True

def main():
    """主函数 - 直接运行版本"""
    
    print("="*60)
    print("Ensembl基因信息爬取器 - 直接运行版本")
    print("="*60)
    print("配置参数:")
    print("  线程数: 4")
    print("  超时时间: 12秒")
    print("  请求延迟: 0.8秒")
    print("  输入文件: gene_ids/ensembl_gene_id_unique.csv")
    print("  输出文件: ensembl_gene_id_with_full_info.csv")
    print("="*60)
    
    # 创建爬取器实例
    scraper = EnsemblGeneInfoScraper(
        max_workers=4,
        timeout=12,
        delay=0.8,
        max_retries=5  # 增加到5次重试
    )
    
    # 运行爬取
    success = scraper.run(
        input_file='missing_genes.csv',
        output_file='ensembl_gene_id_with_full_info_2.csv'
    )
    
    if success:
        logging.info("批量处理完成!")
        print("\n✅ 爬取任务完成！")
        print("结果已保存到: ensembl_gene_id_with_full_info_2.csv")
    else:
        logging.error("批量处理失败!")
        print("\n❌ 爬取任务失败！")
        print("请检查日志文件: ensembl_gene_info_scraper.log")

if __name__ == "__main__":
    main() 
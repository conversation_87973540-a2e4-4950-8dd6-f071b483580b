#!/usr/bin/env python3
"""
<PERSON>ript to add Tissue/Cell Type, Cell line, and Disease columns to unique_genes_with_project_ids.csv
based on project_unique_info.csv lookup.
"""

import pandas as pd
import csv
from collections import defaultdict

def load_project_info(project_info_file):
    """Load project information into a dictionary for fast lookup."""
    project_info = {}
    
    with open(project_info_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            project_id = row['Project ID']
            project_info[project_id] = {
                'tissue_cell_type': row['Tissue/Cell Type'],
                'cell_line': row['Cell line'],
                'condition': row['Condition']
            }
    
    return project_info

def get_unique_values(values_list):
    """Get unique non-NA values and format as a set string."""
    unique_values = set()
    for value in values_list:
        if value and value.strip() and value.strip().upper() != 'NA':
            unique_values.add(value.strip())
    
    if unique_values:
        # Sort for consistent output
        sorted_values = sorted(unique_values)
        return '{' + ','.join(sorted_values) + '}'
    else:
        return '{}'

def load_gene_project_mapping(gene_project_file):
    """Load gene to project ID mapping from unique_genes_with_project_ids.csv."""
    gene_project_mapping = {}

    with open(gene_project_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            gene_id = row['ensembl_gene_id']
            project_ids_str = row['translation_project_ids']
            tissue_cell_type = row['Tissue/Cell Type']
            cell_line = row['Cell line']
            disease = row['Disease']

            gene_project_mapping[gene_id] = {
                'project_ids': project_ids_str,
                'tissue_cell_type': tissue_cell_type,
                'cell_line': cell_line,
                'disease': disease
            }

    return gene_project_mapping

def process_genes_file(genes_file, gene_project_file, output_file):
    """Process the genes file and add project information columns."""

    # Load gene to project mapping
    print("Loading gene to project mapping...")
    gene_project_mapping = load_gene_project_mapping(gene_project_file)
    print(f"Loaded mapping for {len(gene_project_mapping)} genes")

    # Process the genes file
    print("Processing genes file...")

    with open(genes_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8', newline='') as outfile:

        reader = csv.DictReader(infile)

        # Add new columns to fieldnames
        fieldnames = reader.fieldnames + ['Tissue/Cell Type', 'Cell line', 'Disease']
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()

        processed_count = 0
        matched_count = 0

        for row in reader:
            processed_count += 1
            if processed_count % 10000 == 0:
                print(f"Processed {processed_count} rows...")

            # Get gene ID from the row
            gene_id = row.get('Gene_ID', '')

            if gene_id and gene_id in gene_project_mapping:
                # Found mapping for this gene
                mapping = gene_project_mapping[gene_id]
                row['Tissue/Cell Type'] = mapping['tissue_cell_type']
                row['Cell line'] = mapping['cell_line']
                row['Disease'] = mapping['disease']
                matched_count += 1
            else:
                # No mapping found
                row['Tissue/Cell Type'] = '{}'
                row['Cell line'] = '{}'
                row['Disease'] = '{}'

            writer.writerow(row)

        print(f"Completed processing {processed_count} rows")
        print(f"Matched {matched_count} genes with project information")

def main():
    genes_file = 'GO_annotation_final_processed.csv'
    gene_project_file = 'unique_genes_with_project_ids.csv'
    output_file = 'GO_annotation_final_processed_with_project_info.csv'

    try:
        print(f"Input file: {genes_file}")
        print(f"Gene-project mapping file: {gene_project_file}")
        print(f"Output file: {output_file}")

        process_genes_file(genes_file, gene_project_file, output_file)
        print(f"Successfully created {output_file}")

        # Show a sample of the results
        print("\nSample of results:")
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i < 3:  # Show first 3 rows
                    print(f"Gene: {row['Gene_symbol']} ({row['Gene_ID']})")
                    print(f"  GO Term: {row['GO_Term']}")
                    print(f"  Tissue/Cell Type: {row['Tissue/Cell Type']}")
                    print(f"  Cell line: {row['Cell line']}")
                    print(f"  Disease: {row['Disease']}")
                    print()
                else:
                    break

    except Exception as e:
        import traceback
        print(f"Error: {e}")
        print("Traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    main()

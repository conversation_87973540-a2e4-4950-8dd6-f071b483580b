import pandas as pd

print("正在读取CSV文件...")

# 读取pathway_gene_expression_results.csv文件
print("正在读取pathway_gene_expression_results.csv (这可能需要一些时间...)")
pathway_df = pd.read_csv('pathway_gene_expression_results.csv')
print(f"pathway_gene_expression_results.csv行数: {len(pathway_df)}")

# 读取project_unique_info.csv文件
project_info_df = pd.read_csv('project_unique_info.csv')
print(f"project_unique_info.csv行数: {len(project_info_df)}")

print("\n检查数据结构...")
print("pathway_gene_expression_results.csv列名:", pathway_df.columns.tolist())
print("project_unique_info.csv列名:", project_info_df.columns.tolist())

# 检查project_id在两个文件中的匹配情况
pathway_project_ids = set(pathway_df['project_id'].unique())
project_info_ids = set(project_info_df['Project ID'].unique())

print(f"\npathway文件中唯一project_id数量: {len(pathway_project_ids)}")
print(f"project_info文件中唯一Project ID数量: {len(project_info_ids)}")

# 找出匹配和不匹配的project_id
matched_ids = pathway_project_ids.intersection(project_info_ids)
missing_in_project_info = pathway_project_ids - project_info_ids
missing_in_pathway = project_info_ids - pathway_project_ids

print(f"匹配的project_id数量: {len(matched_ids)}")
print(f"在project_info中未找到的project_id数量: {len(missing_in_project_info)}")
print(f"在pathway中未找到的Project ID数量: {len(missing_in_pathway)}")

if missing_in_project_info:
    print(f"在project_info中未找到的project_id (前10个): {list(missing_in_project_info)[:10]}")

# 创建project_id到Condition和Disease Category的映射
project_mapping = dict(zip(project_info_df['Project ID'], 
                          zip(project_info_df['Condition'], project_info_df['Disease Category'])))

print(f"\n正在添加Condition和Disease Category列...")
# 使用map函数添加Condition和Disease Category列
pathway_df['Condition'] = pathway_df['project_id'].map(lambda x: project_mapping.get(x, (None, None))[0])
pathway_df['Disease Category'] = pathway_df['project_id'].map(lambda x: project_mapping.get(x, (None, None))[1])

# 检查添加的列
print(f"添加Condition列后，非空值数量: {pathway_df['Condition'].notna().sum()}")
print(f"添加Disease Category列后，非空值数量: {pathway_df['Disease Category'].notna().sum()}")

# 保存更新后的文件
output_filename = 'pathway_gene_expression_results_with_condition.csv'
print(f"\n正在保存到: {output_filename}")
pathway_df.to_csv(output_filename, index=False)

print(f"文件保存完成！")

# 显示最终结果的前几行
print(f"\n最终结果预览:")
print(pathway_df[['Pathway_Description', 'Gene_ID', 'project_id', 'TE_Level', 'Condition', 'Disease Category']].head())

# 统计各Disease Category的数量
print(f"\n各Disease Category的数据量:")
disease_counts = pathway_df['Disease Category'].value_counts()
print(disease_counts.head(10))
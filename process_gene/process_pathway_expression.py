import pandas as pd

print("正在读取CSV文件...")

# 读取KEGG_final.csv文件
kegg_df = pd.read_csv('KEGG_final.csv')
print(f"KEGG_final.csv行数: {len(kegg_df)}")

# 读取gene_quartile_analysis_with_zscore_results.csv文件
expression_df = pd.read_csv('gene_quartile_analysis_with_zscore_results.csv')
print(f"gene_quartile_analysis_with_zscore_results.csv行数: {len(expression_df)}")

# 获取所有的Pathway_Description
unique_pathways = kegg_df['Pathway_Description'].unique()
print(f"唯一的Pathway_Description数量: {len(unique_pathways)}")

print("\n开始处理每个通路的数据...")

# 创建结果列表
results = []

# 按Pathway_Description进行分组处理
for pathway in unique_pathways:
    print(f"处理通路: {pathway}")
    
    # 获取当前通路下的所有Gene_ID
    pathway_genes = kegg_df[kegg_df['Pathway_Description'] == pathway]['Gene_ID'].unique()
    print(f"  包含基因数量: {len(pathway_genes)}")
    
    # 查询这些Gene_ID在表达数据中的所有行
    pathway_expression = expression_df[expression_df['ensembl_gene_id'].isin(pathway_genes)]
    print(f"  找到表达数据行数: {len(pathway_expression)}")
    
    # 为每行数据添加Pathway_Description
    for _, row in pathway_expression.iterrows():
        result_row = {
            'Pathway_Description': pathway,
            'Gene_ID': row['ensembl_gene_id'],
            'project_id': row['project_id'],
            'TE_Level': row['TE_Level'],
            'TR_Level': row['TR_Level'],
            'EVI_Level': row['EVI_Level']
        }
        results.append(result_row)

print(f"\n总共生成结果行数: {len(results)}")

# 创建结果DataFrame
result_df = pd.DataFrame(results)

# 输出到CSV文件
output_filename = 'pathway_gene_expression_results.csv'
result_df.to_csv(output_filename, index=False)

print(f"结果已保存到: {output_filename}")

# 显示一些统计信息
print(f"\n结果统计:")
print(f"通路数量: {result_df['Pathway_Description'].nunique()}")
print(f"基因数量: {result_df['Gene_ID'].nunique()}")
print(f"项目数量: {result_df['project_id'].nunique()}")

# 显示前几行结果
print(f"\n前5行结果预览:")
print(result_df.head())

# 按通路统计结果行数
pathway_counts = result_df['Pathway_Description'].value_counts()
print(f"\n各通路的结果行数(前10个):")
print(pathway_counts.head(10))
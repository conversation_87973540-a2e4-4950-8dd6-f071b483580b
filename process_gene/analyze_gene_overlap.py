#!/usr/bin/env python3
import pandas as pd
import numpy as np

# 读取两个CSV文件
print("正在读取CSV文件...")
go_df = pd.read_csv('GO_annotation_final.csv')
unique_genes_df = pd.read_csv('unique_genes.csv')

print(f"GO_annotation_final.csv行数: {len(go_df)}")
print(f"unique_genes.csv行数: {len(unique_genes_df)}")

# 获取唯一的Gene_ID
go_gene_ids = set(go_df['Gene_ID'].unique())
unique_gene_ids = set(unique_genes_df['ensembl_gene_id'].unique())

print(f"\nGO_annotation_final.csv中唯一Gene_ID数量: {len(go_gene_ids)}")
print(f"unique_genes.csv中唯一ensembl_gene_id数量: {len(unique_gene_ids)}")

# 检查重叠情况
overlap = go_gene_ids.intersection(unique_gene_ids)
go_only = go_gene_ids - unique_gene_ids
unique_only = unique_gene_ids - go_gene_ids

print(f"\n重叠的基因ID数量: {len(overlap)}")
print(f"只在GO文件中的基因ID数量: {len(go_only)}")
print(f"只在unique_genes文件中的基因ID数量: {len(unique_only)}")

print(f"\n重叠率: {len(overlap)/len(go_gene_ids)*100:.2f}%")

# 检查只在GO文件中的基因ID示例
if len(go_only) > 0:
    print(f"\n只在GO文件中的基因ID示例 (前10个):")
    for gene_id in list(go_only)[:10]:
        print(f"  {gene_id}")

# 检查GO文件中Gene_symbol为空的情况
go_df_with_na_symbol = go_df[go_df['Gene_symbol'].isna()]
print(f"\nGO文件中Gene_symbol为空的记录数: {len(go_df_with_na_symbol)}")

# 检查这些为空的Gene_ID是否在unique_genes中有对应
if len(go_df_with_na_symbol) > 0:
    na_gene_ids = set(go_df_with_na_symbol['Gene_ID'].unique())
    na_found_in_unique = na_gene_ids.intersection(unique_gene_ids)
    print(f"Gene_symbol为空但在unique_genes中有对应的基因数量: {len(na_found_in_unique)}")
    
    if len(na_found_in_unique) > 0:
        print("这些基因的external_gene_name示例:")
        for gene_id in list(na_found_in_unique)[:5]:
            external_name = unique_genes_df[unique_genes_df['ensembl_gene_id'] == gene_id]['external_gene_name'].iloc[0]
            print(f"  {gene_id} -> {external_name}")
import pandas as pd

# 读取两个CSV文件
kegg_df = pd.read_csv('KEGG_final.csv')
unique_genes_df = pd.read_csv('unique_genes.csv')

print(f"原始KEGG_final.csv行数: {len(kegg_df)}")

# 获取unique_genes.csv中的ensembl_gene_id集合
ensembl_gene_ids = set(unique_genes_df['ensembl_gene_id'].dropna())

# 获取KEGG_final.csv中未找到的Gene_ID
kegg_gene_ids = set(kegg_df['Gene_ID'].dropna())
missing_genes = kegg_gene_ids - ensembl_gene_ids

print(f"未找到的Gene_ID数量: {len(missing_genes)}")
print("未找到的Gene_ID列表:")
for gene_id in sorted(missing_genes):
    print(f"  {gene_id}")

# 从KEGG_final.csv中删除未找到的Gene_ID行
filtered_kegg_df = kegg_df[~kegg_df['Gene_ID'].isin(missing_genes)]

print(f"\n删除后KEGG_final.csv行数: {len(filtered_kegg_df)}")
print(f"删除的行数: {len(kegg_df) - len(filtered_kegg_df)}")

# 保存清理后的文件
filtered_kegg_df.to_csv('KEGG_final.csv', index=False)
print("已保存清理后的KEGG_final.csv文件")

# 验证清理结果
remaining_gene_ids = set(filtered_kegg_df['Gene_ID'].dropna())
still_missing = remaining_gene_ids - ensembl_gene_ids
print(f"\n验证：清理后仍未找到的Gene_ID数量: {len(still_missing)}")
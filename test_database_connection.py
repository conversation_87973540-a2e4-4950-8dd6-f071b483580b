#!/usr/bin/env python3
"""
测试数据库连接和表结构的脚本
"""

import pymysql
import sys

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'charset': 'utf8mb4'
}

def test_database_connection():
    """测试数据库连接和基本查询"""
    connection = None
    
    try:
        print("正在测试数据库连接...")
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功!")
        
        with connection.cursor() as cursor:
            # 测试表是否存在
            cursor.execute("SHOW TABLES LIKE 'translationIndices'")
            result = cursor.fetchone()

            if result:
                print("✅ translationIndices 表存在")

                # 获取表结构
                cursor.execute("DESCRIBE translationIndices")
                columns = cursor.fetchall()
                print("\n📋 表结构:")
                for column in columns:
                    print(f"  - {column[0]}: {column[1]}")

                # 获取数据统计
                cursor.execute("SELECT COUNT(*) FROM translationIndices")
                total_rows = cursor.fetchone()[0]
                print(f"\n📊 总记录数: {total_rows:,}")

                # 获取唯一转录本数量
                cursor.execute("SELECT COUNT(DISTINCT transcriptId) FROM translationIndices")
                unique_transcripts = cursor.fetchone()[0]
                print(f"📊 唯一转录本数量: {unique_transcripts:,}")

                # 获取唯一基因数量
                cursor.execute("SELECT COUNT(DISTINCT geneId) FROM translationIndices")
                unique_genes = cursor.fetchone()[0]
                print(f"📊 唯一基因数量: {unique_genes:,}")

                # 获取唯一项目数量
                cursor.execute("SELECT COUNT(DISTINCT projectId) FROM translationIndices")
                unique_projects = cursor.fetchone()[0]
                print(f"📊 唯一项目数量: {unique_projects:,}")

                # 显示一些示例数据
                cursor.execute("SELECT id, transcriptId, geneId, geneSymbol, projectId FROM translationIndices LIMIT 5")
                sample_data = cursor.fetchall()
                print("\n📝 示例数据:")
                for row in sample_data:
                    print(f"  ID: {row[0]}, 转录本: {row[1]}, 基因ID: {row[2]}, 基因名: {row[3]}, 项目ID: {row[4]}")

            else:
                print("❌ translationIndices 表不存在")
                return False
                
    except pymysql.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False
    finally:
        if connection:
            connection.close()
            print("\n🔒 数据库连接已关闭")
    
    return True

def test_sample_query():
    """测试示例查询"""
    connection = None

    try:
        connection = pymysql.connect(**DB_CONFIG)

        # 测试查询特定基因和项目组合
        test_gene_id = "ENSG00000000971"
        test_project_id = "TEDD00019"

        sql = """
        SELECT id, transcriptId, geneId, geneSymbol, projectId, tr, evi, te
        FROM translationIndices
        WHERE geneId = %s AND projectId = %s
        """

        with connection.cursor() as cursor:
            cursor.execute(sql, (test_gene_id, test_project_id))
            results = cursor.fetchall()

            print(f"\n🔍 测试查询结果 (geneId={test_gene_id}, projectId={test_project_id}):")
            print(f"找到 {len(results)} 条记录")

            for row in results:
                print(f"  ID: {row[0]}, 转录本: {row[1]}, 基因: {row[2]} ({row[3]}), 项目: {row[4]}")
                print(f"    TR: {row[5]}, EVI: {row[6]}, TE: {row[7]}")

        # 测试批量查询
        test_pairs = [
            ("ENSG00000000971", "TEDD00037"),
            ("ENSG00000001626", "TEDD00025"),
            ("ENSG00000001630", "TEDD00071")
        ]

        conditions = []
        params = []
        for gene_id, project_id in test_pairs:
            conditions.append("(geneId = %s AND projectId = %s)")
            params.extend([gene_id, project_id])

        batch_sql = f"""
        SELECT geneId, projectId, COUNT(*) as count
        FROM translationIndices
        WHERE {' OR '.join(conditions)}
        GROUP BY geneId, projectId
        """

        with connection.cursor() as cursor:
            cursor.execute(batch_sql, params)
            batch_results = cursor.fetchall()

            print(f"\n🔍 批量查询结果:")
            for row in batch_results:
                print(f"  {row[0]} + {row[1]}: {row[2]} 条记录")

    except Exception as e:
        print(f"❌ 查询测试失败: {e}")
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    print("🧪 数据库连接测试")
    print("=" * 50)
    
    if test_database_connection():
        print("\n" + "=" * 50)
        test_sample_query()
        print("\n✅ 所有测试完成!")
    else:
        print("\n❌ 数据库连接测试失败，请检查配置")
        sys.exit(1)

#!/usr/bin/env python3
import pandas as pd

# 将验证结果写入文件
with open('gene_merge_verification.txt', 'w', encoding='utf-8') as f:
    f.write("基因合并结果验证报告\n")
    f.write("=" * 50 + "\n\n")
    
    try:
        # 读取合并后的文件
        df_merged = pd.read_csv('process_gene/ensembl_gene_id_with_full_info_2.csv')
        
        f.write(f"合并后文件信息:\n")
        f.write(f"  行数: {len(df_merged):,}\n")
        f.write(f"  列数: {len(df_merged.columns)}\n")
        f.write(f"  列名: {list(df_merged.columns)}\n\n")
        
        # 检查基因ID唯一性
        unique_genes = df_merged['ensembl_gene_id'].nunique()
        total_rows = len(df_merged)
        
        f.write(f"数据质量检查:\n")
        f.write(f"  总行数: {total_rows:,}\n")
        f.write(f"  唯一基因ID数: {unique_genes:,}\n")
        f.write(f"  重复基因数: {total_rows - unique_genes}\n")
        
        if total_rows == unique_genes:
            f.write(f"  ✅ 无重复基因ID\n")
        else:
            f.write(f"  ❌ 存在重复基因ID\n")
        
        # 检查空值
        f.write(f"\n空值检查:\n")
        for col in df_merged.columns:
            null_count = df_merged[col].isnull().sum()
            f.write(f"  {col}: {null_count} 个空值\n")
        
        # 转录本统计
        if 'transcript_count' in df_merged.columns:
            f.write(f"\n转录本统计:\n")
            transcript_stats = df_merged['transcript_count'].describe()
            f.write(f"  平均转录本数: {transcript_stats['mean']:.2f}\n")
            f.write(f"  最大转录本数: {int(transcript_stats['max'])}\n")
            f.write(f"  最小转录本数: {int(transcript_stats['min'])}\n")
            
            # 转录本数量分布
            f.write(f"\n转录本数量分布 (前10):\n")
            count_dist = df_merged['transcript_count'].value_counts().head(10)
            for count, freq in count_dist.items():
                f.write(f"  {count} 个转录本: {freq:,} 个基因\n")
        
        # 显示前几行
        f.write(f"\n前5行数据:\n")
        for i, (_, row) in enumerate(df_merged.head().iterrows()):
            f.write(f"  {i+1}. {row['ensembl_gene_id']} - {row['GENE symbol']}")
            if 'transcript_count' in df_merged.columns:
                f.write(f" - {row['transcript_count']} 个转录本")
            f.write(f"\n")
        
        f.write(f"\n✅ 验证完成!\n")
        
        # 读取备份文件进行对比
        try:
            backup_files = [
                'process_gene/ensembl_gene_id_with_full_info_2_backup_20250731_221535.csv'
            ]
            
            for backup_file in backup_files:
                try:
                    df_backup = pd.read_csv(backup_file)
                    f.write(f"\n与备份文件对比 ({backup_file}):\n")
                    f.write(f"  备份文件行数: {len(df_backup):,}\n")
                    f.write(f"  合并后行数: {len(df_merged):,}\n")
                    f.write(f"  增加行数: {len(df_merged) - len(df_backup):,}\n")
                    break
                except:
                    continue
        except:
            pass
        
    except Exception as e:
        f.write(f"❌ 验证失败: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("验证结果已写入 gene_merge_verification.txt")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def remove_matching_rows():
    """
    1. 获取compute_gene/deleted_rows_tr_evi_te_empty.csv文件中ensembl_gene_id和project_id组合
    2. 删除compute_transcript/translation_indices_results_grouped_with_disease_category.csv文件中
       ensembl_gene_id和project_id同时定位的数据
    """
    
    # 文件路径
    deleted_rows_file = "compute_gene/deleted_rows_tr_evi_te_empty.csv"
    transcript_file = "process_transcript/translation_indices_results_grouped.csv"
    
    print("=== 开始处理匹配行删除任务 ===\n")
    
    # 步骤1：读取要删除的行的ensembl_gene_id和project_id组合
    if not os.path.exists(deleted_rows_file):
        print(f"❌ 错误：文件不存在 {deleted_rows_file}")
        return
    
    try:
        print(f"📖 正在读取删除行文件: {deleted_rows_file}")
        deleted_df = pd.read_csv(deleted_rows_file)
        print(f"   文件行数: {len(deleted_df):,}")
        print(f"   文件列名: {list(deleted_df.columns)}")
        
        # 检查必需的列
        if 'ensembl_gene_id' not in deleted_df.columns or 'project_id' not in deleted_df.columns:
            print(f"❌ 错误：删除行文件缺少必需的列")
            print(f"   需要的列: ensembl_gene_id, project_id")
            print(f"   实际的列: {list(deleted_df.columns)}")
            return
        
        # 获取ensembl_gene_id和project_id的组合
        gene_project_combinations = deleted_df[['ensembl_gene_id', 'project_id']].drop_duplicates()
        print(f"   唯一的基因-项目组合数: {len(gene_project_combinations):,}")
        
        # 显示前几个组合示例
        print(f"   前5个组合示例:")
        print(gene_project_combinations.head().to_string(index=False))
        print()
        
    except Exception as e:
        print(f"❌ 读取删除行文件时出错: {str(e)}")
        return
    
    # 步骤2：读取transcript文件
    if not os.path.exists(transcript_file):
        print(f"❌ 错误：文件不存在 {transcript_file}")
        return
    
    try:
        print(f"📖 正在读取transcript文件: {transcript_file}")
        transcript_df = pd.read_csv(transcript_file)
        print(f"   原始文件行数: {len(transcript_df):,}")
        print(f"   原始文件列数: {len(transcript_df.columns)}")
        print(f"   原始文件列名: {list(transcript_df.columns)}")
        
        # 检查必需的列 - 支持不同的列名格式
        gene_id_col = None
        project_id_col = None

        # 查找基因ID列
        if 'ensembl_gene_id' in transcript_df.columns:
            gene_id_col = 'ensembl_gene_id'
        elif 'geneId' in transcript_df.columns:
            gene_id_col = 'geneId'

        # 查找项目ID列
        if 'project_id' in transcript_df.columns:
            project_id_col = 'project_id'
        elif 'projectId' in transcript_df.columns:
            project_id_col = 'projectId'

        if gene_id_col is None or project_id_col is None:
            print(f"❌ 错误：transcript文件缺少必需的列")
            print(f"   需要的列: 基因ID列 (ensembl_gene_id 或 geneId), 项目ID列 (project_id 或 projectId)")
            print(f"   实际的列: {list(transcript_df.columns)}")
            return

        print(f"   使用的基因ID列: {gene_id_col}")
        print(f"   使用的项目ID列: {project_id_col}")
        
        # 显示前几行数据
        print(f"   前3行数据示例:")
        print(transcript_df.head(3).to_string(index=False))
        print()
        
    except Exception as e:
        print(f"❌ 读取transcript文件时出错: {str(e)}")
        return
    
    # 步骤3：创建匹配标识
    try:
        print("🔍 正在查找匹配的行...")
        
        # 创建组合键用于匹配
        gene_project_combinations['match_key'] = (
            gene_project_combinations['ensembl_gene_id'].astype(str) + 
            '|' + 
            gene_project_combinations['project_id'].astype(str)
        )
        
        transcript_df['match_key'] = (
            transcript_df[gene_id_col].astype(str) +
            '|' +
            transcript_df[project_id_col].astype(str)
        )
        
        # 找到匹配的行
        matching_keys = set(gene_project_combinations['match_key'])
        matches_mask = transcript_df['match_key'].isin(matching_keys)
        matching_rows_count = matches_mask.sum()
        
        print(f"   找到匹配的行数: {matching_rows_count:,}")
        
        if matching_rows_count > 0:
            # 显示一些匹配的行示例
            matching_rows = transcript_df[matches_mask].head(5)
            print(f"   匹配行示例（前5行）:")
            print(matching_rows[[gene_id_col, project_id_col]].to_string(index=False))
            print()
            
            # 删除匹配的行
            transcript_df_cleaned = transcript_df[~matches_mask].copy()
            
            # 删除临时的match_key列
            transcript_df_cleaned = transcript_df_cleaned.drop('match_key', axis=1)
            
            print(f"📊 删除统计:")
            print(f"   原始行数: {len(transcript_df):,}")
            print(f"   删除行数: {matching_rows_count:,}")
            print(f"   剩余行数: {len(transcript_df_cleaned):,}")
            print(f"   删除比例: {matching_rows_count/len(transcript_df)*100:.2f}%")
            print()
            
            # 保存清理后的文件
            output_file = "process_transcript/translation_indices_results_grouped_cleaned.csv"
            transcript_df_cleaned.to_csv(output_file, index=False)
            print(f"✅ 已保存清理后的文件到: {output_file}")
            
            # 保存被删除的行（可选）
            deleted_transcript_rows = transcript_df[matches_mask].drop('match_key', axis=1)
            deleted_output_file = "process_transcript/deleted_matching_rows.csv"
            deleted_transcript_rows.to_csv(deleted_output_file, index=False)
            print(f"✅ 已保存被删除的行到: {deleted_output_file}")
            
        else:
            print("ℹ️  没有找到匹配的行，无需删除")
            
    except Exception as e:
        print(f"❌ 处理匹配行时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 任务完成 ===")
    print("✅ 成功根据ensembl_gene_id和project_id组合删除了匹配的行")
    if matching_rows_count > 0:
        print(f"✅ 删除了 {matching_rows_count:,} 行匹配数据")
        print(f"✅ 清理后的文件保存为: translation_indices_results_grouped_cleaned.csv")
        print(f"✅ 被删除的行保存为: deleted_matching_rows.csv")

if __name__ == "__main__":
    remove_matching_rows()

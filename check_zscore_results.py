#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Z-score结果文件
"""

import pandas as pd
import numpy as np

def check_results():
    """
    检查结果文件
    """
    result_file = "/Volumes/zhy/整合的所有TPM文件/process_gene/gene_quartile_analysis_with_zscore_results.csv"
    
    print("正在读取结果文件...")
    df = pd.read_csv(result_file)
    
    print(f"文件列名: {list(df.columns)}")
    print(f"文件行数: {len(df)}")
    
    print("\n前5行数据:")
    print(df.head())
    
    print("\n包含TE Z-score值的前5行:")
    te_with_zscore = df[df['TE_log2_zscore'].notna()]
    if len(te_with_zscore) > 0:
        print(te_with_zscore.head())
        print(f"TE Z-score有效值数量: {len(te_with_zscore)}")
    
    print("\n包含TR Z-score值的前5行:")
    tr_with_zscore = df[df['TR_log2_zscore'].notna()]
    if len(tr_with_zscore) > 0:
        print(tr_with_zscore.head())
        print(f"TR Z-score有效值数量: {len(tr_with_zscore)}")
    
    print("\n包含EVI Z-score值的前5行:")
    evi_with_zscore = df[df['EVI_log2_zscore'].notna()]
    if len(evi_with_zscore) > 0:
        print(evi_with_zscore.head())
        print(f"EVI Z-score有效值数量: {len(evi_with_zscore)}")
    
    print("\n包含所有三个Z-score值的行:")
    all_zscore = df[(df['TE_log2_zscore'].notna()) & 
                   (df['TR_log2_zscore'].notna()) & 
                   (df['EVI_log2_zscore'].notna())]
    if len(all_zscore) > 0:
        print(all_zscore.head())
        print(f"包含所有三个Z-score值的行数: {len(all_zscore)}")
    
    # 统计信息
    print("\n=== 统计信息 ===")
    for col in ['TE_log2_zscore', 'TR_log2_zscore', 'EVI_log2_zscore']:
        valid_values = df[col].dropna()
        if len(valid_values) > 0:
            print(f"{col}:")
            print(f"  有效值数量: {len(valid_values)}")
            print(f"  均值: {valid_values.mean():.6f}")
            print(f"  标准差: {valid_values.std():.6f}")
            print(f"  最小值: {valid_values.min():.6f}")
            print(f"  最大值: {valid_values.max():.6f}")

if __name__ == "__main__":
    check_results()

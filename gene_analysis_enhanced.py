import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 疾病大类与condition的映射关系
DISEASE_CATEGORIES = {
    "Infectious Disease": [
        "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
        "Adult Hepatocellular Carcinoma; HCV Transfection",
        "B95-8 Epstein-Barr Virus Infection",
        "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
        "Human Cytomegalovirus Infection",
        "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
        "IAV Transfection",
        "Lung Adenocarcinoma; IAV Infection",
        "Lung Adenocarcinoma; SARS-CoV-2 Infection",
        "M81 Epstein-Barr Virus Infection",
        "Toxoplasma Infection"
    ],
    "Gastrointestinal System Cancer": [
        "Adult Hepatocellular Carcinoma",
        "Childhood Hepatocellular Carcinoma",
        "Colon Adenocarcinoma",
        "Colon Carcinoma",
        "Esophageal Squamous Cell Carcinoma",
        "Hepatoblastoma",
        "Hepatocellular Carcinoma",
        "Intrahepatic Cholangiocarcinoma"
    ],
    "Musculoskeletal System Cancer": [
        "Osteosarcoma"
    ],
    "Reproductive Organ Cancer": [
        "Human Papillomavirus-related Endocervical Adenocarcinoma",
        "Ovarian Cancer",
        "Ovarian Endometrioid Carcinoma",
        "Prostate Cancer",
        "Prostate Carcinoma"
    ],
    "Respiratory System Cancer": [
        "Lung Adenocarcinoma",
        "Lung Large Cell Carcinoma"
    ],
    "Urinary System Cancer": [
        "Kidney Rhabdoid Cancer",
        "Kidney Tumor",
        "Renal Cell Carcinoma"
    ],
    "Genetic Disease": [
        "Duchenne Muscular Dystrophy",
        "Hbs1L Deficiency",
        "Roberts Syndrome",
        "Treacher Collins Syndrome",
        "Tuberous Sclerosis Complex"
    ],
    "Other": [
        "Cancer-derived"
    ],
    "Nervous System Cancer": [
        "Astrocytoma",
        "Brain Glioma",
        "Neuroblastoma"
    ],
    "Hematologic Cancer": [
        "Acute Myeloid Leukemia",
        "Adult Acute Myeloid Leukemia",
        "Childhood T Acute Lymphoblastic Leukemia",
        "Childhood T Lymphoblastic Lymphoma",
        "Chronic Myeloid Leukemia",
        "Multiple Myeloma"
    ],
    "Breast Cancer": [
        "Breast Adenocarcinoma",
        "Breast Carcinoma"
    ],
    "Head And Neck Cancer": [
        "Head And Neck Squamous Cell Carcinoma",
        "Tongue Squamous Cell Carcinoma"
    ],
    "Endocrine Gland Cancer": [
        "Pancreatic Adenocarcinoma"
    ]
}

def map_condition_to_disease_category(condition):
    """将condition映射到疾病大类"""
    if 'Normal' in condition:
        return 'Normal'
    
    for category, conditions in DISEASE_CATEGORIES.items():
        if condition in conditions:
            return category
    
    return 'Unknown'

def process_gene_data(gene_ids):
    """处理基因数据，为每个基因分别对TE、TR、EVI进行分析"""
    
    print("正在读取translation indices数据...")
    # 读取大文件时分块处理
    chunk_size = 10000
    filtered_data = []
    
    for chunk in pd.read_csv('process_transcript/translation_indices_results_grouped.csv', chunksize=chunk_size):
        # 过滤出目标基因的数据
        gene_chunk = chunk[chunk['geneId'].isin(gene_ids)]
        if not gene_chunk.empty:
            filtered_data.append(gene_chunk)
    
    if not filtered_data:
        print("未找到匹配的基因数据")
        return
    
    df = pd.concat(filtered_data, ignore_index=True)
    print(f"找到 {len(df)} 条基因数据记录")
    print(f"涉及基因数量: {df['geneId'].nunique()}")
    
    # 为每个基因分别处理TE、TR、EVI数据
    all_condition_results = []
    all_disease_results = []
    
    for gene_id in df['geneId'].unique():
        print(f"\n开始处理基因 {gene_id}...")
        gene_data = df[df['geneId'] == gene_id].copy()
        gene_symbol = gene_data['geneSymbol'].iloc[0]
        
        # 分别处理TE、TR、EVI数据
        metrics = ['te', 'tr', 'evi']
        
        for metric in metrics:
            print(f"  处理 {gene_id} 的 {metric.upper()} 数据...")
            
            # 过滤出有该指标值的数据
            metric_data = gene_data[gene_data[metric].notna()].copy()
            
            if metric_data.empty:
                print(f"  {gene_id} 没有找到 {metric.upper()} 数据")
                continue
                
            # 确保指标值是数值类型
            metric_data[metric] = pd.to_numeric(metric_data[metric], errors='coerce')
            metric_data = metric_data.dropna(subset=[metric])
            
            if metric_data.empty:
                print(f"  {gene_id} 的 {metric.upper()} 数据转换后为空")
                continue
            
            # 创建CONDITION_DATASET_ID
            metric_data['CONDITION_DATASET_ID'] = metric_data['Condition'] + '_' + metric_data['projectId']
            
            # 映射疾病大类
            metric_data['DISEASE_CATEGORY'] = metric_data['Condition'].apply(map_condition_to_disease_category)
            
            print(f"    {gene_id} {metric.upper()} 数据形状: {metric_data.shape}")
            
            # 为每个CONDITION计算和，选出最大的TRANSCRIPT ID
            condition_max_transcript = {}
            for condition in metric_data['Condition'].unique():
                condition_data = metric_data[metric_data['Condition'] == condition]
                transcript_sums = condition_data.groupby('transcriptId')[metric].sum()
                if not transcript_sums.empty:
                    max_transcript = transcript_sums.idxmax()
                    condition_max_transcript[condition] = max_transcript
            
            # 为每个疾病大类计算和，选出最大的TRANSCRIPT ID
            category_max_transcript = {}
            for category in metric_data['DISEASE_CATEGORY'].unique():
                category_data = metric_data[metric_data['DISEASE_CATEGORY'] == category]
                transcript_sums = category_data.groupby('transcriptId')[metric].sum()
                if not transcript_sums.empty:
                    max_transcript = transcript_sums.idxmax()
                    category_max_transcript[category] = max_transcript
            
            # 按CONDITION创建结果
            for condition in metric_data['Condition'].unique():
                condition_data = metric_data[metric_data['Condition'] == condition]
                if condition_data.empty:
                    continue
                
                disease_category = condition_data['DISEASE_CATEGORY'].iloc[0]
                
                if condition in condition_max_transcript:
                    condition_max_trans = condition_max_transcript[condition]
                    # 计算该condition下该transcript的表达值总和
                    condition_max_expr = metric_data[(metric_data['Condition'] == condition) & 
                                                   (metric_data['transcriptId'] == condition_max_trans)][metric].sum()
                else:
                    condition_max_trans = None
                    condition_max_expr = 0
                
                all_condition_results.append({
                    'GENE_ID': gene_id,
                    'GENE_SYMBOL': gene_symbol,
                    'CONDITION': condition,
                    'CONDITION_MAX_TRANSCRIPT_ID': condition_max_trans,
                    'CONDITION_MAX_EXPRESSION': condition_max_expr,
                    'DISEASE_CATEGORY': disease_category,
                    'CONDITION_DATASET_COUNT': len(condition_data['projectId'].unique()),
                    'TOTAL_TRANSCRIPTS_IN_CONDITION': len(condition_data['transcriptId'].unique()),
                    'METRIC_TYPE': metric.upper()
                })
            
            # 按DISEASE_CATEGORY创建结果
            for category in metric_data['DISEASE_CATEGORY'].unique():
                category_data = metric_data[metric_data['DISEASE_CATEGORY'] == category]
                if category_data.empty:
                    continue
                
                if category in category_max_transcript:
                    category_max_trans = category_max_transcript[category]
                    # 计算该disease category下该transcript的表达值总和
                    category_max_expr = metric_data[(metric_data['DISEASE_CATEGORY'] == category) & 
                                                  (metric_data['transcriptId'] == category_max_trans)][metric].sum()
                else:
                    category_max_trans = None
                    category_max_expr = 0
                
                all_disease_results.append({
                    'GENE_ID': gene_id,
                    'GENE_SYMBOL': gene_symbol,
                    'DISEASE_CATEGORY': category,
                    'CATEGORY_MAX_TRANSCRIPT_ID': category_max_trans,
                    'CATEGORY_MAX_EXPRESSION': category_max_expr,
                    'CONDITION_COUNT_IN_CATEGORY': len(category_data['Condition'].unique()),
                    'TOTAL_TRANSCRIPTS_IN_CATEGORY': len(category_data['transcriptId'].unique()),
                    'METRIC_TYPE': metric.upper()
                })
    
    # 保存结果
    if all_condition_results:
        condition_df = pd.DataFrame(all_condition_results)
        condition_df = condition_df.drop_duplicates().sort_values(['GENE_ID', 'METRIC_TYPE', 'DISEASE_CATEGORY', 'CONDITION'])
        condition_df.to_csv('gene_analysis_results_by_condition.csv', index=False, encoding='utf-8')
        
        print(f"\n按CONDITION分组的结果已保存到 'gene_analysis_results_by_condition.csv'")
        print(f"CONDITION文件: {len(condition_df)} 条记录")
        print(f"涉及基因数量: {condition_df['GENE_ID'].nunique()}")
        
        # 显示CONDITION文件预览
        print(f"\nCONDITION文件预览:")
        print(condition_df.head(10).to_string(index=False))
    
    if all_disease_results:
        disease_df = pd.DataFrame(all_disease_results)
        disease_df = disease_df.drop_duplicates().sort_values(['GENE_ID', 'METRIC_TYPE', 'DISEASE_CATEGORY'])
        disease_df.to_csv('gene_analysis_results_by_disease_category.csv', index=False, encoding='utf-8')
        
        print(f"\n按DISEASE_CATEGORY分组的结果已保存到 'gene_analysis_results_by_disease_category.csv'")
        print(f"DISEASE_CATEGORY文件: {len(disease_df)} 条记录")
        print(f"涉及基因数量: {disease_df['GENE_ID'].nunique()}")
        
        # 显示DISEASE_CATEGORY文件预览
        print(f"\nDISEASE_CATEGORY文件预览:")
        print(disease_df.head(10).to_string(index=False))
    
    # 显示各指标类型的统计信息
    if all_condition_results:
        condition_df_stats = pd.DataFrame(all_condition_results)
        print(f"\n各指标类型统计:")
        metric_stats = condition_df_stats.groupby('METRIC_TYPE').agg({
            'GENE_ID': 'nunique',
            'CONDITION': 'count',
            'CONDITION_MAX_EXPRESSION': 'mean'
        }).round(3)
        metric_stats.columns = ['Unique_Genes', 'Total_Records', 'Avg_Max_Expression']
        print(metric_stats)
    
    print(f"\n所有分析完成！")

def main():
    # 1. 读取unique_gene_info.csv获取所有geneId和geneSymbol
    print("正在读取基因信息...")
    gene_info = pd.read_csv('hotmap/unique_gene_info.csv')
    gene_ids = gene_info['geneId'].tolist()
    
    print(f"共找到 {len(gene_ids)} 个基因")
    print(f"前5个基因: {gene_ids[:5]}")
    
    # 2. 处理所有基因的TE、TR、EVI数据
    process_gene_data(gene_ids)
    
    print("\n所有分析完成！")

if __name__ == "__main__":
    main()
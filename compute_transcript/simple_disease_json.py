import csv
import json
from collections import defaultdict

# 读取CSV文件
disease_conditions = defaultdict(set)

with open('project_unique_info.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        disease_category = row['Disease Category'].strip()
        condition = row['Condition'].strip()
        
        # 跳过NA值
        if disease_category != 'NA' and condition != 'NA' and disease_category and condition:
            disease_conditions[disease_category].add(condition)

# 转换为字典，并排序
result = {}
for disease_category, conditions in disease_conditions.items():
    result[disease_category] = sorted(list(conditions))

# 保存为JSON
with open('disease_conditions.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, indent=4, ensure_ascii=False)

print("✅ JSON文件已生成: disease_conditions.json")
print(f"疾病类别数量: {len(result)}")
for category, conditions in result.items():
    print(f"{category}: {len(conditions)} 个条件")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理project_unique_info.csv文件
按Disease Category分组，获取每个类别下的所有唯一Condition值
输出为JSON格式
"""

import csv
import json
from collections import defaultdict

def process_disease_conditions(input_file='project_unique_info.csv', output_file='disease_conditions.json'):
    """
    处理CSV文件，按Disease Category分组获取Condition值
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出JSON文件路径
    """
    print(f"📁 读取文件: {input_file}")
    
    # 使用defaultdict来存储每个疾病类别的条件
    disease_conditions = defaultdict(set)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            for row in reader:
                disease_category = row['Disease Category'].strip()
                condition = row['Condition'].strip()
                
                # 跳过空值和'NA'
                if disease_category and disease_category != 'NA' and condition and condition != 'NA':
                    disease_conditions[disease_category].add(condition)
        
        # 转换为普通字典，并将set转换为排序的列表
        result = {}
        for disease_category, conditions in disease_conditions.items():
            result[disease_category] = sorted(list(conditions))
        
        # 保存为JSON文件
        with open(output_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(result, jsonfile, indent=4, ensure_ascii=False)
        
        print(f"💾 结果已保存到: {output_file}")
        
        # 显示统计信息
        print(f"\n📊 统计信息:")
        print(f"   疾病类别数量: {len(result)}")
        
        total_conditions = sum(len(conditions) for conditions in result.values())
        print(f"   总条件数量: {total_conditions}")
        
        print(f"\n📋 各疾病类别的条件数量:")
        for disease_category, conditions in sorted(result.items()):
            print(f"   {disease_category}: {len(conditions)} 个条件")
        
        # 显示前几个类别的详细信息
        print(f"\n🔍 前3个疾病类别的详细信息:")
        for i, (disease_category, conditions) in enumerate(sorted(result.items())):
            if i >= 3:
                break
            print(f"\n   {disease_category}:")
            for condition in conditions[:5]:  # 只显示前5个条件
                print(f"     - {condition}")
            if len(conditions) > 5:
                print(f"     ... 还有 {len(conditions) - 5} 个条件")
        
        return result
        
    except FileNotFoundError:
        print(f"❌ 文件未找到: {input_file}")
        return None
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return None

def main():
    """主函数"""
    print("=" * 80)
    print("🧬 疾病类别-条件映射处理工具")
    print("=" * 80)
    
    # 处理文件
    result = process_disease_conditions()
    
    if result:
        print("\n" + "=" * 80)
        print("✅ 处理完成！")
        print("=" * 80)
        
        # 显示JSON文件的前几行预览
        print("\n📄 JSON文件预览:")
        print(json.dumps(dict(list(result.items())[:2]), indent=2, ensure_ascii=False))
        if len(result) > 2:
            print("...")

if __name__ == "__main__":
    main()

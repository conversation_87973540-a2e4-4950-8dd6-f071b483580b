import pandas as pd
import numpy as np

# 读取CSV文件
df = pd.read_csv('GSE_match_new.csv')
print(f'原始数据行数: {len(df):,}')

# 选择需要的列
columns_needed = ['Project ID', 'Tissue/Cell Type', 'Cell line', 'Condition', 'Disease Category']
df_selected = df[columns_needed].copy()

# 处理空值和'NA'
df_selected = df_selected.replace(['NA', ''], np.nan)

# 获取唯一的组合
df_unique = df_selected.drop_duplicates().reset_index(drop=True)
print(f'唯一组合数: {len(df_unique):,}')

# 按Project ID排序
df_unique = df_unique.sort_values('Project ID').reset_index(drop=True)

# 保存结果
df_unique.to_csv('project_unique_info.csv', index=False)
print('结果已保存到: project_unique_info.csv')

# 显示统计信息
print('\n统计信息:')
print(f'唯一Project ID数量: {df_unique["Project ID"].nunique():,}')
print(f'唯一Tissue/Cell Type数量: {df_unique["Tissue/Cell Type"].nunique():,}')
print(f'唯一Cell line数量: {df_unique["Cell line"].nunique():,}')
print(f'唯一Condition数量: {df_unique["Condition"].nunique():,}')
print(f'唯一Disease Category数量: {df_unique["Disease Category"].nunique():,}')

# 显示前几行预览
print('\n前5行预览:')
print(df_unique.head(5))

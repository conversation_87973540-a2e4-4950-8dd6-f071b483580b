#!/usr/bin/env python3
import csv
from collections import defaultdict

def add_disease_category_to_translation_indices():
    """
    为 translation_indices_results_grouped.csv 添加 Disease Category 列
    通过 project_id 从 project_unique_info.csv 查询对应的疾病类别
    """
    
    # 文件路径
    project_info_file = 'compute_transcript/project_unique_info.csv'
    translation_indices_file = 'compute_transcript/translation_indices_results_grouped.csv'
    output_file = 'compute_transcript/translation_indices_results_grouped_with_disease_category.csv'
    
    print("=== 为翻译指数结果添加疾病类别 ===")
    
    # 第一步：读取项目信息并建立映射
    print("正在读取项目信息...")
    project_to_disease = {}
    
    try:
        with open(project_info_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                project_id = row['Project ID'].strip()
                disease_category = row['Disease Category'].strip()
                
                if project_id:
                    project_to_disease[project_id] = disease_category
        
        print(f"成功读取 {len(project_to_disease):,} 个项目的疾病类别映射")
        
        # 显示一些映射示例
        print("\n项目到疾病类别映射示例:")
        for i, (project_id, disease_category) in enumerate(project_to_disease.items()):
            if i >= 5:
                break
            print(f"  {project_id} -> {disease_category}")
        
    except Exception as e:
        print(f"读取项目信息文件时出错: {e}")
        return False
    
    # 第二步：处理翻译指数文件
    print("\n正在处理翻译指数文件...")
    
    try:
        matched_count = 0
        unmatched_count = 0
        total_rows = 0
        unmatched_examples = []
        
        with open(translation_indices_file, 'r', encoding='utf-8') as f_in, \
             open(output_file, 'w', encoding='utf-8', newline='') as f_out:
            
            reader = csv.DictReader(f_in)
            
            # 检查是否有 project_id 列
            if 'project_id' not in reader.fieldnames:
                print(f"错误: 输入文件中没有找到 'project_id' 列")
                print(f"可用的列: {reader.fieldnames}")
                return False
            
            # 创建新的列名（添加 Disease Category）
            new_headers = list(reader.fieldnames) + ['Disease Category']
            writer = csv.DictWriter(f_out, fieldnames=new_headers)
            writer.writeheader()
            
            # 处理每一行
            for row in reader:
                total_rows += 1
                project_id = row['project_id'].strip()
                
                # 查找对应的疾病类别
                if project_id in project_to_disease:
                    disease_category = project_to_disease[project_id]
                    matched_count += 1
                else:
                    disease_category = ''
                    unmatched_count += 1
                    if len(unmatched_examples) < 5:  # 只保存前5个未匹配的例子
                        unmatched_examples.append(project_id)
                
                # 添加疾病类别到行数据
                row['Disease Category'] = disease_category
                writer.writerow(row)
                
                # 进度显示
                if total_rows % 100000 == 0:
                    print(f"  已处理 {total_rows:,} 行...")
        
        # 输出统计信息
        print(f"\n处理完成!")
        print(f"总行数: {total_rows:,}")
        print(f"成功匹配: {matched_count:,}")
        print(f"未匹配: {unmatched_count:,}")
        print(f"匹配率: {matched_count/total_rows*100:.2f}%")
        
        if unmatched_examples:
            print(f"\n未匹配的项目ID示例:")
            for project_id in unmatched_examples:
                print(f"  - {project_id}")
        
        print(f"\n结果已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"处理翻译指数文件时出错: {e}")
        return False

def verify_results():
    """验证处理结果"""
    
    output_file = 'compute_transcript/translation_indices_results_grouped_with_disease_category.csv'
    
    print("\n=== 验证处理结果 ===")
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"新文件的列名 (共 {len(headers)} 列):")
            for i, header in enumerate(headers):
                print(f"  {i+1}. {header}")
            
            # 检查前几行
            print(f"\n前5行数据示例:")
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n行 {i+1}:")
                print(f"  transcript_id: {row['transcript_id']}")
                print(f"  project_id: {row['project_id']}")
                print(f"  Condition: {row['Condition']}")
                print(f"  Disease Category: {row['Disease Category']}")
                print(f"  TE: {row['TE']}")
            
            # 统计疾病类别分布
            f.seek(0)  # 重置文件指针
            reader = csv.DictReader(f)
            next(reader)  # 跳过表头
            
            disease_category_counts = defaultdict(int)
            total_count = 0
            empty_category_count = 0
            
            for row in reader:
                total_count += 1
                disease_category = row['Disease Category'].strip()
                
                if disease_category:
                    disease_category_counts[disease_category] += 1
                else:
                    empty_category_count += 1
            
            print(f"\n统计信息:")
            print(f"总行数: {total_count:,}")
            print(f"空疾病类别: {empty_category_count:,}")
            print(f"有效疾病类别: {total_count - empty_category_count:,}")
            print(f"有效疾病类别比例: {(total_count - empty_category_count)/total_count*100:.2f}%")
            
            print(f"\n疾病类别分布:")
            for disease_category, count in sorted(disease_category_counts.items()):
                percentage = count / total_count * 100
                print(f"  {disease_category}: {count:,} ({percentage:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"验证结果时出错: {e}")
        return False

def main():
    """主函数"""
    
    # 添加疾病类别
    if add_disease_category_to_translation_indices():
        # 验证结果
        verify_results()
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()

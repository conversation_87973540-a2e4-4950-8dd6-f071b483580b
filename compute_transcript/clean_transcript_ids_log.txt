=== 清理转录本ID格式 ===

正在处理文件: /Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median.csv
  清理示例 1: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 2: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 3: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 4: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 5: '('ENST00000000233',)' -> 'ENST00000000233'
  总行数: 1,343,512
  清理的ID数量: 1,343,512
  输出文件: /Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median_cleaned.csv

正在处理文件: /Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median_with_genes.csv
  清理示例 1: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 2: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 3: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 4: '('ENST00000000233',)' -> 'ENST00000000233'
  清理示例 5: '('ENST00000000233',)' -> 'ENST00000000233'
  总行数: 1,343,512
  清理的ID数量: 1,343,512
  输出文件: /Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median_with_genes_cleaned.csv

=== 处理完成 ===
成功处理了 2/2 个文件
所有文件都已成功处理！

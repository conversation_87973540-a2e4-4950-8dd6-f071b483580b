#!/usr/bin/env python3
import csv
import os

def verify_median_results():
    # 验证生成的文件
    input_file = '/Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median_with_genes.csv'
    
    if not os.path.exists(input_file):
        print("错误: 生成的文件不存在")
        return
    
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        
        # 写入验证报告
        with open('/Volumes/zhy/整合的所有TPM文件/compute_transcript/median_verification_report.txt', 'w', encoding='utf-8') as report:
            report.write("=== Median文件处理验证报告 ===\n\n")
            report.write(f"新文件的列名 (共 {len(headers)} 列):\n")
            for i, header in enumerate(headers):
                report.write(f"  {i+1}. {header}\n")
            
            report.write("\n前5行数据示例:\n")
            
            total_rows = 0
            empty_gene_id = 0
            empty_gene_name = 0
            
            for i, row in enumerate(reader):
                total_rows += 1
                
                if not row['ensembl_gene_id'].strip():
                    empty_gene_id += 1
                if not row['external_gene_name'].strip():
                    empty_gene_name += 1
                
                if i < 5:
                    report.write(f"\n行 {i+1}:\n")
                    report.write(f"  transcript_id: {row['transcript_id']}\n")
                    report.write(f"  condition_category: {row['condition_category']}\n")
                    report.write(f"  variable: {row['variable']}\n")
                    report.write(f"  direction: {row['direction']}\n")
                    report.write(f"  ensembl_gene_id: {row['ensembl_gene_id']}\n")
                    report.write(f"  external_gene_name: {row['external_gene_name']}\n")
            
            # 文件大小
            file_size = os.path.getsize(input_file)
            
            report.write(f"\n=== 统计信息 ===\n")
            report.write(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)\n")
            report.write(f"总行数: {total_rows:,}\n")
            report.write(f"空的 ensembl_gene_id: {empty_gene_id:,}\n")
            report.write(f"空的 external_gene_name: {empty_gene_name:,}\n")
            report.write(f"有效基因ID的比例: {(total_rows-empty_gene_id)/total_rows*100:.2f}%\n")
            report.write(f"有效基因名称的比例: {(total_rows-empty_gene_name)/total_rows*100:.2f}%\n")
            
            report.write("\n=== 与原始日志对比 ===\n")
            report.write("原始处理日志显示:\n")
            report.write("- 总行数: 1,343,512\n")
            report.write("- 成功匹配: 1,343,512\n")
            report.write("- 未匹配: 0\n")
            report.write("- 匹配率: 100.00%\n")
            
            if total_rows == 1343512:
                report.write("✓ 行数匹配正确\n")
            else:
                report.write(f"⚠ 行数不匹配: 期望 1,343,512，实际 {total_rows}\n")
            
            report.write("\n处理完成！\n")
    
    print("验证报告已生成: compute_transcript/median_verification_report.txt")

if __name__ == "__main__":
    verify_median_results()

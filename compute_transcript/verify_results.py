#!/usr/bin/env python3
import csv

def verify_results():
    # 验证生成的文件
    with open('/Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_by_disease_category_with_genes.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        
        # 写入验证报告
        with open('/Volumes/zhy/整合的所有TPM文件/compute_transcript/verification_report.txt', 'w', encoding='utf-8') as report:
            report.write("=== 文件处理验证报告 ===\n\n")
            report.write(f"新文件的列名 (共 {len(headers)} 列):\n")
            for i, header in enumerate(headers):
                report.write(f"  {i+1}. {header}\n")
            
            report.write("\n前5行数据示例:\n")
            
            total_rows = 0
            empty_gene_id = 0
            empty_gene_name = 0
            
            for i, row in enumerate(reader):
                total_rows += 1
                
                if not row['ensembl_gene_id'].strip():
                    empty_gene_id += 1
                if not row['external_gene_name'].strip():
                    empty_gene_name += 1
                
                if i < 5:
                    report.write(f"\n行 {i+1}:\n")
                    report.write(f"  transcript_id: {row['transcript_id']}\n")
                    report.write(f"  disease_category: {row['disease_category']}\n")
                    report.write(f"  variable: {row['variable']}\n")
                    report.write(f"  ensembl_gene_id: {row['ensembl_gene_id']}\n")
                    report.write(f"  external_gene_name: {row['external_gene_name']}\n")
            
            report.write(f"\n=== 统计信息 ===\n")
            report.write(f"总行数: {total_rows}\n")
            report.write(f"空的 ensembl_gene_id: {empty_gene_id}\n")
            report.write(f"空的 external_gene_name: {empty_gene_name}\n")
            report.write(f"有效基因ID的比例: {(total_rows-empty_gene_id)/total_rows*100:.2f}%\n")
            report.write(f"有效基因名称的比例: {(total_rows-empty_gene_name)/total_rows*100:.2f}%\n")
            
            report.write("\n处理完成！\n")
    
    print("验证报告已生成: compute_transcript/verification_report.txt")

if __name__ == "__main__":
    verify_results()

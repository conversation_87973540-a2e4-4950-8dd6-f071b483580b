#!/usr/bin/env python3
import csv
import re
from collections import defaultdict

def clean_transcript_id(transcript_id_str):
    """清理转录本ID格式，从 "('ENST00000000233',)" 提取 "ENST00000000233" """
    # 使用正则表达式提取ENST ID
    match = re.search(r"ENST\d+", transcript_id_str)
    if match:
        return match.group(0)
    else:
        # 如果没有匹配到，返回原始字符串去除引号和括号
        return transcript_id_str.strip("()\"' ")

def add_gene_info_median():
    # 首先读取 Trans_gene_name.csv 建立 Transcriptid 到基因信息的映射
    transcript_to_gene = {}
    
    print("正在读取 Trans_gene_name.csv...")
    with open('/Volumes/zhy/整合的所有TPM文件/compute_transcript/Trans_gene_name.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            transcript_id = row['Transcriptid'].strip()
            ensembl_gene_id = row['ensembl_gene_id'].strip()
            external_gene_name = row['external_gene_name'].strip()
            
            transcript_to_gene[transcript_id] = {
                'ensembl_gene_id': ensembl_gene_id,
                'external_gene_name': external_gene_name
            }
    
    print(f"共读取了 {len(transcript_to_gene)} 个转录本的基因信息")
    
    # 读取目标文件并查看结构
    print("正在读取 transcript_statistical_analysis_results_median.csv...")
    
    input_file = '/Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median.csv'
    output_file = '/Volumes/zhy/整合的所有TPM文件/compute_transcript/transcript_statistical_analysis_results_median_with_genes.csv'
    
    # 先查看文件结构
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)
        print(f"原始文件列名: {headers}")
        
        # 显示前几行数据
        print("前3行数据:")
        for i, row in enumerate(reader):
            print(f"  行 {i+1}: {row}")
            if i >= 2:
                break
    
    # 检查是否有 transcript_id 列
    transcript_col = None
    if 'transcript_id' in headers:
        transcript_col = 'transcript_id'
    elif 'Transcriptid' in headers:
        transcript_col = 'Transcriptid'
    else:
        print("错误: 文件中没有找到 'transcript_id' 或 'Transcriptid' 列")
        print(f"可用的列: {headers}")
        return
    
    # 处理文件并添加基因信息
    matched_count = 0
    unmatched_count = 0
    total_rows = 0
    unmatched_examples = []
    
    with open(input_file, 'r', encoding='utf-8') as f_in, \
         open(output_file, 'w', encoding='utf-8', newline='') as f_out:
        
        reader = csv.DictReader(f_in)
        
        # 创建新的列名
        new_headers = list(reader.fieldnames) + ['ensembl_gene_id', 'external_gene_name']
        writer = csv.DictWriter(f_out, fieldnames=new_headers)
        writer.writeheader()
        
        for row in reader:
            total_rows += 1
            raw_transcript_id = row[transcript_col].strip()
            
            # 清理转录本ID格式
            clean_transcript_id_val = clean_transcript_id(raw_transcript_id)
            
            # 查找基因信息
            if clean_transcript_id_val in transcript_to_gene:
                gene_info = transcript_to_gene[clean_transcript_id_val]
                row['ensembl_gene_id'] = gene_info['ensembl_gene_id']
                row['external_gene_name'] = gene_info['external_gene_name']
                matched_count += 1
            else:
                row['ensembl_gene_id'] = ''
                row['external_gene_name'] = ''
                unmatched_count += 1
                if len(unmatched_examples) < 5:  # 只保存前5个未匹配的例子
                    unmatched_examples.append((raw_transcript_id, clean_transcript_id_val))
            
            writer.writerow(row)
    
    print(f"\n处理完成!")
    print(f"总行数: {total_rows}")
    print(f"成功匹配: {matched_count}")
    print(f"未匹配: {unmatched_count}")
    print(f"匹配率: {matched_count/total_rows*100:.2f}%")
    
    if unmatched_examples:
        print("\n未匹配的例子:")
        for raw_id, clean_id in unmatched_examples:
            print(f"  原始: {raw_id} -> 清理后: {clean_id}")
    
    print(f"结果已保存到: {output_file}")

if __name__ == "__main__":
    add_gene_info_median()

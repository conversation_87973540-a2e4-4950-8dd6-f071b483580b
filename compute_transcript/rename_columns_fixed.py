#!/usr/bin/env python3
"""
修改translation_indices_results_grouped.csv文件的表头
按照指定的映射关系重命名列（修正版本）
"""

import pandas as pd
import os

def main():
    input_file = "translation_indices_results_grouped.csv"
    output_file = "translation_indices_results_grouped_renamed.csv"
    
    # 列名映射关系
    column_mapping = {
        'transcript_id': 'transcriptId',
        'project_id': 'projectId',
        'bioproject_id': 'bioprojectId',
        'Tissue/Cell Type': 'tissueCellType',
        'Cell line': 'cellLine',
        'TR': 'tr',
        'EVI': 'evi',
        'TE': 'te',
        'ensembl_gene_id': 'geneId',
        'external_gene_name': 'geneSymbol'
    }
    
    print("开始修改表头...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    try:
        # 先读取第一行来获取列名
        print("读取文件头部获取列名...")
        header_df = pd.read_csv(input_file, nrows=0)
        original_columns = list(header_df.columns)
        print("原始列名:", original_columns)
        
        # 检查是否所有需要的列都存在
        missing_cols = [old for old in column_mapping.keys() if old not in original_columns]
        if missing_cols:
            print(f"警告: 以下列不存在: {missing_cols}")
        
        # 创建新的列名列表
        new_columns = []
        for col in original_columns:
            if col in column_mapping:
                new_columns.append(column_mapping[col])
            else:
                new_columns.append(col)
        
        print("新的列名:", new_columns)
        
        # 读取文件（分块处理大文件）
        print("读取文件并重命名列...")
        chunk_size = 100000  # 每次读取10万行
        chunks = []
        
        for i, chunk in enumerate(pd.read_csv(input_file, chunksize=chunk_size, low_memory=False)):
            print(f"处理第 {i+1} 个数据块，包含 {len(chunk)} 行...")
            
            # 重命名列
            chunk.columns = new_columns
            
            chunks.append(chunk)
        
        # 合并所有数据块
        print("合并所有数据块...")
        result_df = pd.concat(chunks, ignore_index=True)
        
        # 保存结果
        print(f"保存结果到 {output_file}...")
        result_df.to_csv(output_file, index=False)
        
        print("处理完成！")
        print(f"结果文件包含 {len(result_df)} 行数据")
        print(f"最终列名: {list(result_df.columns)}")
        
        # 显示前几行数据验证
        print("\n前5行数据:")
        print(result_df.head())
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
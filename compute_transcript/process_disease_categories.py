#!/usr/bin/env python3
import csv
import json
from collections import defaultdict

def process_disease_categories():
    # 读取 CSV 文件
    data = []
    with open('compute_transcript/project_unique_info.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        print("列名:", headers)

        for row in reader:
            data.append(row)

    print(f"共读取了 {len(data)} 行数据")
    print("前几行数据:")
    for i, row in enumerate(data[:3]):
        print(f"  行 {i+1}: {row}")

    # 获取 Disease Category 列的唯一值
    disease_categories = set()
    for row in data:
        disease_categories.add(row['Disease Category'])

    print("\n唯一的 Disease Category 值:")
    for cat in sorted(disease_categories):
        print(f"  - {cat}")

    # 创建字典来存储每个 Disease Category 对应的 Condition 值
    result = defaultdict(set)

    for row in data:
        disease_category = row['Disease Category']
        condition = row['Condition']

        # 处理 Condition 中可能包含多个疾病的情况（用分号分隔）
        conditions = [c.strip() for c in condition.split(';')]

        for cond in conditions:
            if cond:  # 确保不是空字符串
                result[disease_category].add(cond)

    # 转换为普通字典，并将 set 转换为排序的列表
    final_result = {}
    for category, conditions in result.items():
        final_result[category] = sorted(list(conditions))

    # 保存为 JSON 文件
    with open('compute_transcript/disease_categories.json', 'w', encoding='utf-8') as f:
        json.dump(final_result, f, indent=4, ensure_ascii=False)

    print(f"\n结果已保存到 compute_transcript/disease_categories.json")
    print(f"共处理了 {len(final_result)} 个疾病类别")

    # 打印结果预览
    print("\n结果预览:")
    for category, conditions in final_result.items():
        print(f"\n{category}: ({len(conditions)} 个条件)")
        for condition in conditions[:5]:  # 只显示前5个
            print(f"  - {condition}")
        if len(conditions) > 5:
            print(f"  ... 还有 {len(conditions) - 5} 个")

if __name__ == "__main__":
    process_disease_categories()

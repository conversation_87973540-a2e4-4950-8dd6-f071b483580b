import pandas as pd
import csv

def count_genes_by_project():
    """
    统计translation_indices_results_grouped_filtered.csv文件中
    每个ensembl_gene_id在每个project_id中的出现次数
    """
    print("正在读取文件...")
    
    # 读取CSV文件
    df = pd.read_csv('translation_indices_results_grouped.csv')
    print(f"文件总行数: {len(df)}")
    
    # 显示列名
    print("文件列名:", df.columns.tolist())
    
    # 检查数据质量
    print("\nensembl_gene_id和external_gene_name的唯一组合数:")
    unique_gene_combinations = df[['ensembl_gene_id', 'external_gene_name']].drop_duplicates()
    print(f"唯一组合数: {len(unique_gene_combinations)}")
    
    # 检查是否有重复的ensembl_gene_id对应不同的external_gene_name
    gene_id_mapping = df.groupby('ensembl_gene_id')['external_gene_name'].nunique()
    if (gene_id_mapping > 1).any():
        print("警告: 发现ensembl_gene_id对应多个external_gene_name的情况")
        problematic_ids = gene_id_mapping[gene_id_mapping > 1]
        print(f"问题ID数量: {len(problematic_ids)}")
    else:
        print("确认: ensembl_gene_id和external_gene_name是一一对应的")
    
    print("\n正在统计每个基因ID在每个项目中的出现次数...")
    
    # 按project_id和ensembl_gene_id分组，计算每个基因ID在每个项目中的出现次数
    result = df.groupby(['project_id', 'ensembl_gene_id']).size().reset_index(name='count')
    
    # 添加external_gene_name信息
    gene_name_mapping = df[['ensembl_gene_id', 'external_gene_name']].drop_duplicates()
    result = result.merge(gene_name_mapping, on='ensembl_gene_id', how='left')
    
    # 重新排列列的顺序
    result = result[['ensembl_gene_id', 'external_gene_name', 'project_id', 'count']]
    
    # 按ensembl_gene_id排序
    result = result.sort_values(['ensembl_gene_id', 'project_id'])
    
    print(f"统计结果总行数: {len(result)}")
    print(f"唯一基因ID数: {result['ensembl_gene_id'].nunique()}")
    print(f"涉及项目数: {result['project_id'].nunique()}")
    
    # 显示统计信息
    print("\n统计概览:")
    print(f"平均每个基因ID-项目组合的转录本数: {result['count'].mean():.2f}")
    print(f"最大转录本数: {result['count'].max()}")
    print(f"最小转录本数: {result['count'].min()}")
    
    # 保存结果
    output_file = 'gene_count_by_project_results.csv'
    result.to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    # 显示前几行结果
    print("\n结果示例(前10行):")
    print(result.head(10).to_string(index=False))
    
    # 显示一些统计信息
    print(f"\n各项目的基因ID数量:")
    project_gene_counts = result.groupby('project_id')['ensembl_gene_id'].nunique().sort_values(ascending=False)
    print(project_gene_counts.head(10).to_string())
    
    print(f"\n出现在最多项目中的基因ID(前10个):")
    gene_project_counts = result.groupby('ensembl_gene_id')['project_id'].nunique().sort_values(ascending=False)
    print(gene_project_counts.head(10).to_string())
    
    return result

if __name__ == "__main__":
    result = count_genes_by_project() 
#!/usr/bin/env python3
import csv
import re
import os

def clean_transcript_id(transcript_id_str):
    """清理转录本ID格式，从 "('ENST00000000233',)" 提取 "ENST00000000233" """
    # 使用正则表达式提取ENST ID
    match = re.search(r"ENST\d+", transcript_id_str)
    if match:
        return match.group(0)
    else:
        # 如果没有匹配到，返回原始字符串去除引号和括号
        return transcript_id_str.strip("()\"', ")

def process_file(input_file, output_file):
    """处理单个文件，清理transcript_id列"""
    
    print(f"正在处理文件: {input_file}")
    
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 - {input_file}")
        return False
    
    # 统计信息
    total_rows = 0
    cleaned_count = 0
    
    with open(input_file, 'r', encoding='utf-8') as f_in, \
         open(output_file, 'w', encoding='utf-8', newline='') as f_out:
        
        reader = csv.DictReader(f_in)
        headers = reader.fieldnames
        
        # 检查是否有transcript_id列
        if 'transcript_id' not in headers:
            print(f"错误: 文件中没有找到 'transcript_id' 列")
            print(f"可用的列: {headers}")
            return False
        
        writer = csv.DictWriter(f_out, fieldnames=headers)
        writer.writeheader()
        
        # 显示前几个原始ID的例子
        examples_shown = 0
        
        for row in reader:
            total_rows += 1
            original_id = row['transcript_id']
            cleaned_id = clean_transcript_id(original_id)
            
            # 显示前5个清理的例子
            if examples_shown < 5 and original_id != cleaned_id:
                print(f"  清理示例 {examples_shown + 1}: '{original_id}' -> '{cleaned_id}'")
                examples_shown += 1
            
            if original_id != cleaned_id:
                cleaned_count += 1
            
            row['transcript_id'] = cleaned_id
            writer.writerow(row)
    
    print(f"  总行数: {total_rows:,}")
    print(f"  清理的ID数量: {cleaned_count:,}")
    print(f"  输出文件: {output_file}")
    print()
    
    return True

def main():
    """主函数，处理两个文件"""
    
    base_path = '/Volumes/zhy/整合的所有TPM文件/compute_transcript'
    
    # 定义要处理的文件
    files_to_process = [
        {
            'input': f'{base_path}/transcript_statistical_analysis_results_median.csv',
            'output': f'{base_path}/transcript_statistical_analysis_results_median_cleaned.csv'
        },
        {
            'input': f'{base_path}/transcript_statistical_analysis_results_median_with_genes.csv',
            'output': f'{base_path}/transcript_statistical_analysis_results_median_with_genes_cleaned.csv'
        }
    ]
    
    print("=== 清理转录本ID格式 ===\n")
    
    success_count = 0
    
    for file_info in files_to_process:
        if process_file(file_info['input'], file_info['output']):
            success_count += 1
    
    print(f"=== 处理完成 ===")
    print(f"成功处理了 {success_count}/{len(files_to_process)} 个文件")
    
    if success_count == len(files_to_process):
        print("所有文件都已成功处理！")
    else:
        print("部分文件处理失败，请检查错误信息。")

if __name__ == "__main__":
    main()

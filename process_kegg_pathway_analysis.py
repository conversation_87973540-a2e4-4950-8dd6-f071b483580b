#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KEGG通路分析脚本 - 内存优化版
处理KEGG_final.csv和transcript_quartile_analysis_results.csv文件
按照Pathway_Description分组，为每个通路创建单独的文件（优先xlsx，失败则csv）
使用流式处理减少内存占用
"""

import pandas as pd
import numpy as np
import os
import re
from pathlib import Path
import time
import gc

def save_dataframe(df, filepath, filename_base):
    """
    尝试保存DataFrame，优先保存为xlsx，失败则保存为csv
    
    Args:
        df: DataFrame要保存的数据
        filepath: str, 文件路径（不含扩展名）
        filename_base: str, 基础文件名
        
    Returns:
        str: 实际保存的文件名
    """
    # 尝试保存为xlsx
    try:
        xlsx_path = f"{filepath}.xlsx"
        df.to_excel(xlsx_path, index=False, engine='openpyxl')
        return f"{filename_base}.xlsx"
    except Exception as e:
        print(f"    xlsx保存失败: {e}，尝试保存为csv")
        # 保存为csv
        try:
            csv_path = f"{filepath}.csv"
            df.to_csv(csv_path, index=False, encoding='utf-8')
            return f"{filename_base}.csv"
        except Exception as e2:
            print(f"    csv保存也失败: {e2}")
            return None

def clean_filename(filename):
    # 替换不允许的字符为下划线
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除多余的空格和特殊字符
    filename = re.sub(r'[^\w\s-]', '_', filename)
    # 替换多个连续的下划线为单个下划线
    filename = re.sub(r'_+', '_', filename)
    # 移除首尾的下划线和空格
    filename = filename.strip('_ ')
    return filename

def process_kegg_pathway_data():
    """
    处理KEGG通路数据 - 内存优化版
    使用流式处理减少内存占用
    """
    # 文件路径
    kegg_file = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_final.csv"
    transcript_file = "/Volumes/zhy/整合的所有TPM文件/process_transcript/transcript_quartile_analysis_results.csv"
    output_dir = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_pathway_results"
    
    start_time = time.time()
    print("正在读取数据文件...")
    
    try:
        # 分块读取KEGG数据
        print("分块读取KEGG数据...")
        kegg_chunks = []
        chunk_size = 10000
        for chunk in pd.read_csv(kegg_file, chunksize=chunk_size):
            kegg_chunks.append(chunk)
        kegg_df = pd.concat(kegg_chunks, ignore_index=True)
        print(f"成功读取KEGG数据，共 {len(kegg_df)} 行")
        
        # 清理chunk数据
        del kegg_chunks
        gc.collect()
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        print(f"输出目录已创建: {output_dir}")
        
        # 预定义结果列
        result_columns = [
            'Pathway_Description', 'transcriptId', 'projectId', 'geneId', 'geneSymbol',
            'te_Level', 'tr_Level', 'evi_Level',
            'te_log2_zscore', 'tr_log2_zscore', 'evi_log2_zscore'
        ]
        
        # 按Pathway_Description分组
        pathway_groups = kegg_df.groupby('Pathway_Description')
        print(f"\n共找到 {len(pathway_groups)} 个不同的通路")
        
        # 汇总信息列表
        summary_data = []
        
        # 逐个处理通路，避免在内存中累积大量数据
        for i, (pathway_name, pathway_group) in enumerate(pathway_groups):
            print(f"处理通路 ({i+1}/{len(pathway_groups)}): {pathway_name}")
            
            # 获取该通路下的所有Gene_ID（去重）
            gene_ids = set(pathway_group['Gene_ID'])
            print(f"  该通路包含 {len(gene_ids)} 个基因")
            
            try:
                # 分块读取转录本数据并筛选匹配项
                matched_transcripts_list = []
                transcript_chunk_size = 5000
                
                print("  正在分块处理转录本数据...")
                for chunk in pd.read_csv(transcript_file, chunksize=transcript_chunk_size):
                    # 筛选匹配的基因ID
                    matched_chunk = chunk[chunk['geneId'].isin(gene_ids)]
                    if not matched_chunk.empty:
                        matched_transcripts_list.append(matched_chunk)
                
                if not matched_transcripts_list:
                    print(f"  警告: 通路 {pathway_name} 没有找到匹配的转录本数据")
                    continue
                
                # 合并匹配的转录本数据
                matched_transcripts = pd.concat(matched_transcripts_list, ignore_index=True)
                print(f"  找到匹配的转录本数据 {len(matched_transcripts)} 行")
                
                # 添加Pathway_Description列
                matched_transcripts['Pathway_Description'] = pathway_name
                
                # 选择需要的列
                result_df = matched_transcripts[result_columns]
                
                # 立即保存文件
                clean_pathway_name = clean_filename(pathway_name)
                output_path_base = os.path.join(output_dir, clean_pathway_name)
                saved_filename = save_dataframe(result_df, output_path_base, clean_pathway_name)
                
                if saved_filename:
                    print(f"  已保存: {saved_filename}")
                    
                    # 收集汇总信息
                    summary_data.append({
                        'Pathway_Description': pathway_name,
                        'Gene_Count': len(gene_ids),
                        'Transcript_Count': len(matched_transcripts),
                        'Output_File': saved_filename
                    })
                else:
                    print(f"  保存失败: {pathway_name}")
                
                # 清理当前处理的数据
                del matched_transcripts_list, matched_transcripts, result_df
                gc.collect()
                
            except Exception as e:
                print(f"  处理通路 {pathway_name} 时出错: {e}")
                continue
        
        # 创建汇总文件
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_path_base = os.path.join(output_dir, "pathway_summary")
            summary_filename = save_dataframe(summary_df, summary_path_base, "pathway_summary")
            
            if summary_filename:
                print(f"\n汇总文件已保存到: {summary_filename}")
            
            # 显示汇总统计
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"\n处理结果汇总:")
            print(f"处理时间: {processing_time:.2f} 秒")
            print(f"成功处理的通路数量: {len([s for s in summary_data if s['Output_File'] != 'pending'])}")
            print(f"总基因数量: {summary_df['Gene_Count'].sum()}")
            print(f"总转录本数量: {summary_df['Transcript_Count'].sum()}")
            
            # 显示前10个通路的转录本数量
            top_pathways = summary_df.nlargest(10, 'Transcript_Count')
            print(f"\n转录本数量最多的前10个通路:")
            for _, row in top_pathways.iterrows():
                print(f"  {row['Pathway_Description']}: {row['Transcript_Count']} 个转录本")
        
        print(f"\n处理完成！所有结果文件已保存到: {output_dir}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始处理KEGG通路数据...")
    try:
        process_kegg_pathway_data()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()
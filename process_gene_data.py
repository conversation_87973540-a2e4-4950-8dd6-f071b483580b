import pandas as pd

# 读取两个CSV文件
print("读取文件中...")
gene_count_df = pd.read_csv('process_gene/gene_count_by_project_results_cleaned.csv')
gene_info_df = pd.read_csv('process_gene/ensembl_gene_id_with_full_info_split_2.csv')

print(f"gene_count文件行数: {len(gene_count_df)}")
print(f"gene_info文件行数: {len(gene_info_df)}")

# 查看文件列名
print("\ngene_count文件列名:", gene_count_df.columns.tolist())
print("gene_info文件列名:", gene_info_df.columns.tolist())

# 发现geneSymbol列实际存储的是ensembl_gene_id，需要使用ensembl_gene_id进行匹配
print("\n创建chromosome映射...")
ensembl_to_chromosome = dict(zip(gene_info_df['ensembl_gene_id'], gene_info_df['Chromosome']))

# 添加chromosome列到gene_count_df，使用geneSymbol列（实际是ensembl_gene_id）进行匹配
gene_count_df['chromosome'] = gene_count_df['geneSymbol'].map(ensembl_to_chromosome)

# 检查匹配情况
matched = gene_count_df['chromosome'].notna().sum()
total = len(gene_count_df)
print(f"\n匹配情况: {matched}/{total} ({matched/total*100:.2f}%)")

# 查看一些未匹配的示例
unmatched = gene_count_df[gene_count_df['chromosome'].isna()]
if len(unmatched) > 0:
    print(f"\n未匹配的基因符号示例 (前10个):")
    print(unmatched['geneSymbol'].head(10).tolist())

# 保存结果
output_path = 'process_gene/gene_count_by_project_results_with_chromosome.csv'
gene_count_df.to_csv(output_path, index=False)
print(f"\n结果已保存到: {output_path}")

# 显示前几行结果
print("\n处理后数据前5行:")
print(gene_count_df.head())
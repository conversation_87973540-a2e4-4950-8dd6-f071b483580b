#!/usr/bin/env python3
"""
脚本功能：
删除Gene文件夹中那些样本ID不在Sample.csv中的文件
为了安全起见，会先列出要删除的文件，然后询问用户确认
"""

import os
import csv
import shutil

def extract_sample_id(filename):
    """从文件名中提取第一个'_'前面的样本ID"""
    if filename.endswith('.txt'):
        return filename.split('_')[0]
    return None

def get_sample_csv_ids(csv_file='Sample.csv'):
    """从Sample.csv文件的第一列读取样本ID"""
    sample_ids = set()
    
    print(f"正在从 {csv_file} 中读取样本ID...")
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)  # 跳过表头
            print(f"  第一列表头: {header[0]}")
            
            for row in reader:
                if row and row[0]:  # 确保行不为空且第一列有值
                    # 移除可能的引号
                    sample_id = row[0].strip().strip('"')
                    sample_ids.add(sample_id)
            
        print(f"  从Sample.csv中读取到 {len(sample_ids)} 个样本ID")
        
    except FileNotFoundError:
        print(f"  错误: 找不到文件 {csv_file}")
        return set()
    except Exception as e:
        print(f"  错误: 读取文件时出现问题 - {e}")
        return set()
    
    return sample_ids

def find_files_to_delete(target_folder='Gene'):
    """找出需要删除的文件"""
    if not os.path.exists(target_folder):
        print(f"错误: {target_folder} 文件夹不存在")
        return []
    
    # 获取Sample.csv中的样本ID
    sample_csv_ids = get_sample_csv_ids()
    if not sample_csv_ids:
        print("无法读取Sample.csv文件，程序退出")
        return []
    
    files_to_delete = []
    total_files = 0
    
    print(f"\n正在扫描 {target_folder} 文件夹...")

    for filename in os.listdir(target_folder):
        if filename.endswith('.txt'):
            total_files += 1
            sample_id = extract_sample_id(filename)
            if sample_id and sample_id not in sample_csv_ids:
                file_path = os.path.join(target_folder, filename)
                files_to_delete.append((filename, sample_id, file_path))
    
    print(f"  总文件数: {total_files}")
    print(f"  需要删除的文件数: {len(files_to_delete)}")
    
    return files_to_delete

def create_backup_list(files_to_delete, target_folder='Gene'):
    """创建要删除文件的备份列表"""
    backup_file = f'files_to_delete_backup_list_{target_folder.lower()}.txt'
    
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write("要删除的文件列表 (备份记录)\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {__import__('datetime').datetime.now()}\n")
        f.write(f"总文件数: {len(files_to_delete)}\n\n")
        
        for filename, sample_id, file_path in files_to_delete:
            f.write(f"{filename} (样本ID: {sample_id})\n")
    
    print(f"已创建备份列表: {backup_file}")

def delete_files(files_to_delete, target_folder='Gene', create_backup=True):
    """删除文件"""
    if not files_to_delete:
        print("没有需要删除的文件")
        return

    # 创建备份列表
    if create_backup:
        create_backup_list(files_to_delete, target_folder)
    
    print(f"\n开始删除 {len(files_to_delete)} 个文件...")
    
    deleted_count = 0
    error_count = 0
    
    for filename, sample_id, file_path in files_to_delete:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                deleted_count += 1
                if deleted_count <= 10:  # 只显示前10个删除的文件
                    print(f"  已删除: {filename}")
                elif deleted_count == 11:
                    print("  ...")
            else:
                print(f"  警告: 文件不存在 {filename}")
                error_count += 1
        except Exception as e:
            print(f"  错误: 无法删除 {filename} - {e}")
            error_count += 1
    
    print(f"\n删除完成:")
    print(f"  成功删除: {deleted_count} 个文件")
    if error_count > 0:
        print(f"  删除失败: {error_count} 个文件")

def main():
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        target_folder = sys.argv[1]
    else:
        target_folder = 'Gene'  # 默认处理Gene文件夹

    print(f"{target_folder}文件夹清理工具")
    print("=" * 50)
    print(f"此工具将删除{target_folder}文件夹中那些样本ID不在Sample.csv中的文件")

    # 找出需要删除的文件
    files_to_delete = find_files_to_delete(target_folder)
    
    if not files_to_delete:
        print(f"\n没有需要删除的文件，所有{target_folder}文件夹中的样本ID都在Sample.csv中")
        return
    
    # 显示前10个要删除的文件作为示例
    print(f"\n前10个要删除的文件示例:")
    for i, (filename, sample_id, file_path) in enumerate(files_to_delete[:10]):
        print(f"  {i+1}. {filename} (样本ID: {sample_id})")
    
    if len(files_to_delete) > 10:
        print(f"  ... 还有 {len(files_to_delete) - 10} 个文件")
    
    # 询问用户确认
    print(f"\n警告: 即将删除 {len(files_to_delete)} 个文件!")
    print("删除前会创建备份列表文件")
    
    while True:
        confirm = input("\n确定要删除这些文件吗? (yes/no): ").lower().strip()
        if confirm in ['yes', 'y']:
            delete_files(files_to_delete, target_folder)
            break
        elif confirm in ['no', 'n']:
            print("操作已取消")
            break
        else:
            print("请输入 'yes' 或 'no'")

if __name__ == "__main__":
    main()

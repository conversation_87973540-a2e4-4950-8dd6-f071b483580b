#!/usr/bin/env python3
"""
检查Gene和Transcript文件夹与Sample.csv的对应关系
验证是否一一对应
"""

import os
import csv

def extract_sample_id(filename):
    """从文件名中提取第一个'_'前面的样本ID"""
    if filename.endswith('.txt'):
        return filename.split('_')[0]
    return None

def get_sample_ids_from_folder(folder_path):
    """获取指定文件夹下所有txt文件的样本ID"""
    sample_ids = set()
    if os.path.exists(folder_path):
        for filename in os.listdir(folder_path):
            if filename.endswith('.txt'):
                sample_id = extract_sample_id(filename)
                if sample_id:
                    sample_ids.add(sample_id)
    return sample_ids

def get_sample_csv_ids(csv_file='Sample.csv'):
    """从Sample.csv文件的第一列读取样本ID"""
    sample_ids = set()
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)  # 跳过表头
            
            for row in reader:
                if row and row[0]:  # 确保行不为空且第一列有值
                    # 移除可能的引号
                    sample_id = row[0].strip().strip('"')
                    sample_ids.add(sample_id)
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file}")
        return set()
    except Exception as e:
        print(f"错误: 读取文件时出现问题 - {e}")
        return set()
    
    return sample_ids

def main():
    print("Gene和Transcript文件夹与Sample.csv对应关系检查")
    print("=" * 60)
    
    # 获取Sample.csv中的样本ID
    sample_csv_ids = get_sample_csv_ids()
    print(f"Sample.csv中的样本ID数量: {len(sample_csv_ids)}")
    
    # 获取Gene文件夹中的样本ID
    gene_sample_ids = get_sample_ids_from_folder('Gene')
    print(f"Gene文件夹中的样本ID数量: {len(gene_sample_ids)}")
    
    # 获取Transcript文件夹中的样本ID
    transcript_sample_ids = get_sample_ids_from_folder('Transcript')
    print(f"Transcript文件夹中的样本ID数量: {len(transcript_sample_ids)}")
    
    print("\n" + "=" * 60)
    print("对应关系分析:")
    
    # 检查Gene文件夹与Sample.csv的对应关系
    print(f"\n1. Gene文件夹与Sample.csv的对应关系:")
    gene_not_in_sample = gene_sample_ids - sample_csv_ids
    sample_not_in_gene = sample_csv_ids - gene_sample_ids
    
    print(f"   - Gene中有但Sample.csv中没有的样本: {len(gene_not_in_sample)} 个")
    if gene_not_in_sample:
        print(f"     前5个示例: {list(gene_not_in_sample)[:5]}")
    
    print(f"   - Sample.csv中有但Gene中没有的样本: {len(sample_not_in_gene)} 个")
    if sample_not_in_gene:
        print(f"     前5个示例: {list(sample_not_in_gene)[:5]}")
    
    if len(gene_not_in_sample) == 0 and len(sample_not_in_gene) == 0:
        print("   ✅ Gene文件夹与Sample.csv完全一一对应！")
    else:
        print("   ❌ Gene文件夹与Sample.csv不完全对应")
    
    # 检查Transcript文件夹与Sample.csv的对应关系
    print(f"\n2. Transcript文件夹与Sample.csv的对应关系:")
    transcript_not_in_sample = transcript_sample_ids - sample_csv_ids
    sample_not_in_transcript = sample_csv_ids - transcript_sample_ids
    
    print(f"   - Transcript中有但Sample.csv中没有的样本: {len(transcript_not_in_sample)} 个")
    if transcript_not_in_sample:
        print(f"     前5个示例: {list(transcript_not_in_sample)[:5]}")
    
    print(f"   - Sample.csv中有但Transcript中没有的样本: {len(sample_not_in_transcript)} 个")
    if sample_not_in_transcript:
        print(f"     前5个示例: {list(sample_not_in_transcript)[:5]}")
    
    if len(transcript_not_in_sample) == 0 and len(sample_not_in_transcript) == 0:
        print("   ✅ Transcript文件夹与Sample.csv完全一一对应！")
    else:
        print("   ❌ Transcript文件夹与Sample.csv不完全对应")
    
    # 检查Gene和Transcript文件夹之间的对应关系
    print(f"\n3. Gene和Transcript文件夹之间的对应关系:")
    gene_not_in_transcript = gene_sample_ids - transcript_sample_ids
    transcript_not_in_gene = transcript_sample_ids - gene_sample_ids
    
    print(f"   - Gene中有但Transcript中没有的样本: {len(gene_not_in_transcript)} 个")
    if gene_not_in_transcript:
        print(f"     前5个示例: {list(gene_not_in_transcript)[:5]}")
    
    print(f"   - Transcript中有但Gene中没有的样本: {len(transcript_not_in_gene)} 个")
    if transcript_not_in_gene:
        print(f"     前5个示例: {list(transcript_not_in_gene)[:5]}")
    
    if len(gene_not_in_transcript) == 0 and len(transcript_not_in_gene) == 0:
        print("   ✅ Gene和Transcript文件夹完全一一对应！")
    else:
        print("   ❌ Gene和Transcript文件夹不完全对应")
    
    # 总结
    print(f"\n" + "=" * 60)
    print("总结:")
    
    all_perfect = (len(gene_not_in_sample) == 0 and len(sample_not_in_gene) == 0 and
                   len(transcript_not_in_sample) == 0 and len(sample_not_in_transcript) == 0 and
                   len(gene_not_in_transcript) == 0 and len(transcript_not_in_gene) == 0)
    
    if all_perfect:
        print("✅ 所有文件夹都完全一一对应！")
        print("   - Gene文件夹 ↔ Sample.csv ✅")
        print("   - Transcript文件夹 ↔ Sample.csv ✅")
        print("   - Gene文件夹 ↔ Transcript文件夹 ✅")
    else:
        print("❌ 存在不对应的情况，需要进一步处理")
    
    # 保存详细报告
    with open('correspondence_report.txt', 'w', encoding='utf-8') as f:
        f.write("Gene和Transcript文件夹与Sample.csv对应关系详细报告\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"Sample.csv中的样本ID数量: {len(sample_csv_ids)}\n")
        f.write(f"Gene文件夹中的样本ID数量: {len(gene_sample_ids)}\n")
        f.write(f"Transcript文件夹中的样本ID数量: {len(transcript_sample_ids)}\n\n")
        
        if gene_not_in_sample:
            f.write("Gene中有但Sample.csv中没有的样本:\n")
            for sample_id in sorted(gene_not_in_sample):
                f.write(f"  {sample_id}\n")
            f.write("\n")
        
        if sample_not_in_gene:
            f.write("Sample.csv中有但Gene中没有的样本:\n")
            for sample_id in sorted(sample_not_in_gene):
                f.write(f"  {sample_id}\n")
            f.write("\n")
        
        if transcript_not_in_sample:
            f.write("Transcript中有但Sample.csv中没有的样本:\n")
            for sample_id in sorted(transcript_not_in_sample):
                f.write(f"  {sample_id}\n")
            f.write("\n")
        
        if sample_not_in_transcript:
            f.write("Sample.csv中有但Transcript中没有的样本:\n")
            for sample_id in sorted(sample_not_in_transcript):
                f.write(f"  {sample_id}\n")
            f.write("\n")
        
        if gene_not_in_transcript:
            f.write("Gene中有但Transcript中没有的样本:\n")
            for sample_id in sorted(gene_not_in_transcript):
                f.write(f"  {sample_id}\n")
            f.write("\n")
        
        if transcript_not_in_gene:
            f.write("Transcript中有但Gene中没有的样本:\n")
            for sample_id in sorted(transcript_not_in_gene):
                f.write(f"  {sample_id}\n")
            f.write("\n")
    
    print(f"\n详细报告已保存到: correspondence_report.txt")

if __name__ == "__main__":
    main()

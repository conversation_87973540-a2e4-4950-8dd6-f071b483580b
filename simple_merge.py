#!/usr/bin/env python3
import pandas as pd
import shutil
from datetime import datetime

print("开始合并转录本信息...")

# 文件路径
base_file = "process_gene/ensembl_gene_id_with_full_info_2.csv"
transcript_file = "process_gene/ensembl_gene_id_with_transcript_info_2.csv"
backup_file = f"process_gene/ensembl_gene_id_with_full_info_2_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

try:
    # 创建备份
    print("创建备份...")
    shutil.copy2(base_file, backup_file)
    print(f"备份已创建: {backup_file}")
    
    # 读取文件
    print("读取基础文件...")
    df_base = pd.read_csv(base_file)
    print(f"基础文件: {len(df_base)} 行, 列: {list(df_base.columns)}")
    
    print("读取转录本文件...")
    df_transcript = pd.read_csv(transcript_file)
    print(f"转录本文件: {len(df_transcript)} 行, 列: {list(df_transcript.columns)}")
    
    # 合并数据
    print("执行合并...")
    merge_columns = ['ensembl_gene_id', 'transcript_count', 'transcripts']
    df_to_merge = df_transcript[merge_columns]
    
    df_merged = df_base.merge(df_to_merge, on='ensembl_gene_id', how='left')
    
    # 填充缺失值
    df_merged['transcript_count'] = df_merged['transcript_count'].fillna(0)
    df_merged['transcripts'] = df_merged['transcripts'].fillna('[]')
    
    print(f"合并后: {len(df_merged)} 行, 列: {list(df_merged.columns)}")
    
    # 保存文件
    print("保存文件...")
    df_merged.to_csv(base_file, index=False)
    
    print("✅ 合并完成!")
    print("前3行数据:")
    print(df_merged.head(3)[['ensembl_gene_id', 'external_gene_name', 'transcript_count']].to_string(index=False))
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

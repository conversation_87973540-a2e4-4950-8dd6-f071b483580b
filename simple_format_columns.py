#!/usr/bin/env python3
"""
简单版本：处理CSV文件，将JSON格式列转换为分号分隔格式
"""

import pandas as pd
import json

def format_json_column(json_str):
    """将JSON格式转换为分号分隔格式"""
    try:
        if pd.isna(json_str) or json_str == '' or json_str == '[]':
            return ''
        
        # 解析JSON
        data_list = json.loads(json_str)
        
        if not data_list:
            return ''
        elif len(data_list) == 1:
            return data_list[0]
        else:
            return '; '.join(data_list)
    except:
        return str(json_str)

# 读取文件
print("读取文件...")
df = pd.read_csv('Transcript_id_processed_fast.csv')

print(f"文件包含 {len(df)} 行数据")

# 处理两列
print("处理 ensembl_gene_id 列...")
df['ensembl_gene_id'] = df['ensembl_gene_id'].apply(format_json_column)

print("处理 external_gene_name 列...")
df['external_gene_name'] = df['external_gene_name'].apply(format_json_column)

# 保存结果
print("保存结果...")
df.to_csv('Transcript_id_formatted.csv', index=False)

# 统计
non_empty_ensembl = (df['ensembl_gene_id'] != '').sum()
non_empty_external = (df['external_gene_name'] != '').sum()

print("=" * 40)
print("处理完成!")
print(f"总行数: {len(df):,}")
print(f"有ensembl_gene_id的行数: {non_empty_ensembl:,}")
print(f"有external_gene_name的行数: {non_empty_external:,}")
print("输出文件: Transcript_id_formatted.csv")
print("=" * 40)

# 显示示例
print("\n示例数据:")
sample = df[df['ensembl_gene_id'] != ''].head(3)
for idx, row in sample.iterrows():
    print(f"转录本: {row.get('Transcript', 'N/A')}")
    print(f"  基因ID: {row['ensembl_gene_id']}")
    print(f"  基因名: {row['external_gene_name']}")
    print()

#!/usr/bin/env python3
"""
验证删除操作的结果
"""

import os
import csv

def main():
    # 统计Gene文件夹中的文件数量
    gene_files = [f for f in os.listdir('Gene') if f.endswith('.txt')]
    print(f'Gene文件夹中现在有 {len(gene_files)} 个txt文件')
    
    # 读取Sample.csv中的样本ID
    sample_ids = set()
    with open('Sample.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过表头
        for row in reader:
            if row and row[0]:
                sample_ids.add(row[0].strip().strip('"'))
    
    print(f'Sample.csv中有 {len(sample_ids)} 个样本ID')
    
    # 检查是否还有不在Sample.csv中的文件
    not_in_sample = []
    for filename in gene_files:
        sample_id = filename.split('_')[0]
        if sample_id not in sample_ids:
            not_in_sample.append(filename)
    
    print(f'仍有 {len(not_in_sample)} 个文件的样本ID不在Sample.csv中')
    
    if not_in_sample:
        print('前5个示例:')
        for f in not_in_sample[:5]:
            print(f'  {f}')
    else:
        print('✅ 删除成功！现在Gene文件夹中的所有文件的样本ID都在Sample.csv中')
    
    # 计算删除的文件数量
    original_count = 1956  # 原始文件数量
    deleted_count = original_count - len(gene_files)
    print(f'\n删除统计:')
    print(f'  原始文件数: {original_count}')
    print(f'  当前文件数: {len(gene_files)}')
    print(f'  已删除文件数: {deleted_count}')

if __name__ == "__main__":
    main()

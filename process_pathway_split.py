#!/usr/bin/env python3
import pandas as pd
import os
import re

def process_pathway_gene_expression():
    # 文件路径
    pathway_file = "process_gene/pathway_gene_expression_results_with_condition.csv"
    unique_genes_file = "process_gene/unique_genes.csv"
    output_dir = "process_gene/pathway_split_files"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"创建输出目录: {output_dir}")
    
    # 读取主文件
    print("读取pathway_gene_expression_results_with_condition.csv...")
    pathway_df = pd.read_csv(pathway_file)
    print(f"主文件包含 {len(pathway_df)} 行数据")
    
    # 读取基因名称映射文件
    print("读取unique_genes.csv...")
    genes_df = pd.read_csv(unique_genes_file)
    print(f"基因映射文件包含 {len(genes_df)} 行数据")
    
    # 合并数据，添加external_gene_name列
    print("合并数据，添加external_gene_name列...")
    merged_df = pathway_df.merge(
        genes_df, 
        left_on='Gene_ID', 
        right_on='ensembl_gene_id', 
        how='left'
    )
    
    # 删除重复的ensembl_gene_id列
    merged_df = merged_df.drop('ensembl_gene_id', axis=1)
    
    # 重新排列列的顺序，将external_gene_name放在Gene_ID后面
    columns = list(merged_df.columns)
    gene_name_col = columns.pop(columns.index('external_gene_name'))
    gene_id_index = columns.index('Gene_ID')
    columns.insert(gene_id_index + 1, gene_name_col)
    merged_df = merged_df[columns]
    
    # 检查匹配情况
    matched_count = merged_df['external_gene_name'].notna().sum()
    unmatched_count = len(merged_df) - matched_count
    print(f"成功匹配的记录数量: {matched_count}")
    print(f"未匹配的记录数量: {unmatched_count}")
    
    if unmatched_count > 0:
        print("未匹配的Gene_ID样例：")
        unmatched_genes = merged_df[merged_df['external_gene_name'].isna()]['Gene_ID'].unique()[:5]
        for gene_id in unmatched_genes:
            print(f"  - {gene_id}")
    
    # 获取所有唯一的Pathway_Description
    unique_pathways = merged_df['Pathway_Description'].unique()
    print(f"\\n发现 {len(unique_pathways)} 个不同的Pathway_Description:")
    
    # 函数：清理文件名
    def clean_filename(name):
        # 移除或替换不适合作为文件名的字符
        name = re.sub(r'[<>:"/\\|?*]', '_', name)
        name = re.sub(r'\\s+', '_', name)
        name = name.strip('._')
        return name
    
    # 按Pathway_Description拆分文件
    print("\\n开始按Pathway_Description拆分文件...")
    split_summary = {}
    
    for pathway in unique_pathways:
        # 过滤数据
        pathway_data = merged_df[merged_df['Pathway_Description'] == pathway]
        
        # 清理文件名
        clean_pathway_name = clean_filename(pathway)
        filename = f"{clean_pathway_name}.csv"
        filepath = os.path.join(output_dir, filename)
        
        # 保存文件
        pathway_data.to_csv(filepath, index=False)
        
        split_summary[pathway] = {
            'filename': filename,
            'record_count': len(pathway_data),
            'unique_genes': pathway_data['Gene_ID'].nunique()
        }
        
        print(f"  {pathway}: {len(pathway_data)} 条记录 -> {filename}")
    
    # 输出总结
    print(f"\\n拆分完成！共生成 {len(split_summary)} 个文件")
    print(f"所有文件已保存到: {output_dir}")
    
    # 验证：统计信息
    total_records = sum(info['record_count'] for info in split_summary.values())
    print(f"\\n验证：原始记录数 = {len(merged_df)}, 拆分后总记录数 = {total_records}")
    
    if total_records == len(merged_df):
        print("✅ 验证成功：所有记录都已正确拆分")
    else:
        print("❌ 验证失败：记录数不匹配")
    
    # 显示拆分详情
    print("\\n拆分详情:")
    for pathway, info in split_summary.items():
        print(f"  {info['filename']}: {info['record_count']} 条记录, {info['unique_genes']} 个唯一基因")
    
    return split_summary

if __name__ == "__main__":
    process_pathway_gene_expression()
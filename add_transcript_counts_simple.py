#!/usr/bin/env python3
"""
为 Sample.csv 文件添加 TRANSLATED GENES NUMBER 列
通过 SRA Accession 查询数据库中对应的基因数量
使用 subprocess 调用 mysql 命令行工具
"""

import csv
import subprocess
import json
import os
from collections import defaultdict

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def execute_mysql_query(query):
    """使用命令行执行 MySQL 查询"""
    try:
        # 构建 mysql 命令
        cmd = [
            'mysql',
            f'--host={DB_CONFIG["host"]}',
            f'--port={DB_CONFIG["port"]}',
            f'--user={DB_CONFIG["user"]}',
            f'--password={DB_CONFIG["password"]}',
            f'--database={DB_CONFIG["database"]}',
            '--batch',
            '--raw',
            '--execute', query
        ]
        
        # 执行命令
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            print(f"❌ MySQL 查询失败: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ MySQL 查询超时")
        return None
    except Exception as e:
        print(f"❌ 执行 MySQL 查询时出错: {e}")
        return None

def test_database_connection():
    """测试数据库连接"""
    print("正在测试数据库连接...")
    
    query = "SELECT COUNT(*) FROM Gene_TPM LIMIT 1;"
    result = execute_mysql_query(query)
    
    if result is not None:
        print("✅ 数据库连接成功")
        lines = result.strip().split('\n')
        if len(lines) >= 2:
            count = lines[1]  # 第一行是列名，第二行是数据
            print(f"数据库中共有 {count} 条记录")
        return True
    else:
        print("❌ 数据库连接失败")
        return False

def get_gene_counts_for_sra_list(sra_accessions):
    """查询多个 SRA Accession 的基因数量"""
    
    if not sra_accessions:
        return {}
    
    # 构建 IN 查询
    sra_list = "','".join(sra_accessions)
    query = f"""
    SELECT sraAccession, COUNT(*) as gene_count
    FROM Gene_TPM 
    WHERE sraAccession IN ('{sra_list}')
    GROUP BY sraAccession;
    """
    
    result = execute_mysql_query(query)
    
    if result is None:
        return {}
    
    # 解析结果
    counts = {}
    lines = result.strip().split('\n')
    
    if len(lines) > 1:  # 跳过表头
        for line in lines[1:]:
            if line.strip():
                parts = line.split('\t')
                if len(parts) >= 2:
                    sra_accession = parts[0]
                    count = int(parts[1])
                    counts[sra_accession] = count
    
    return counts

def process_sample_file():
    """处理 Sample.csv 文件，添加基因数量列"""
    
    input_file = 'Sample.csv'
    output_file = 'Sample_with_gene_counts.csv'
    
    print("=== 为 Sample.csv 添加基因数量 ===")
    
    # 测试数据库连接
    if not test_database_connection():
        return False
    
    try:
        # 第一步：读取所有 SRA Accession
        print("正在读取 Sample.csv 文件...")
        
        all_sra_accessions = set()
        sample_data = []
        
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                sra_accession = row['SRA Accession'].strip()
                all_sra_accessions.add(sra_accession)
                sample_data.append(row)
        
        print(f"读取了 {len(sample_data)} 行样本数据")
        print(f"发现 {len(all_sra_accessions)} 个唯一的 SRA Accession")
        
        # 第二步：批量查询基因数量
        print("正在查询数据库中的基因数量...")
        
        batch_size = 50  # 每批查询50个，避免命令行过长
        all_counts = {}
        
        sra_list = list(all_sra_accessions)
        for i in range(0, len(sra_list), batch_size):
            batch = sra_list[i:i + batch_size]
            batch_counts = get_gene_counts_for_sra_list(batch)
            all_counts.update(batch_counts)
            
            print(f"  已查询 {min(i + batch_size, len(sra_list))}/{len(sra_list)} 个 SRA Accession")
        
        print(f"查询完成，获得 {len(all_counts)} 个 SRA Accession 的基因数量")
        
        # 第三步：写入新文件
        print("正在生成新文件...")
        
        matched_count = 0
        unmatched_count = 0
        unmatched_examples = []
        
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            # 创建新的列名
            fieldnames = list(sample_data[0].keys()) + ['TRANSLATED GENES NUMBER']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for row in sample_data:
                sra_accession = row['SRA Accession'].strip()
                
                # 获取基因数量
                if sra_accession in all_counts:
                    gene_count = all_counts[sra_accession]
                    matched_count += 1
                else:
                    gene_count = 0
                    unmatched_count += 1
                    if len(unmatched_examples) < 5:
                        unmatched_examples.append(sra_accession)
                
                # 添加基因数量
                row['TRANSLATED GENES NUMBER'] = gene_count
                writer.writerow(row)
        
        # 输出统计信息
        print(f"\n处理完成!")
        print(f"总样本数: {len(sample_data):,}")
        print(f"成功匹配: {matched_count:,}")
        print(f"未匹配: {unmatched_count:,}")
        print(f"匹配率: {matched_count/len(sample_data)*100:.2f}%")
        
        if unmatched_examples:
            print(f"\n未匹配的 SRA Accession 示例:")
            for sra in unmatched_examples:
                print(f"  - {sra}")
        
        # 显示一些统计信息
        gene_counts = [count for count in all_counts.values() if count > 0]
        if gene_counts:
            print(f"\n基因数量统计:")
            print(f"  最小值: {min(gene_counts):,}")
            print(f"  最大值: {max(gene_counts):,}")
            print(f"  平均值: {sum(gene_counts)/len(gene_counts):.0f}")
        
        print(f"\n结果已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_results():
    """验证处理结果"""
    
    output_file = 'Sample_with_gene_counts.csv'
    
    print("\n=== 验证处理结果 ===")
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"新文件的列名 (共 {len(headers)} 列):")
            for i, header in enumerate(headers):
                print(f"  {i+1}. {header}")
            
            # 检查前几行
            print(f"\n前5行数据示例:")
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n行 {i+1}:")
                print(f"  SRA Accession: {row['SRA Accession']}")
                print(f"  Dataset ID: {row['Dataset ID']}")
                print(f"  Condition: {row['Condition']}")
                print(f"  TRANSLATED GENES NUMBER: {row['TRANSLATED GENES NUMBER']}")
        
        return True
        
    except Exception as e:
        print(f"验证结果时出错: {e}")
        return False

def main():
    """主函数"""
    
    # 处理文件
    if process_sample_file():
        # 验证结果
        verify_results()
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()

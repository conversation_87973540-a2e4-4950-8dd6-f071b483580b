#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证四分位数分析结果的脚本
"""

import pandas as pd
import numpy as np

def validate_results():
    """
    验证四分位数分析结果
    """
    result_file = "/Volumes/zhy/整合的所有TPM文件/process_gene/gene_quartile_analysis_results.csv"
    
    print("正在读取结果文件...")
    df = pd.read_csv(result_file)
    
    print(f"结果文件包含 {len(df)} 行数据")
    print(f"列名: {list(df.columns)}")
    
    # 统计各个等级的分布
    print("\n=== TE等级分布 ===")
    te_counts = df['TE_Level'].value_counts()
    print(te_counts)
    
    print("\n=== TR等级分布 ===")
    tr_counts = df['TR_Level'].value_counts()
    print(tr_counts)
    
    print("\n=== EVI等级分布 ===")
    evi_counts = df['EVI_Level'].value_counts()
    print(evi_counts)
    
    # 检查每个project_id的分布情况
    print("\n=== 按项目统计 ===")
    project_stats = []
    
    for project_id in df['project_id'].unique()[:10]:  # 只显示前10个项目
        project_data = df[df['project_id'] == project_id]
        
        te_dist = project_data['TE_Level'].value_counts()
        tr_dist = project_data['TR_Level'].value_counts()
        evi_dist = project_data['EVI_Level'].value_counts()
        
        print(f"\n项目 {project_id}:")
        print(f"  总行数: {len(project_data)}")
        if len(te_dist) > 0:
            print(f"  TE分布: {dict(te_dist)}")
        if len(tr_dist) > 0:
            print(f"  TR分布: {dict(tr_dist)}")
        if len(evi_dist) > 0:
            print(f"  EVI分布: {dict(evi_dist)}")
    
    # 显示一些样本数据
    print("\n=== 样本数据 ===")
    print("包含所有三个指标的行:")
    complete_rows = df[(df['TE_Level'].notna()) & (df['TE_Level'] != '') &
                      (df['TR_Level'].notna()) & (df['TR_Level'] != '') &
                      (df['EVI_Level'].notna()) & (df['EVI_Level'] != '')]
    if len(complete_rows) > 0:
        print(complete_rows.head(10))
        print(f"共有 {len(complete_rows)} 行包含所有三个指标")
    else:
        print("没有行包含所有三个指标")

    print("\n包含TE和TR指标的行:")
    te_tr_rows = df[(df['TE_Level'].notna()) & (df['TE_Level'] != '') &
                   (df['TR_Level'].notna()) & (df['TR_Level'] != '')]
    if len(te_tr_rows) > 0:
        print(te_tr_rows.head(5))
        print(f"共有 {len(te_tr_rows)} 行包含TE和TR指标")

    print("\n只包含TE指标的行:")
    te_only_rows = df[(df['TE_Level'].notna()) & (df['TE_Level'] != '') &
                     ((df['TR_Level'].isna()) | (df['TR_Level'] == '')) &
                     ((df['EVI_Level'].isna()) | (df['EVI_Level'] == ''))]
    if len(te_only_rows) > 0:
        print(te_only_rows.head(5))
        print(f"共有 {len(te_only_rows)} 行只包含TE指标")

if __name__ == "__main__":
    validate_results()

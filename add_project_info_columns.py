#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from collections import defaultdict
import sys
import os

def add_project_info_columns():
    """
    处理process_gene/unique_genes_with_project_ids.csv文件，
    根据translation_project_ids查询project_unique_info.csv，
    添加Tissue/Cell Type, Cell line, Disease三列
    """
    
    print("开始处理unique_genes_with_project_ids.csv文件...")
    
    # 读取unique_genes_with_project_ids.csv文件
    try:
        genes_df = pd.read_csv('process_gene/unique_genes_with_project_ids.csv')
        print(f"成功读取unique_genes_with_project_ids.csv，共{len(genes_df)}行")
        print("列名:", genes_df.columns.tolist())
        print("前3行数据:")
        print(genes_df.head(3))
    except Exception as e:
        print(f"读取unique_genes_with_project_ids.csv失败: {e}")
        return
    
    # 读取project_unique_info.csv文件
    try:
        project_info_df = pd.read_csv('process_gene/project_unique_info.csv')
        print(f"\n成功读取project_unique_info.csv，共{len(project_info_df)}行")
        print("列名:", project_info_df.columns.tolist())
        print("前5行数据:")
        print(project_info_df.head())
    except Exception as e:
        print(f"读取project_unique_info.csv失败: {e}")
        return
    
    print("\n创建项目信息查找字典...")
    
    # 创建项目信息查找字典
    project_dict = {}
    for _, row in project_info_df.iterrows():
        project_id = row['Project ID']
        tissue_cell_type = row['Tissue/Cell Type'] if pd.notna(row['Tissue/Cell Type']) and row['Tissue/Cell Type'] != 'NA' else ''
        cell_line = row['Cell line'] if pd.notna(row['Cell line']) and row['Cell line'] != 'NA' else ''
        condition = row['Condition'] if pd.notna(row['Condition']) and row['Condition'] != 'NA' else ''
        
        project_dict[project_id] = {
            'tissue_cell_type': tissue_cell_type,
            'cell_line': cell_line,
            'condition': condition
        }
    
    print(f"创建了{len(project_dict)}个项目的信息字典")
    
    # 添加新列
    genes_df['Tissue/Cell Type'] = ''
    genes_df['Cell line'] = ''
    genes_df['Disease'] = ''
    
    print("\n开始处理每个基因的项目信息...")
    
    # 处理每个基因
    for idx, row in genes_df.iterrows():
        if idx % 1000 == 0:
            print(f"处理进度: {idx}/{len(genes_df)}")
        
        translation_project_ids_str = row['translation_project_ids']
        
        # 解析project_ids
        if pd.notna(translation_project_ids_str) and translation_project_ids_str:
            project_ids = translation_project_ids_str.split(';')
            
            # 收集所有唯一的值
            tissue_cell_types = set()
            cell_lines = set()
            conditions = set()
            
            for project_id in project_ids:
                project_id = project_id.strip()
                if project_id in project_dict:
                    info = project_dict[project_id]
                    if info['tissue_cell_type']:
                        tissue_cell_types.add(info['tissue_cell_type'])
                    if info['cell_line']:
                        cell_lines.add(info['cell_line'])
                    if info['condition']:
                        conditions.add(info['condition'])
            
            # 转换为排序的字符串格式，用花括号包围
            tissue_cell_type_str = '{' + ','.join(sorted(tissue_cell_types)) + '}' if tissue_cell_types else '{}'
            cell_line_str = '{' + ','.join(sorted(cell_lines)) + '}' if cell_lines else '{}'
            condition_str = '{' + ','.join(sorted(conditions)) + '}' if conditions else '{}'
            
            # 更新DataFrame
            genes_df.at[idx, 'Tissue/Cell Type'] = tissue_cell_type_str
            genes_df.at[idx, 'Cell line'] = cell_line_str
            genes_df.at[idx, 'Disease'] = condition_str
        else:
            # 如果没有project_ids，设置为空的花括号
            genes_df.at[idx, 'Tissue/Cell Type'] = '{}'
            genes_df.at[idx, 'Cell line'] = '{}'
            genes_df.at[idx, 'Disease'] = '{}'
    
    # 保存结果
    output_file = 'process_gene/unique_genes_with_project_info.csv'
    genes_df.to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    # 显示一些统计信息
    print(f"\n统计信息:")
    print(f"总基因数: {len(genes_df)}")
    
    # 显示一些示例结果
    print(f"\n前5行结果示例:")
    print(genes_df[['ensembl_gene_id', 'external_gene_name', 'Tissue/Cell Type', 'Cell line', 'Disease']].head())
    
    # 显示一些有数据的行
    non_empty_mask = (genes_df['Tissue/Cell Type'] != '{}') | (genes_df['Cell line'] != '{}') | (genes_df['Disease'] != '{}')
    non_empty_genes = genes_df[non_empty_mask]
    if not non_empty_genes.empty:
        print(f"\n有项目信息的基因示例（前3个）:")
        print(non_empty_genes[['ensembl_gene_id', 'external_gene_name', 'Tissue/Cell Type', 'Cell line', 'Disease']].head(3))
    
    print("\n处理完成！")

if __name__ == "__main__":
    add_project_info_columns()

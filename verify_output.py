#!/usr/bin/env python3

import csv

def verify_output():
    filename = 'Transcript_id_with_gene_info_latest.csv'
    
    print("🔍 验证输出文件")
    print("=" * 50)
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            
            print(f"📋 表头: {header}")
            print()
            
            total_rows = 0
            has_gene_id = 0
            has_gene_name = 0
            both_present = 0
            mapping_found = 0
            mapping_not_found = 0
            
            for row in reader:
                total_rows += 1
                
                if len(row) >= 4:
                    transcript_id = row[0]
                    gene_id = row[1]
                    gene_name = row[2]
                    mapping_status = row[3]
                    
                    if gene_id and gene_id != '':
                        has_gene_id += 1
                    if gene_name and gene_name != '':
                        has_gene_name += 1
                    if gene_id and gene_id != '' and gene_name and gene_name != '':
                        both_present += 1
                    
                    if mapping_status == 'found':
                        mapping_found += 1
                    elif mapping_status == 'not_found':
                        mapping_not_found += 1
            
            print(f"📊 统计结果:")
            print(f"  总行数: {total_rows:,}")
            print(f"  有基因ID: {has_gene_id:,} ({has_gene_id/total_rows*100:.2f}%)")
            print(f"  有基因名称: {has_gene_name:,} ({has_gene_name/total_rows*100:.2f}%)")
            print(f"  同时有ID和名称: {both_present:,} ({both_present/total_rows*100:.2f}%)")
            print(f"  映射成功: {mapping_found:,}")
            print(f"  映射失败: {mapping_not_found:,}")
            print()
            
            # 显示前几行示例
            print("📝 前5行示例:")
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                header = next(reader)
                for i, row in enumerate(reader):
                    if i < 5:
                        print(f"  {i+1}: {row}")
                    else:
                        break
            
            print()
            print("✅ 文件验证完成！")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_output()

#!/usr/bin/env python3
import os
import csv

# 获取Transcript文件夹中的样本ID
transcript_folder = 'Transcript'
transcript_sample_ids = []

print("正在处理Transcript文件夹...")
if os.path.exists(transcript_folder):
    files = os.listdir(transcript_folder)
    txt_files = [f for f in files if f.endswith('.txt')]
    print(f"找到 {len(txt_files)} 个txt文件")
    
    for filename in txt_files:
        if '_' in filename:
            sample_id = filename.split('_')[0]
            transcript_sample_ids.append(sample_id)
    
    transcript_sample_ids = sorted(list(set(transcript_sample_ids)))
    print(f"提取到 {len(transcript_sample_ids)} 个唯一样本ID")
else:
    print("Transcript文件夹不存在")

# 读取Sample.csv文件
sample_csv_ids = []
sample_csv_path = 'Sample.csv'

print("\n正在读取Sample.csv文件...")
if os.path.exists(sample_csv_path):
    with open(sample_csv_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        header = next(reader, None)
        print(f"Sample.csv表头: {header}")
        
        for row in reader:
            if row:
                sample_csv_ids.append(row[0])
    
    sample_csv_ids = sorted(list(set(sample_csv_ids)))
    print(f"Sample.csv中找到 {len(sample_csv_ids)} 个唯一样本ID")
else:
    print("Sample.csv文件不存在")

# 比较
if transcript_sample_ids and sample_csv_ids:
    transcript_set = set(transcript_sample_ids)
    sample_set = set(sample_csv_ids)
    
    missing_in_sample = transcript_set - sample_set
    missing_in_transcript = sample_set - transcript_set
    common_samples = transcript_set & sample_set
    
    print(f"\n=== 比较结果 ===")
    print(f"Transcript文件夹中的样本总数: {len(transcript_sample_ids)}")
    print(f"Sample.csv中的样本总数: {len(sample_csv_ids)}")
    print(f"共同样本数: {len(common_samples)}")
    print(f"Transcript文件夹中有但Sample.csv中没有的样本数: {len(missing_in_sample)}")
    print(f"Sample.csv中有但Transcript文件夹中没有的样本数: {len(missing_in_transcript)}")
    
    # 保存结果
    if missing_in_sample:
        with open('transcript_missing_in_sample.csv', 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID'])
            for sample_id in sorted(missing_in_sample):
                writer.writerow([sample_id])
        print(f"\nTranscript文件夹中有但Sample.csv中没有的样本已保存到: transcript_missing_in_sample.csv")
        
        print(f"\n前20个缺失的样本:")
        for i, sample_id in enumerate(sorted(missing_in_sample)[:20]):
            print(f"  {i+1}. {sample_id}")
        if len(missing_in_sample) > 20:
            print(f"  ... 还有 {len(missing_in_sample) - 20} 个样本")
    
    # 保存Transcript样本ID列表
    with open('transcript_all_sample_ids.csv', 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Sample_ID'])
        for sample_id in transcript_sample_ids:
            writer.writerow([sample_id])
    print(f"\nTranscript文件夹所有样本ID已保存到: transcript_all_sample_ids.csv")

print("\n处理完成！")

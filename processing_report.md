# CSV文件处理报告

## 任务描述
处理文件：`compute_gene/gene_count_by_project_results_with_translation_indices_and_project_info.csv`

**任务要求：**
1. 删除TR, EVI, TE同时为空的行
2. 保存这些被删除行的ensembl_gene_id, external_gene_name, project_id列到新的CSV文件

## 处理结果

### ✅ 成功完成处理

**输出文件：**

1. **被删除的行数据：** `compute_gene/deleted_rows_tr_evi_te_empty.csv`
   - 包含102,000行数据（不包括标题行）
   - 包含列：ensembl_gene_id, external_gene_name, project_id
   - 这些是TR, EVI, TE同时为空的行

2. **清理后的完整数据：** `compute_gene/gene_count_by_project_results_cleaned.csv`
   - 包含原始数据中TR, EVI, TE不全为空的所有行
   - 保持原始文件的所有列结构

### 数据示例

**被删除的行示例：**
```
ensembl_gene_id,external_gene_name,project_id
ENSG00000000971,CFH,TEDD00019
ENSG00000000971,CFH,TEDD00037
ENSG00000000971,CFH,TEDD00038
ENSG00000000971,CFH,TEDD00039
ENSG00000000971,CFH,TEDD00044
```

### 处理逻辑

脚本检查了以下条件来识别需要删除的行：
- TR列为空（NaN、空字符串或0）
- EVI列为空（NaN、空字符串或0）  
- TE列为空（NaN、空字符串或0）

只有当这三个条件**同时满足**时，该行才会被删除。

### 文件位置

所有处理后的文件都保存在 `compute_gene/` 目录下：
- 原始文件：`gene_count_by_project_results_with_translation_indices_and_project_info.csv`
- 删除的行：`deleted_rows_tr_evi_te_empty.csv` 
- 清理后数据：`gene_count_by_project_results_cleaned.csv`

## 总结

✅ 任务已成功完成
- 识别并删除了102,000行TR, EVI, TE同时为空的数据
- 保存了被删除行的指定列（ensembl_gene_id, external_gene_name, project_id）
- 生成了清理后的完整数据文件
- 保持了数据的完整性和一致性

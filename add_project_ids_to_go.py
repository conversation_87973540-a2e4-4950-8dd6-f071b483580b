import pandas as pd

# 读取两个文件
go_df = pd.read_csv('process_gene/GO_annotation_final_processed.csv')
genes_df = pd.read_csv('process_gene/unique_genes_with_project_ids.csv')

# 显示文件信息
print(f"GO注释文件行数: {len(go_df)}")
print(f"基因文件行数: {len(genes_df)}")

# 创建映射字典，从ensembl_gene_id到translation_project_ids
gene_to_projects = dict(zip(genes_df['ensembl_gene_id'], genes_df['translation_project_ids']))

# 将Gene_ID映射到translation_project_ids
go_df['translation_project_ids'] = go_df['Gene_ID'].map(gene_to_projects)

# 检查匹配情况
matched_count = go_df['translation_project_ids'].notna().sum()
total_count = len(go_df)
print(f"成功匹配的记录数: {matched_count}/{total_count}")

# 保存结果
output_file = 'process_gene/GO_annotation_final_processed_with_project_ids.csv'
go_df.to_csv(output_file, index=False)

print(f"结果已保存到: {output_file}")
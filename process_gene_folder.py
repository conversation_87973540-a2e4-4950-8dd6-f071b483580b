#!/usr/bin/env python3
"""
脚本功能：
1. 处理Gene文件夹，获取第一个"_"前面的样本ID作为第一列
2. 读取Sample.csv文件的第一列
3. 比较两个列表，找出哪些样本ID没有在Sample.csv文件中出现
"""

import os
import csv

def extract_sample_id(filename):
    """从文件名中提取第一个'_'前面的样本ID"""
    if filename.endswith('.txt'):
        return filename.split('_')[0]
    return None

def get_gene_sample_ids(gene_folder):
    """获取Gene文件夹下所有txt文件的样本ID"""
    sample_ids = []
    if os.path.exists(gene_folder):
        for filename in os.listdir(gene_folder):
            if filename.endswith('.txt'):
                sample_id = extract_sample_id(filename)
                if sample_id:
                    sample_ids.append(sample_id)
    return sorted(list(set(sample_ids)))  # 去重并排序

def read_sample_csv(sample_csv_path):
    """读取Sample.csv文件的第一列"""
    sample_ids = []
    try:
        with open(sample_csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader, None)  # 读取表头
            print(f"Sample.csv表头: {header}")
            
            for row in reader:
                if row:  # 确保行不为空
                    sample_ids.append(row[0])  # 取第一列
    except FileNotFoundError:
        print(f"错误：找不到文件 {sample_csv_path}")
        return []
    except Exception as e:
        print(f"读取Sample.csv文件时出错：{e}")
        return []
    
    return sorted(list(set(sample_ids)))  # 去重并排序

def main():
    # 定义文件路径
    gene_folder = 'Gene'
    sample_csv_path = 'Sample.csv'
    
    print("正在处理Gene文件夹...")
    gene_sample_ids = get_gene_sample_ids(gene_folder)
    print(f"Gene文件夹中找到 {len(gene_sample_ids)} 个唯一样本ID")
    
    print("\n正在读取Sample.csv文件...")
    sample_csv_ids = read_sample_csv(sample_csv_path)
    print(f"Sample.csv文件中找到 {len(sample_csv_ids)} 个唯一样本ID")
    
    # 转换为集合以便快速查找
    gene_set = set(gene_sample_ids)
    sample_set = set(sample_csv_ids)
    
    # 找出在Gene文件夹中但不在Sample.csv中的样本ID
    missing_in_sample = gene_set - sample_set
    
    # 找出在Sample.csv中但不在Gene文件夹中的样本ID
    missing_in_gene = sample_set - gene_set
    
    # 找出共同的样本ID
    common_samples = gene_set & sample_set
    
    print(f"\n=== 比较结果 ===")
    print(f"Gene文件夹中的样本总数: {len(gene_sample_ids)}")
    print(f"Sample.csv中的样本总数: {len(sample_csv_ids)}")
    print(f"共同样本数: {len(common_samples)}")
    print(f"Gene文件夹中有但Sample.csv中没有的样本数: {len(missing_in_sample)}")
    print(f"Sample.csv中有但Gene文件夹中没有的样本数: {len(missing_in_gene)}")
    
    # 保存Gene文件夹中的样本ID列表
    gene_output_file = 'gene_folder_sample_ids.csv'
    with open(gene_output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Sample_ID'])  # 表头
        for sample_id in gene_sample_ids:
            writer.writerow([sample_id])
    print(f"\nGene文件夹样本ID列表已保存到: {gene_output_file}")
    
    # 保存在Gene文件夹中但不在Sample.csv中的样本ID
    if missing_in_sample:
        missing_output_file = 'gene_not_in_sample.csv'
        with open(missing_output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID'])  # 表头
            for sample_id in sorted(missing_in_sample):
                writer.writerow([sample_id])
        print(f"Gene文件夹中有但Sample.csv中没有的样本已保存到: {missing_output_file}")
        
        # 显示前20个缺失的样本
        print(f"\n前20个在Gene文件夹中但不在Sample.csv中的样本:")
        for i, sample_id in enumerate(sorted(missing_in_sample)[:20]):
            print(f"  {i+1}. {sample_id}")
        if len(missing_in_sample) > 20:
            print(f"  ... 还有 {len(missing_in_sample) - 20} 个样本")
    else:
        print("\n✅ 所有Gene文件夹中的样本都在Sample.csv中找到了！")
    
    # 保存在Sample.csv中但不在Gene文件夹中的样本ID
    if missing_in_gene:
        sample_missing_output_file = 'sample_not_in_gene.csv'
        with open(sample_missing_output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID'])  # 表头
            for sample_id in sorted(missing_in_gene):
                writer.writerow([sample_id])
        print(f"Sample.csv中有但Gene文件夹中没有的样本已保存到: {sample_missing_output_file}")
        
        # 显示前10个缺失的样本
        print(f"\n前10个在Sample.csv中但不在Gene文件夹中的样本:")
        for i, sample_id in enumerate(sorted(missing_in_gene)[:10]):
            print(f"  {i+1}. {sample_id}")
        if len(missing_in_gene) > 10:
            print(f"  ... 还有 {len(missing_in_gene) - 10} 个样本")
    
    # 生成详细的比较报告
    report_file = 'gene_vs_sample_comparison.csv'
    with open(report_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Sample_ID', 'In_Gene_Folder', 'In_Sample_CSV', 'Status'])
        
        # 所有样本ID的并集
        all_samples = gene_set | sample_set
        
        for sample_id in sorted(all_samples):
            in_gene = sample_id in gene_set
            in_sample = sample_id in sample_set
            
            if in_gene and in_sample:
                status = 'Found in both'
            elif in_gene and not in_sample:
                status = 'Missing in Sample.csv'
            elif not in_gene and in_sample:
                status = 'Missing in Gene folder'
            else:
                status = 'Unknown'
            
            writer.writerow([sample_id, in_gene, in_sample, status])
    
    print(f"\n详细比较报告已保存到: {report_file}")
    print("\n处理完成！")

if __name__ == "__main__":
    main()

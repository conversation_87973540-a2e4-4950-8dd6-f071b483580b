#!/usr/bin/env python3
"""
处理 Transcript_id.csv 文件，为其中的 "Transcript" 列添加两个新列：
ensembl_gene_id 和 external_gene_name

作者: AI Assistant
日期: 2025-07-29
"""

import pandas as pd
import pymysql
import json
import logging
from typing import Dict, List, Tuple, Optional
import sys
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('transcript_processing.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306,
    'charset': 'utf8mb4',
    'autocommit': True
}

class TranscriptProcessor:
    """处理转录本数据的主类"""
    
    def __init__(self, db_config: Dict):
        """
        初始化处理器
        
        Args:
            db_config: 数据库配置字典
        """
        self.db_config = db_config
        self.connection = None
        
    def connect_database(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = pymysql.connect(**self.db_config)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def query_transcript_data(self, transcript_ids: List[str]) -> Dict[str, Tuple[List[str], List[str]]]:
        """
        批量查询转录本数据
        
        Args:
            transcript_ids: 转录本ID列表
            
        Returns:
            Dict: {transcript_id: (ensembl_gene_ids, external_gene_names)}
        """
        if not self.connection:
            logger.error("数据库未连接")
            return {}
        
        # 构建批量查询的SQL
        placeholders = ','.join(['%s'] * len(transcript_ids))
        sql = f"""
        SELECT transcriptId, ensemblGeneId, externalGeneName
        FROM tpmData 
        WHERE transcriptId IN ({placeholders})
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, transcript_ids)
                results = cursor.fetchall()
                
                # 组织数据：每个transcript_id对应的唯一基因ID和基因名
                transcript_data = defaultdict(lambda: (set(), set()))
                
                for transcript_id, ensembl_gene_id, external_gene_name in results:
                    ensembl_set, external_set = transcript_data[transcript_id]
                    
                    # 添加非空值到集合中
                    if ensembl_gene_id and ensembl_gene_id.strip():
                        ensembl_set.add(ensembl_gene_id.strip())
                    if external_gene_name and external_gene_name.strip():
                        external_set.add(external_gene_name.strip())
                
                # 转换为列表并排序
                result_dict = {}
                for transcript_id, (ensembl_set, external_set) in transcript_data.items():
                    result_dict[transcript_id] = (
                        sorted(list(ensembl_set)),
                        sorted(list(external_set))
                    )
                
                logger.info(f"成功查询 {len(result_dict)} 个转录本的数据")
                return result_dict
                
        except Exception as e:
            logger.error(f"查询数据库时出错: {e}")
            return {}
    
    def process_csv_file(self, input_file: str, output_file: str, batch_size: int = 1000):
        """
        处理CSV文件
        
        Args:
            input_file: 输入CSV文件路径
            output_file: 输出CSV文件路径
            batch_size: 批量查询大小
        """
        try:
            # 读取CSV文件
            logger.info(f"读取CSV文件: {input_file}")
            df = pd.read_csv(input_file)
            
            # 检查必要的列是否存在
            if 'Transcript' not in df.columns:
                raise ValueError("CSV文件中缺少 'Transcript' 列")
            
            logger.info(f"CSV文件包含 {len(df)} 行数据")
            
            # 获取所有唯一的转录本ID
            unique_transcripts = df['Transcript'].dropna().unique().tolist()
            logger.info(f"发现 {len(unique_transcripts)} 个唯一的转录本ID")
            
            # 初始化新列
            df['ensembl_gene_id'] = ''
            df['external_gene_name'] = ''
            
            # 批量处理转录本ID
            all_transcript_data = {}
            
            for i in range(0, len(unique_transcripts), batch_size):
                batch = unique_transcripts[i:i + batch_size]
                logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 个转录本")
                
                batch_data = self.query_transcript_data(batch)
                all_transcript_data.update(batch_data)
            
            # 填充数据到DataFrame
            logger.info("填充数据到DataFrame")
            for index, row in df.iterrows():
                transcript_id = row['Transcript']
                
                if pd.isna(transcript_id) or transcript_id not in all_transcript_data:
                    # 如果转录本ID为空或未找到数据，设置为空的JSON数组
                    df.at[index, 'ensembl_gene_id'] = json.dumps([])
                    df.at[index, 'external_gene_name'] = json.dumps([])
                else:
                    ensembl_ids, external_names = all_transcript_data[transcript_id]
                    df.at[index, 'ensembl_gene_id'] = json.dumps(ensembl_ids)
                    df.at[index, 'external_gene_name'] = json.dumps(external_names)
            
            # 保存结果
            logger.info(f"保存结果到: {output_file}")
            df.to_csv(output_file, index=False)
            
            # 统计信息
            non_empty_ensembl = df[df['ensembl_gene_id'] != '[]'].shape[0]
            non_empty_external = df[df['external_gene_name'] != '[]'].shape[0]
            
            logger.info(f"处理完成!")
            logger.info(f"- 总行数: {len(df)}")
            logger.info(f"- 找到ensembl_gene_id的行数: {non_empty_ensembl}")
            logger.info(f"- 找到external_gene_name的行数: {non_empty_external}")
            
        except Exception as e:
            logger.error(f"处理CSV文件时出错: {e}")
            raise

def main():
    """主函数"""
    # 文件路径配置
    input_file = 'Transcript_id.csv'
    output_file = 'Transcript_id_processed.csv'
    
    # 创建处理器实例
    processor = TranscriptProcessor(DB_CONFIG)
    
    try:
        # 连接数据库
        if not processor.connect_database():
            logger.error("无法连接数据库，程序退出")
            return
        
        # 处理CSV文件
        processor.process_csv_file(input_file, output_file)
        
        logger.info("所有处理完成!")
        
    except FileNotFoundError:
        logger.error(f"找不到输入文件: {input_file}")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        # 确保数据库连接被关闭
        processor.close_database()

if __name__ == "__main__":
    main()

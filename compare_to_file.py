#!/usr/bin/env python3
import pandas as pd

# 将结果写入文件
with open('comparison_results.txt', 'w', encoding='utf-8') as f:
    f.write("基因ID比较分析结果\n")
    f.write("=" * 50 + "\n\n")
    
    try:
        # 读取文件
        f.write("读取文件1: unique_genes.csv\n")
        df1 = pd.read_csv('process_gene/unique_genes.csv')
        f.write(f"文件1行数: {len(df1)}\n")
        
        f.write("读取文件2: ensembl_gene_id_with_full_info_split_2.csv\n")
        df2 = pd.read_csv('process_gene/ensembl_gene_id_with_full_info_split_2.csv')
        f.write(f"文件2行数: {len(df2)}\n\n")
        
        # 提取基因ID
        genes1 = set(df1['ensembl_gene_id'])
        genes2 = set(df2['ensembl_gene_id'])
        
        f.write(f"文件1中唯一基因ID数: {len(genes1)}\n")
        f.write(f"文件2中唯一基因ID数: {len(genes2)}\n\n")
        
        # 找出差异
        missing = genes1 - genes2
        extra = genes2 - genes1
        common = genes1 & genes2
        
        f.write(f"共同基因数: {len(common)}\n")
        f.write(f"文件1中但不在文件2中的基因数: {len(missing)}\n")
        f.write(f"文件2中但不在文件1中的基因数: {len(extra)}\n\n")
        
        if missing:
            f.write(f"缺失的基因ID列表 (前20个):\n")
            missing_list = sorted(list(missing))
            for i, gene_id in enumerate(missing_list[:20]):
                gene_name = df1[df1['ensembl_gene_id'] == gene_id]['external_gene_name'].iloc[0]
                f.write(f"  {i+1:2d}. {gene_id} - {gene_name}\n")
            
            if len(missing) > 20:
                f.write(f"  ... 还有 {len(missing) - 20} 个基因\n")
            
            # 保存缺失的基因
            missing_df = df1[df1['ensembl_gene_id'].isin(missing)].copy()
            missing_df = missing_df.sort_values('ensembl_gene_id')
            missing_df.to_csv('process_gene/missing_genes.csv', index=False)
            f.write(f"\n已保存 {len(missing)} 个缺失基因到 process_gene/missing_genes.csv\n")
        else:
            f.write("✅ 所有基因ID都匹配!\n")
        
        f.write(f"\n匹配率: {len(common)/len(genes1)*100:.2f}%\n")
        f.write("分析完成!\n")
        
    except Exception as e:
        f.write(f"错误: {str(e)}\n")

print("结果已写入 comparison_results.txt 文件")

#!/usr/bin/env python3
"""
过滤基因数据文件，只保留在unique_genes.csv中存在的ensembl_gene_id
"""

import csv
import sys
import os

def filter_genes():
    """
    处理process_gene/ensembl_gene_id_with_full_info_split_2.csv文件，
    只保留在process_gene/unique_genes.csv中存在的ensembl_gene_id的行
    """

    # 文件路径
    full_info_file = "process_gene/ensembl_gene_id_with_full_info_split_2.csv"
    unique_genes_file = "process_gene/unique_genes.csv"
    output_file = "process_gene/ensembl_gene_id_with_full_info_split_2_filtered.csv"

    # 检查文件是否存在
    if not os.path.exists(full_info_file):
        print(f"错误：文件 {full_info_file} 不存在")
        return False

    if not os.path.exists(unique_genes_file):
        print(f"错误：文件 {unique_genes_file} 不存在")
        return False

    try:
        print("正在读取文件...")

        # 读取unique_genes.csv，获取所有有效的ensembl_gene_id
        print(f"读取 {unique_genes_file}...")
        valid_gene_ids = set()
        with open(unique_genes_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                valid_gene_ids.add(row['ensembl_gene_id'])
        print(f"找到 {len(valid_gene_ids)} 个有效的基因ID")

        # 读取完整信息文件并过滤
        print(f"读取并过滤 {full_info_file}...")
        original_count = 0
        filtered_count = 0

        with open(full_info_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:

            reader = csv.DictReader(infile)
            writer = csv.DictWriter(outfile, fieldnames=reader.fieldnames)
            writer.writeheader()

            for row in reader:
                original_count += 1
                if row['ensembl_gene_id'] in valid_gene_ids:
                    writer.writerow(row)
                    filtered_count += 1

        deleted_count = original_count - filtered_count

        # 显示统计信息
        print("\n处理完成！")
        print(f"原始行数: {original_count}")
        print(f"有效基因ID数量: {len(valid_gene_ids)}")
        print(f"过滤后行数: {filtered_count}")
        print(f"删除行数: {deleted_count}")
        print(f"输出文件: {output_file}")

        return True

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = filter_genes()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Z-score的四分位数分组分析脚本（包含Z-score值）
处理gene_count_by_project_results_cleaned.csv文件
按照project_id分组，对TE、TR、EVI值进行四分位数分析，并添加Z-score值
"""

import pandas as pd
import numpy as np
import math
import os

def calculate_quartile_groups_and_zscores(data, value_column):
    """
    计算基于Z-score的四分位数分组，同时返回Z-score值
    
    Args:
        data: DataFrame，包含数据
        value_column: str，要分析的列名（TE、TR或EVI）
    
    Returns:
        tuple: (quartile_groups, zscore_values)
    """
    # 步骤1：数据预处理和对数转换
    # 过滤出有效数据并进行对数转换
    valid_data = data[data[value_column] > 0].copy()
    
    if len(valid_data) == 0:
        return ([''] * len(data), [np.nan] * len(data))
    
    # 对原始值取log2
    log_values = np.log2(valid_data[value_column])
    
    # 步骤2：计算 Z-score
    # 计算均值
    mean = np.mean(log_values)
    
    # 计算样本标准差（除以 n-1）
    std_dev = np.std(log_values, ddof=1)
    
    if std_dev == 0:
        return (['Q2'] * len(data), [0.0] * len(data))  # 如果标准差为0，都分配到Q2，Z-score为0
    
    # 计算每个数据点的 Z-score
    zscores = (log_values - mean) / std_dev
    
    # 步骤3：计算分位数阈值
    # 对 Z-score 进行排序
    sorted_zscores = np.sort(zscores)
    n = len(sorted_zscores)
    
    # 计算分位数索引（Python 方式）
    q1_idx = int(n * 0.25)  # 25% 分位数索引
    q2_idx = int(n * 0.5)   # 50% 分位数索引（中位数）
    q3_idx = int(n * 0.75)  # 75% 分位数索引
    
    # 获取分位数值
    q1 = sorted_zscores[q1_idx]
    q2 = sorted_zscores[q2_idx]
    q3 = sorted_zscores[q3_idx]
    
    # 步骤4：分配四分位数等级和Z-score值
    quartile_groups = []
    zscore_values = []
    zscore_dict = dict(zip(valid_data.index, zscores))
    
    for idx in data.index:
        if idx in zscore_dict:
            zscore = zscore_dict[idx]
            zscore_values.append(zscore)
            
            if zscore <= q1:
                quartile_groups.append('Q1')      # 最低 25%
            elif zscore <= q2:
                quartile_groups.append('Q2')      # 25%-50%
            elif zscore <= q3:
                quartile_groups.append('Q3')      # 50%-75%
            else:
                quartile_groups.append('Q4')      # 最高 25%
        else:
            quartile_groups.append('')  # 无效数据
            zscore_values.append(np.nan)  # 无效数据的Z-score为NaN
    
    return quartile_groups, zscore_values

def process_gene_data():
    """
    处理基因数据文件
    """
    input_file = "/Volumes/zhy/整合的所有TPM文件/process_gene/gene_count_by_project_results_cleaned.csv"
    output_file = "/Volumes/zhy/整合的所有TPM文件/process_gene/gene_quartile_analysis_with_zscore_results.csv"
    
    print("正在读取数据文件...")
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"成功读取数据，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        
        # 检查数据结构
        print("\n数据预览:")
        print(df.head())
        
        # 检查TE、TR、EVI列的数据情况
        print(f"\nTE列非空值数量: {df['TE'].notna().sum()}")
        print(f"TR列非空值数量: {df['TR'].notna().sum()}")
        print(f"EVI列非空值数量: {df['EVI'].notna().sum()}")
        
        # 获取所有唯一的project_id
        unique_projects = df['project_id'].unique()
        print(f"\n共有 {len(unique_projects)} 个不同的project_id")
        
        # 初始化结果列
        df['TE_Level'] = ''
        df['TR_Level'] = ''
        df['EVI_Level'] = ''
        df['TE_log2_zscore'] = np.nan
        df['TR_log2_zscore'] = np.nan
        df['EVI_log2_zscore'] = np.nan
        
        # 按project_id分组处理
        for i, project_id in enumerate(unique_projects):
            print(f"\n处理项目 {i+1}/{len(unique_projects)}: {project_id}")
            project_data = df[df['project_id'] == project_id].copy()
            
            # 处理TE列
            if project_data['TE'].notna().sum() > 0:
                te_levels, te_zscores = calculate_quartile_groups_and_zscores(project_data, 'TE')
                df.loc[df['project_id'] == project_id, 'TE_Level'] = te_levels
                df.loc[df['project_id'] == project_id, 'TE_log2_zscore'] = te_zscores
                print(f"  TE: 处理了 {len([x for x in te_levels if x])} 个有效值")
            
            # 处理TR列
            if project_data['TR'].notna().sum() > 0:
                tr_levels, tr_zscores = calculate_quartile_groups_and_zscores(project_data, 'TR')
                df.loc[df['project_id'] == project_id, 'TR_Level'] = tr_levels
                df.loc[df['project_id'] == project_id, 'TR_log2_zscore'] = tr_zscores
                print(f"  TR: 处理了 {len([x for x in tr_levels if x])} 个有效值")
            
            # 处理EVI列
            if project_data['EVI'].notna().sum() > 0:
                evi_levels, evi_zscores = calculate_quartile_groups_and_zscores(project_data, 'EVI')
                df.loc[df['project_id'] == project_id, 'EVI_Level'] = evi_levels
                df.loc[df['project_id'] == project_id, 'EVI_log2_zscore'] = evi_zscores
                print(f"  EVI: 处理了 {len([x for x in evi_levels if x])} 个有效值")
        
        # 创建最终输出DataFrame，包含所有需要的列
        output_df = df[['ensembl_gene_id', 'external_gene_name', 'project_id', 
                       'TE_Level', 'TR_Level', 'EVI_Level',
                       'TE_log2_zscore', 'TR_log2_zscore', 'EVI_log2_zscore']].copy()
        
        # 保存结果
        output_df.to_csv(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 显示结果统计
        print("\n结果统计:")
        for level_col in ['TE_Level', 'TR_Level', 'EVI_Level']:
            print(f"{level_col}:")
            value_counts = output_df[level_col].value_counts()
            for level, count in value_counts.items():
                if level:  # 只显示非空值
                    print(f"  {level}: {count}")
        
        # 显示Z-score统计
        print("\nZ-score统计:")
        for zscore_col in ['TE_log2_zscore', 'TR_log2_zscore', 'EVI_log2_zscore']:
            valid_zscores = output_df[zscore_col].dropna()
            if len(valid_zscores) > 0:
                print(f"{zscore_col}: 均值={valid_zscores.mean():.4f}, 标准差={valid_zscores.std():.4f}, 有效值数量={len(valid_zscores)}")
        
        print(f"\n处理完成！输出文件包含 {len(output_df)} 行数据")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始处理基因数据...")
    try:
        process_gene_data()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()

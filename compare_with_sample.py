#!/usr/bin/env python3
"""
脚本功能：
1. 处理Gene文件夹，获取第一个"_"前面的样本ID
2. 读取Sample.csv文件的第一列（SRA Accession）
3. 比较两者，找出Gene文件夹中有但Sample.csv中没有的样本ID
4. 生成比较结果报告
"""

import os
import csv
from collections import defaultdict

def extract_sample_id(filename):
    """从文件名中提取第一个'_'前面的样本ID"""
    if filename.endswith('.txt'):
        return filename.split('_')[0]
    return None

def get_sample_ids_from_folder(folder_path):
    """获取指定文件夹下所有txt文件的样本ID"""
    sample_ids = set()
    if os.path.exists(folder_path):
        for filename in os.listdir(folder_path):
            if filename.endswith('.txt'):
                sample_id = extract_sample_id(filename)
                if sample_id:
                    sample_ids.add(sample_id)
    return sample_ids

def read_sample_csv(csv_file):
    """读取Sample.csv文件的第一列（SRA Accession）"""
    sample_ids = set()
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)  # 跳过表头
            print(f"Sample.csv表头: {header[0]}")
            
            for row in reader:
                if row and row[0]:  # 确保第一列不为空
                    # 去除可能的引号
                    sample_id = row[0].strip().strip('"')
                    sample_ids.add(sample_id)
    except FileNotFoundError:
        print(f"错误：找不到文件 {csv_file}")
        return set()
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return set()
    
    return sample_ids

def main():
    # 定义Gene文件夹的绝对路径
    base_path = "/Volumes/zhy/整合的所有TPM文件"
    gene_folders = [
        os.path.join(base_path, 'Gene_01'),
        os.path.join(base_path, 'Gene_02'),
        os.path.join(base_path, 'Gene_03')
    ]
    folder_names = ['Gene_01', 'Gene_02', 'Gene_03']
    
    # 收集所有Gene文件夹中的样本ID
    all_gene_sample_ids = set()
    folder_sample_counts = {}
    
    print("正在处理Gene文件夹...")
    for i, folder in enumerate(gene_folders):
        folder_name = folder_names[i]
        if os.path.exists(folder):
            sample_ids = get_sample_ids_from_folder(folder)
            folder_sample_counts[folder_name] = len(sample_ids)
            all_gene_sample_ids.update(sample_ids)
            print(f"  {folder_name}: {len(sample_ids)} 个样本ID")
        else:
            print(f"  警告：文件夹 {folder} 不存在")
            folder_sample_counts[folder_name] = 0
    
    print(f"\nGene文件夹总计: {len(all_gene_sample_ids)} 个唯一样本ID")
    
    # 读取Sample.csv文件
    print("\n正在读取Sample.csv文件...")
    sample_csv_path = os.path.join(base_path, 'Sample.csv')
    sample_csv_ids = read_sample_csv(sample_csv_path)
    print(f"Sample.csv中有 {len(sample_csv_ids)} 个样本ID")
    
    # 比较分析
    print("\n正在进行比较分析...")
    
    # 在Gene文件夹中但不在Sample.csv中的样本ID
    missing_in_sample = all_gene_sample_ids - sample_csv_ids
    
    # 在Sample.csv中但不在Gene文件夹中的样本ID
    missing_in_gene = sample_csv_ids - all_gene_sample_ids
    
    # 共同的样本ID
    common_samples = all_gene_sample_ids & sample_csv_ids
    
    # 输出结果
    print(f"\n=== 比较结果 ===")
    print(f"Gene文件夹中的样本ID总数: {len(all_gene_sample_ids)}")
    print(f"Sample.csv中的样本ID总数: {len(sample_csv_ids)}")
    print(f"共同的样本ID数量: {len(common_samples)}")
    print(f"Gene文件夹中有但Sample.csv中没有的: {len(missing_in_sample)}")
    print(f"Sample.csv中有但Gene文件夹中没有的: {len(missing_in_gene)}")
    
    # 保存Gene文件夹中有但Sample.csv中没有的样本ID
    if missing_in_sample:
        output_file = 'missing_in_sample.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID', 'Status'])
            
            for sample_id in sorted(missing_in_sample):
                writer.writerow([sample_id, 'Missing in Sample.csv'])
        
        print(f"\n缺失样本ID已保存到: {output_file}")
        
        # 显示前20个缺失的样本ID
        print(f"\n前20个Gene文件夹中有但Sample.csv中没有的样本ID:")
        for i, sample_id in enumerate(sorted(missing_in_sample)[:20]):
            print(f"  {i+1}. {sample_id}")
        
        if len(missing_in_sample) > 20:
            print(f"  ... 还有 {len(missing_in_sample) - 20} 个")
    
    # 保存Sample.csv中有但Gene文件夹中没有的样本ID
    if missing_in_gene:
        output_file2 = 'missing_in_gene.csv'
        with open(output_file2, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID', 'Status'])
            
            for sample_id in sorted(missing_in_gene):
                writer.writerow([sample_id, 'Missing in Gene folders'])
        
        print(f"\nSample.csv中有但Gene文件夹中没有的样本ID已保存到: {output_file2}")
        
        # 显示前20个
        print(f"\n前20个Sample.csv中有但Gene文件夹中没有的样本ID:")
        for i, sample_id in enumerate(sorted(missing_in_gene)[:20]):
            print(f"  {i+1}. {sample_id}")
        
        if len(missing_in_gene) > 20:
            print(f"  ... 还有 {len(missing_in_gene) - 20} 个")
    
    # 保存完整的比较报告
    report_file = 'comparison_report.csv'
    with open(report_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Sample_ID', 'In_Gene_Folders', 'In_Sample_CSV', 'Status'])
        
        # 所有样本ID的并集
        all_sample_ids = all_gene_sample_ids | sample_csv_ids
        
        for sample_id in sorted(all_sample_ids):
            in_gene = sample_id in all_gene_sample_ids
            in_sample = sample_id in sample_csv_ids
            
            if in_gene and in_sample:
                status = 'Common'
            elif in_gene and not in_sample:
                status = 'Missing in Sample.csv'
            elif not in_gene and in_sample:
                status = 'Missing in Gene folders'
            else:
                status = 'Unknown'
            
            writer.writerow([sample_id, in_gene, in_sample, status])
    
    print(f"\n完整比较报告已保存到: {report_file}")
    print("\n处理完成！")

if __name__ == "__main__":
    main()

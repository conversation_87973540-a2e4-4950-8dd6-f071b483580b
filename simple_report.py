#!/usr/bin/env python3

import csv
from datetime import datetime

def simple_report():
    print("=" * 80)
    print("🎯 转录本数据处理 - 最终统计报告")
    print("=" * 80)
    print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 统计原始文件
    try:
        with open('Transcript_id.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            original_count = sum(1 for row in reader)
        
        print(f"📋 原始CSV文件行数: {original_count:,}")
        print()
    except:
        print("❌ 无法读取原始CSV文件")
        print()
    
    # 统计处理结果
    try:
        with open('Transcript_id_processed_fast.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            
            total_rows = 0
            yes_count = 0
            false_count = 0
            gene_id_count = 0
            gene_name_count = 0
            
            for row in reader:
                total_rows += 1
                if len(row) >= 4:  # 确保有足够的列
                    # single_value 列（最后一列）
                    if row[-1] == 'yes':
                        yes_count += 1
                    elif row[-1] == 'false':
                        false_count += 1
                    
                    # ensembl_gene_id 列（第2列，索引1）
                    if len(row) > 1 and row[1] and row[1] != '':
                        gene_id_count += 1
                    
                    # external_gene_name 列（第3列，索引2）
                    if len(row) > 2 and row[2] and row[2] != '':
                        gene_name_count += 1
        
        print("📊 处理结果统计:")
        print(f"  总行数: {total_rows:,}")
        print(f"  single_value = 'yes': {yes_count:,} ({yes_count/total_rows*100:.2f}%)")
        print(f"  single_value = 'false': {false_count:,} ({false_count/total_rows*100:.2f}%)")
        print(f"  有基因ID: {gene_id_count:,} ({gene_id_count/total_rows*100:.2f}%)")
        print(f"  有基因名称: {gene_name_count:,} ({gene_name_count/total_rows*100:.2f}%)")
        print()
        
    except Exception as e:
        print(f"❌ 无法读取处理后的CSV文件: {e}")
        print()
    
    # 性能统计
    print("⚡ 处理性能:")
    total_time = 1777.54  # 秒
    total_rows = 251121
    speed = total_rows / total_time
    
    print(f"  总处理时间: {total_time:.2f} 秒 ({total_time/60:.1f} 分钟)")
    print(f"  处理速度: {speed:.0f} 行/秒")
    print()
    
    print("✅ 数据处理成功完成！")
    print("=" * 80)

if __name__ == "__main__":
    simple_report()

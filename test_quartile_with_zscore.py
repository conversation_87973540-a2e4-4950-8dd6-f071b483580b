#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试版本：基于Z-score的四分位数分组分析脚本（包含Z-score值）
"""

import pandas as pd
import numpy as np
import math
import os

def calculate_quartile_groups_and_zscores(data, value_column):
    """
    计算基于Z-score的四分位数分组，同时返回Z-score值
    """
    print(f"    处理列 {value_column}，数据行数: {len(data)}")
    
    # 步骤1：数据预处理和对数转换
    valid_data = data[data[value_column] > 0].copy()
    print(f"    有效数据行数: {len(valid_data)}")
    
    if len(valid_data) == 0:
        return ([''] * len(data), [np.nan] * len(data))
    
    # 对原始值取log2
    log_values = np.log2(valid_data[value_column])
    
    # 步骤2：计算 Z-score
    mean = np.mean(log_values)
    std_dev = np.std(log_values, ddof=1)
    
    if std_dev == 0:
        return (['Q2'] * len(data), [0.0] * len(data))
    
    # 计算每个数据点的 Z-score
    zscores = (log_values - mean) / std_dev
    
    # 步骤3：计算分位数阈值
    sorted_zscores = np.sort(zscores)
    n = len(sorted_zscores)
    
    q1_idx = int(n * 0.25)
    q2_idx = int(n * 0.5)
    q3_idx = int(n * 0.75)
    
    q1 = sorted_zscores[q1_idx]
    q2 = sorted_zscores[q2_idx]
    q3 = sorted_zscores[q3_idx]
    
    # 步骤4：分配四分位数等级和Z-score值
    quartile_groups = []
    zscore_values = []
    zscore_dict = dict(zip(valid_data.index, zscores))
    
    for idx in data.index:
        if idx in zscore_dict:
            zscore = zscore_dict[idx]
            zscore_values.append(zscore)
            
            if zscore <= q1:
                quartile_groups.append('Q1')
            elif zscore <= q2:
                quartile_groups.append('Q2')
            elif zscore <= q3:
                quartile_groups.append('Q3')
            else:
                quartile_groups.append('Q4')
        else:
            quartile_groups.append('')
            zscore_values.append(np.nan)
    
    print(f"    分配结果: Q1={quartile_groups.count('Q1')}, Q2={quartile_groups.count('Q2')}, Q3={quartile_groups.count('Q3')}, Q4={quartile_groups.count('Q4')}")
    
    return quartile_groups, zscore_values

def test_process():
    """
    测试处理函数
    """
    input_file = "/Volumes/zhy/整合的所有TPM文件/process_gene/gene_count_by_project_results_cleaned.csv"
    output_file = "/Volumes/zhy/整合的所有TPM文件/process_gene/gene_quartile_analysis_with_zscore_results.csv"
    
    print("开始测试处理...")
    print("正在读取数据文件...")
    
    try:
        # 读取CSV文件，只处理前1000行进行测试
        df = pd.read_csv(input_file, nrows=10000)
        print(f"测试数据读取成功，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        
        # 初始化结果列
        df['TE_Level'] = ''
        df['TR_Level'] = ''
        df['EVI_Level'] = ''
        df['TE_log2_zscore'] = np.nan
        df['TR_log2_zscore'] = np.nan
        df['EVI_log2_zscore'] = np.nan
        
        # 获取前几个project_id进行测试
        unique_projects = df['project_id'].unique()[:3]
        print(f"测试项目: {unique_projects}")
        
        # 按project_id分组处理
        for project_id in unique_projects:
            print(f"\n处理项目: {project_id}")
            project_data = df[df['project_id'] == project_id].copy()
            print(f"  项目数据行数: {len(project_data)}")
            
            # 处理TE列
            if project_data['TE'].notna().sum() > 0:
                print("  处理TE列...")
                te_levels, te_zscores = calculate_quartile_groups_and_zscores(project_data, 'TE')
                df.loc[df['project_id'] == project_id, 'TE_Level'] = te_levels
                df.loc[df['project_id'] == project_id, 'TE_log2_zscore'] = te_zscores
                print(f"  TE: 处理了 {len([x for x in te_levels if x])} 个有效值")
            
            # 处理TR列
            if project_data['TR'].notna().sum() > 0:
                print("  处理TR列...")
                tr_levels, tr_zscores = calculate_quartile_groups_and_zscores(project_data, 'TR')
                df.loc[df['project_id'] == project_id, 'TR_Level'] = tr_levels
                df.loc[df['project_id'] == project_id, 'TR_log2_zscore'] = tr_zscores
                print(f"  TR: 处理了 {len([x for x in tr_levels if x])} 个有效值")
            
            # 处理EVI列
            if project_data['EVI'].notna().sum() > 0:
                print("  处理EVI列...")
                evi_levels, evi_zscores = calculate_quartile_groups_and_zscores(project_data, 'EVI')
                df.loc[df['project_id'] == project_id, 'EVI_Level'] = evi_levels
                df.loc[df['project_id'] == project_id, 'EVI_log2_zscore'] = evi_zscores
                print(f"  EVI: 处理了 {len([x for x in evi_levels if x])} 个有效值")
        
        # 创建输出DataFrame
        output_df = df[['ensembl_gene_id', 'external_gene_name', 'project_id', 
                       'TE_Level', 'TR_Level', 'EVI_Level',
                       'TE_log2_zscore', 'TR_log2_zscore', 'EVI_log2_zscore']].copy()
        
        # 保存结果
        output_df.to_csv(output_file, index=False)
        print(f"\n测试结果已保存到: {output_file}")
        
        # 显示前几行结果
        print("\n前10行结果:")
        print(output_df.head(10))
        
        print(f"\n测试完成！输出文件包含 {len(output_df)} 行数据")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_process()

#!/usr/bin/env python3
import csv
from collections import defaultdict, Counter

def create_validation_report():
    # 检查原始文件
    gene_id_to_names = defaultdict(set)
    total_rows = 0
    
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/Trans_gene_name.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            total_rows += 1
            ensembl_gene_id = row['ensembl_gene_id'].strip()
            external_gene_name = row['external_gene_name'].strip()
            
            if ensembl_gene_id:
                gene_id_to_names[ensembl_gene_id].add(external_gene_name)
    
    # 检查多个名称的情况
    multiple_names_cases = []
    for gene_id, names in gene_id_to_names.items():
        valid_names = {name for name in names if name}
        if len(valid_names) > 1:
            multiple_names_cases.append((gene_id, list(valid_names)))
    
    # 检查生成的文件
    unique_ids = []
    empty_names = 0
    
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/unique_gene_names.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            gene_id = row['ensembl_gene_id'].strip()
            gene_name = row['external_gene_name'].strip()
            
            unique_ids.append(gene_id)
            
            if not gene_name:
                empty_names += 1
    
    # 检查重复
    id_counts = Counter(unique_ids)
    duplicate_ids = [gene_id for gene_id, count in id_counts.items() if count > 1]
    
    # 写入报告
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/validation_report.txt', 'w', encoding='utf-8') as f:
        f.write("=== 基因名称处理验证报告 ===\n\n")
        f.write(f"原始文件总行数: {total_rows}\n")
        f.write(f"唯一的 ensembl_gene_id 数量: {len(gene_id_to_names)}\n")
        f.write(f"一个 ID 对应多个名称的情况: {len(multiple_names_cases)} 个\n\n")

        if multiple_names_cases:
            f.write("前10个一对多的例子:\n")
            for i, (gene_id, names) in enumerate(multiple_names_cases[:10]):
                f.write(f"  {gene_id}: {names}\n")
            f.write("\n")

        f.write(f"生成文件总行数: {len(unique_ids)}\n")
        f.write(f"空基因名称的数量: {empty_names}\n")
        f.write(f"有效基因名称的数量: {len(unique_ids) - empty_names}\n\n")

        if duplicate_ids:
            f.write(f"警告: 发现重复的 ensembl_gene_id: {len(duplicate_ids)} 个\n")
            for gene_id in duplicate_ids[:5]:
                f.write(f"  {gene_id}: 出现 {id_counts[gene_id]} 次\n")
        else:
            f.write("✓ 没有发现重复的 ensembl_gene_id\n")

        f.write("\n处理完成！\n")

if __name__ == "__main__":
    create_validation_report()

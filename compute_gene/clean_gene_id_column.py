#!/usr/bin/env python3
"""
清理gene_statistical_analysis_results_median.csv文件的第一列
将"('ENSG00000000003',)"格式转换为"ENSG00000000003"
"""

import pandas as pd
import re
import ast

def clean_gene_id(gene_id_str):
    """清理基因ID，将元组格式转换为简单字符串"""
    if pd.isna(gene_id_str):
        return gene_id_str
    
    gene_id_str = str(gene_id_str).strip()
    
    # 如果已经是简单格式，直接返回
    if gene_id_str.startswith('ENSG') and not gene_id_str.startswith("('"):
        return gene_id_str
    
    # 处理元组格式
    if gene_id_str.startswith("('") and gene_id_str.endswith("',)"):
        # 使用正则表达式提取ENSG ID
        match = re.search(r"ENSG\d+", gene_id_str)
        if match:
            return match.group()
    
    # 尝试使用ast.literal_eval解析元组
    try:
        if gene_id_str.startswith('(') and gene_id_str.endswith(')'):
            parsed = ast.literal_eval(gene_id_str)
            if isinstance(parsed, tuple) and len(parsed) > 0:
                return str(parsed[0])
    except:
        pass
    
    # 如果无法解析，返回原始值
    return gene_id_str

def main():
    input_file = "gene_statistical_analysis_results_median.csv"
    output_file = "gene_statistical_analysis_results_median_cleaned.csv"
    
    print("开始处理文件...")
    
    try:
        # 读取文件
        print("读取文件...")
        df = pd.read_csv(input_file)
        print(f"文件包含 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        
        # 显示处理前的样本
        print("\n处理前的第一列样本:")
        print(df['ensembl_gene_id'].head(10))
        
        # 清理第一列
        print("\n开始清理第一列...")
        df['ensembl_gene_id'] = df['ensembl_gene_id'].apply(clean_gene_id)
        
        # 显示处理后的样本
        print("\n处理后的第一列样本:")
        print(df['ensembl_gene_id'].head(10))
        
        # 检查是否还有元组格式
        tuple_pattern = r"^\('.*',\)$"
        remaining_tuples = df['ensembl_gene_id'].str.match(tuple_pattern, na=False).sum()
        print(f"\n剩余元组格式的数量: {remaining_tuples}")
        
        if remaining_tuples > 0:
            print("警告: 仍有元组格式未处理")
            print("未处理的样本:")
            print(df[df['ensembl_gene_id'].str.match(tuple_pattern, na=False)]['ensembl_gene_id'].head())
        
        # 保存结果
        print(f"\n保存结果到 {output_file}...")
        df.to_csv(output_file, index=False)
        
        print("处理完成！")
        print(f"结果文件包含 {len(df)} 行数据")
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"唯一基因ID数量: {df['ensembl_gene_id'].nunique()}")
        print(f"基因ID格式示例:")
        print(df['ensembl_gene_id'].drop_duplicates().head(10).tolist())
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
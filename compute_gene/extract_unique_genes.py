#!/usr/bin/env python3
import csv

def extract_unique_genes():
    """
    从 Trans_gene_name.csv 文件中提取唯一的 ensembl_gene_id 和对应的 external_gene_name
    """
    # 用字典存储唯一的基因信息
    unique_genes = {}
    
    # 读取原始文件
    with open('gene_count_by_project_results.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            ensembl_gene_id = row['ensembl_gene_id'].strip()
            external_gene_name = row['external_gene_name'].strip()
            
            # 跳过空的 ensembl_gene_id
            if not ensembl_gene_id:
                continue
            
            # 如果这个基因ID还没有记录，或者当前记录有基因名而之前的记录没有
            if (ensembl_gene_id not in unique_genes or 
                (external_gene_name and not unique_genes[ensembl_gene_id])):
                unique_genes[ensembl_gene_id] = external_gene_name
    
    # 写入新文件
    output_file = 'unique_genes.csv'
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        
        # 写入表头
        writer.writerow(['ensembl_gene_id', 'external_gene_name'])
        
        # 按 ensembl_gene_id 排序并写入数据
        for ensembl_gene_id in sorted(unique_genes.keys()):
            external_gene_name = unique_genes[ensembl_gene_id]
            writer.writerow([ensembl_gene_id, external_gene_name])
    
    print(f"处理完成！")
    print(f"原始文件中的转录本数量: {sum(1 for line in open('gene_count_by_project_results.csv')) - 1}")
    print(f"提取的唯一基因数量: {len(unique_genes)}")
    print(f"输出文件: {output_file}")
    
    # 显示一些统计信息
    genes_with_names = sum(1 for name in unique_genes.values() if name)
    genes_without_names = len(unique_genes) - genes_with_names
    print(f"有基因名的基因: {genes_with_names}")
    print(f"没有基因名的基因: {genes_without_names}")
    
    # 显示前10个结果作为预览
    print("\n前10个结果预览:")
    print("ensembl_gene_id\texternal_gene_name")
    print("-" * 40)
    for i, (gene_id, gene_name) in enumerate(sorted(unique_genes.items())[:10]):
        print(f"{gene_id}\t{gene_name}")

if __name__ == "__main__":
    extract_unique_genes()

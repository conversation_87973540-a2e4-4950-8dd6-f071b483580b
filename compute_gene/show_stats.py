#!/usr/bin/env python3
import csv

def show_statistics():
    # 统计原始文件
    with open('compute_gene/Trans_gene_name.csv', 'r') as f:
        original_count = sum(1 for line in f) - 1  # 减去表头

    # 统计输出文件
    with open('compute_gene/unique_genes.csv', 'r') as f:
        reader = csv.DictReader(f)
        unique_genes = list(reader)
        
    unique_count = len(unique_genes)
    genes_with_names = sum(1 for gene in unique_genes if gene['external_gene_name'].strip())
    genes_without_names = unique_count - genes_with_names

    print(f'处理完成！')
    print(f'原始文件中的转录本数量: {original_count:,}')
    print(f'提取的唯一基因数量: {unique_count:,}')
    print(f'有基因名的基因: {genes_with_names:,}')
    print(f'没有基因名的基因: {genes_without_names:,}')
    print(f'输出文件: compute_gene/unique_genes.csv')

    print('\n前10个结果预览:')
    print('ensembl_gene_id\t\texternal_gene_name')
    print('-' * 50)
    for i, gene in enumerate(unique_genes[:10]):
        gene_name = gene['external_gene_name'] if gene['external_gene_name'].strip() else '(无基因名)'
        print(f'{gene["ensembl_gene_id"]}\t{gene_name}')

if __name__ == "__main__":
    show_statistics()

#!/usr/bin/env python3
"""
测试基因统计分析脚本的修改是否正确
"""

import csv
import os

def test_input_file_structure():
    """测试输入文件结构是否符合预期"""
    
    input_file = "gene_count_by_project_results_with_translation_indices.csv"
    
    print("=== 测试输入文件结构 ===")
    
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"文件列名: {headers}")
            
            # 检查必需的列
            required_columns = ['ensembl_gene_id', 'external_gene_name', 'project_id', 'count', 'TR', 'EVI', 'TE']
            missing_columns = []
            
            for col in required_columns:
                if col not in headers:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"❌ 缺少必需的列: {missing_columns}")
                return False
            else:
                print("✅ 所有必需的列都存在")
            
            # 读取前几行数据
            print("\n前3行数据:")
            for i, row in enumerate(reader):
                if i >= 3:
                    break
                print(f"  行 {i+1}:")
                print(f"    ensembl_gene_id: {row['ensembl_gene_id']}")
                print(f"    external_gene_name: {row['external_gene_name']}")
                print(f"    project_id: {row['project_id']}")
                print(f"    TE: {row['TE']}")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return False

def test_script_syntax():
    """测试脚本语法是否正确"""
    
    scripts = [
        "gene_statistical_analysis.py",
        "gene_statistical_analysis_original.py"
    ]
    
    print("=== 测试脚本语法 ===")
    
    for script in scripts:
        print(f"测试 {script}...")
        
        if not os.path.exists(script):
            print(f"❌ 脚本文件不存在: {script}")
            continue
        
        try:
            # 尝试编译脚本
            with open(script, 'r', encoding='utf-8') as f:
                code = f.read()
            
            compile(code, script, 'exec')
            print(f"✅ {script} 语法正确")
            
        except SyntaxError as e:
            print(f"❌ {script} 语法错误: {e}")
        except Exception as e:
            print(f"❌ {script} 其他错误: {e}")

def check_key_modifications():
    """检查关键修改是否正确应用"""
    
    print("=== 检查关键修改 ===")
    
    scripts = [
        "gene_statistical_analysis.py",
        "gene_statistical_analysis_original.py"
    ]
    
    for script in scripts:
        print(f"\n检查 {script}:")
        
        if not os.path.exists(script):
            print(f"❌ 脚本文件不存在: {script}")
            continue
        
        try:
            with open(script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含新的列名
            if 'ensembl_gene_id' in content:
                print("✅ 包含 ensembl_gene_id")
            else:
                print("❌ 缺少 ensembl_gene_id")
            
            if 'external_gene_name' in content:
                print("✅ 包含 external_gene_name")
            else:
                print("❌ 缺少 external_gene_name")
            
            # 检查是否更新了输入文件名
            if 'gene_count_by_project_results_with_translation_indices.csv' in content:
                print("✅ 输入文件名已更新")
            else:
                print("❌ 输入文件名未更新")
            
            # 检查是否移除了旧的 transcript_id 引用
            if 'transcript_id' in content:
                print("⚠️  仍包含 transcript_id 引用，请检查是否完全替换")
            else:
                print("✅ 已移除 transcript_id 引用")
            
            # 检查函数名是否更新
            if script == "gene_statistical_analysis.py":
                if 'analyze_gene_data' in content:
                    print("✅ 函数名已更新为 analyze_gene_data")
                else:
                    print("❌ 函数名未更新")
            
        except Exception as e:
            print(f"❌ 读取 {script} 时出错: {e}")

def main():
    """主测试函数"""
    
    print("=== 基因统计分析脚本修改验证 ===\n")
    
    # 测试输入文件结构
    file_ok = test_input_file_structure()
    
    print()
    
    # 测试脚本语法
    test_script_syntax()
    
    print()
    
    # 检查关键修改
    check_key_modifications()
    
    print("\n=== 测试完成 ===")
    
    if file_ok:
        print("✅ 输入文件结构正确，脚本可以运行")
    else:
        print("❌ 输入文件结构有问题，请检查")

if __name__ == "__main__":
    main()

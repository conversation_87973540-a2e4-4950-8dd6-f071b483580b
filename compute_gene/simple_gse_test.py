#!/usr/bin/env python3
import csv
from collections import defaultdict

def test_gse_file_structure():
    """测试新的GSE文件结构，不使用pandas"""
    
    file_path = 'GSE_match_new.csv'
    
    try:
        print("=== GSE文件结构测试 ===")
        print(f"文件路径: {file_path}")
        
        # 读取CSV文件
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"列数: {len(headers)}")
            print("\n列名:")
            for i, col in enumerate(headers):
                print(f"  {i+1}. {col}")
            
            # 检查关键列是否存在
            required_columns = ['Project ID', 'SRA Accession', 'Tissue/Cell Type', 'Cell line', 'Condition', 'Strategy']
            missing_columns = []
            
            for col in required_columns:
                if col not in headers:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"\n❌ 缺少必需的列: {missing_columns}")
                return False
            else:
                print(f"\n✅ 所有必需的列都存在")
            
            # 读取数据并进行统计
            rows = list(reader)
            total_rows = len(rows)
            
            print(f"总行数: {total_rows:,}")
            
            # 显示前5行数据
            print("\n前5行数据:")
            for i, row in enumerate(rows[:5]):
                print(f"  行 {i+1}:")
                for key, value in row.items():
                    print(f"    {key}: {value}")
                print()
            
            # 统计唯一值
            project_ids = set()
            sra_accessions = set()
            strategies = set()
            tissue_cell_types = set()
            cell_lines = set()
            conditions = set()
            
            for row in rows:
                project_ids.add(row['Project ID'])
                sra_accessions.add(row['SRA Accession'])
                strategies.add(row['Strategy'])
                
                tissue_cell_type = row['Tissue/Cell Type']
                if tissue_cell_type and tissue_cell_type != 'NA':
                    tissue_cell_types.add(tissue_cell_type)
                
                cell_line = row['Cell line']
                if cell_line and cell_line != 'NA':
                    cell_lines.add(cell_line)
                
                condition = row['Condition']
                if condition and condition != 'NA':
                    conditions.add(condition)
            
            print("=== 统计信息 ===")
            print(f"唯一项目数: {len(project_ids)}")
            print(f"唯一SRA数: {len(sra_accessions)}")
            print(f"唯一策略数: {len(strategies)}")
            print(f"策略类型: {list(strategies)}")
            print(f"唯一Tissue/Cell Type数: {len(tissue_cell_types)}")
            print(f"唯一Cell line数: {len(cell_lines)}")
            print(f"唯一Condition数: {len(conditions)}")
            
            # 测试分组功能
            print("\n=== 测试分组功能 ===")
            
            # 选择第一个项目进行测试
            test_project = list(project_ids)[0] if project_ids else None
            
            if test_project:
                print(f"测试项目: {test_project}")
                
                # 过滤出特定项目的行
                project_rows = [row for row in rows if row['Project ID'] == test_project]
                print(f"项目 {test_project} 的行数: {len(project_rows)}")
                
                # 创建分组
                groups = defaultdict(lambda: {'sra_accessions': [], 'strategies': set()})
                
                for row in project_rows:
                    # 处理空值和NA
                    tissue_cell_type = row['Tissue/Cell Type'] if row['Tissue/Cell Type'] not in ['', 'NA'] else None
                    cell_line = row['Cell line'] if row['Cell line'] not in ['', 'NA'] else None
                    condition = row['Condition'] if row['Condition'] not in ['', 'NA'] else None
                    
                    group_key = (tissue_cell_type, cell_line, condition)
                    groups[group_key]['sra_accessions'].append(row['SRA Accession'])
                    groups[group_key]['strategies'].add(row['Strategy'])
                
                print(f"项目 {test_project} 的分组数: {len(groups)}")
                
                # 显示前几个分组
                print("\n前5个分组:")
                for i, (group_key, group_info) in enumerate(groups.items()):
                    if i >= 5:
                        break
                    tissue_cell_type, cell_line, condition = group_key
                    sra_count = len(group_info['sra_accessions'])
                    strategies = list(group_info['strategies'])
                    
                    print(f"  分组 {i+1}:")
                    print(f"    Tissue/Cell Type: {tissue_cell_type}")
                    print(f"    Cell line: {cell_line}")
                    print(f"    Condition: {condition}")
                    print(f"    SRA数量: {sra_count}")
                    print(f"    策略: {strategies}")
                    print()
            
            print("✅ GSE文件结构测试完成")
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_gse_file_structure()

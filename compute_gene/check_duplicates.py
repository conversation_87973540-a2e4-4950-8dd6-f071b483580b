#!/usr/bin/env python3
import csv
from collections import defaultdict, Counter

def check_duplicates():
    # 检查原始文件中的重复情况
    print("=== 检查原始文件中的重复情况 ===")
    
    gene_id_to_names = defaultdict(set)
    total_rows = 0
    
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/Trans_gene_name.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            total_rows += 1
            ensembl_gene_id = row['ensembl_gene_id'].strip()
            external_gene_name = row['external_gene_name'].strip()
            
            if ensembl_gene_id:
                gene_id_to_names[ensembl_gene_id].add(external_gene_name)
    
    print(f"原始文件总行数: {total_rows}")
    print(f"唯一的 ensembl_gene_id 数量: {len(gene_id_to_names)}")
    
    # 检查一个 ID 对应多个名称的情况
    multiple_names_count = 0
    multiple_names_examples = []
    
    for gene_id, names in gene_id_to_names.items():
        # 移除空字符串
        valid_names = {name for name in names if name}
        
        if len(valid_names) > 1:
            multiple_names_count += 1
            if len(multiple_names_examples) < 10:  # 只显示前10个例子
                multiple_names_examples.append((gene_id, list(valid_names)))
    
    print(f"一个 ID 对应多个名称的情况: {multiple_names_count} 个")
    
    if multiple_names_examples:
        print("\n前10个例子:")
        for gene_id, names in multiple_names_examples:
            print(f"  {gene_id}: {names}")
    
    # 检查生成的文件
    print("\n=== 检查生成的唯一文件 ===")
    
    unique_ids = []
    unique_names = []
    empty_names = 0
    
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/unique_gene_names.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            gene_id = row['ensembl_gene_id'].strip()
            gene_name = row['external_gene_name'].strip()
            
            unique_ids.append(gene_id)
            unique_names.append(gene_name)
            
            if not gene_name:
                empty_names += 1
    
    print(f"生成文件总行数: {len(unique_ids)}")
    print(f"空基因名称的数量: {empty_names}")
    print(f"有效基因名称的数量: {len(unique_ids) - empty_names}")
    
    # 检查是否有重复的 ID
    id_counts = Counter(unique_ids)
    duplicate_ids = [gene_id for gene_id, count in id_counts.items() if count > 1]
    
    if duplicate_ids:
        print(f"警告: 发现重复的 ensembl_gene_id: {len(duplicate_ids)} 个")
        for gene_id in duplicate_ids[:5]:  # 只显示前5个
            print(f"  {gene_id}: 出现 {id_counts[gene_id]} 次")
    else:
        print("✓ 没有发现重复的 ensembl_gene_id")
    
    # 显示一些样本数据
    print("\n=== 生成文件的前10行样本 ===")
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/unique_gene_names.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for i, row in enumerate(reader):
            if i >= 10:
                break
            print(f"  {row['ensembl_gene_id']} -> {row['external_gene_name']}")

if __name__ == "__main__":
    check_duplicates()

#!/usr/bin/env python3
import csv
from collections import defaultdict

def add_gene_names_to_translation_indices():
    """
    为 translation_indices_results_grouped.csv 添加 external_gene_name 列
    通过 ensembl_gene_id 从 unique_gene_names.csv 查询对应的基因名称
    """
    
    # 文件路径
    gene_names_file = 'unique_gene_names.csv'
    input_file = 'translation_indices_results_grouped.csv'
    output_file = 'translation_indices_results_grouped_with_gene_names.csv'
    
    print("=== 为翻译指数结果添加基因名称 ===")
    
    # 第一步：读取基因名称映射
    print("正在读取基因名称映射...")
    gene_id_to_name = {}
    
    try:
        with open(gene_names_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                ensembl_gene_id = row['ensembl_gene_id'].strip()
                external_gene_name = row['external_gene_name'].strip()
                
                if ensembl_gene_id:
                    gene_id_to_name[ensembl_gene_id] = external_gene_name
        
        print(f"成功读取 {len(gene_id_to_name):,} 个基因ID到名称的映射")
        
    except Exception as e:
        print(f"读取基因名称文件时出错: {e}")
        return False
    
    # 第二步：处理翻译指数文件
    print("正在处理翻译指数文件...")
    
    try:
        matched_count = 0
        unmatched_count = 0
        total_rows = 0
        unmatched_examples = []
        
        with open(input_file, 'r', encoding='utf-8') as f_in, \
             open(output_file, 'w', encoding='utf-8', newline='') as f_out:
            
            reader = csv.DictReader(f_in)
            
            # 检查是否有 ensembl_gene_id 列
            if 'ensembl_gene_id' not in reader.fieldnames:
                print(f"错误: 输入文件中没有找到 'ensembl_gene_id' 列")
                print(f"可用的列: {reader.fieldnames}")
                return False
            
            # 创建新的列名（在最后添加 external_gene_name）
            new_headers = list(reader.fieldnames) + ['external_gene_name']
            writer = csv.DictWriter(f_out, fieldnames=new_headers)
            writer.writeheader()
            
            # 处理每一行
            for row in reader:
                total_rows += 1
                ensembl_gene_id = row['ensembl_gene_id'].strip()
                
                # 查找对应的基因名称
                if ensembl_gene_id in gene_id_to_name:
                    external_gene_name = gene_id_to_name[ensembl_gene_id]
                    matched_count += 1
                else:
                    external_gene_name = ''
                    unmatched_count += 1
                    if len(unmatched_examples) < 5:  # 只保存前5个未匹配的例子
                        unmatched_examples.append(ensembl_gene_id)
                
                # 添加基因名称到行数据
                row['external_gene_name'] = external_gene_name
                writer.writerow(row)
                
                # 进度显示
                if total_rows % 10000 == 0:
                    print(f"  已处理 {total_rows:,} 行...")
        
        # 输出统计信息
        print(f"\n处理完成!")
        print(f"总行数: {total_rows:,}")
        print(f"成功匹配: {matched_count:,}")
        print(f"未匹配: {unmatched_count:,}")
        print(f"匹配率: {matched_count/total_rows*100:.2f}%")
        
        if unmatched_examples:
            print(f"\n未匹配的基因ID示例:")
            for gene_id in unmatched_examples:
                print(f"  - {gene_id}")
        
        print(f"\n结果已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"处理翻译指数文件时出错: {e}")
        return False

def verify_results():
    """验证处理结果"""
    
    output_file = 'translation_indices_results_grouped_with_gene_names.csv'
    
    print("\n=== 验证处理结果 ===")
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"新文件的列名 (共 {len(headers)} 列):")
            for i, header in enumerate(headers):
                print(f"  {i+1}. {header}")
            
            # 检查前几行
            print(f"\n前5行数据示例:")
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n行 {i+1}:")
                print(f"  ensembl_gene_id: {row['ensembl_gene_id']}")
                print(f"  project_id: {row['project_id']}")
                print(f"  external_gene_name: {row['external_gene_name']}")
                print(f"  TR: {row['TR']}")
                print(f"  TE: {row['TE']}")
            
            # 统计空基因名称
            f.seek(0)  # 重置文件指针
            reader = csv.DictReader(f)
            next(reader)  # 跳过表头
            
            total_count = 0
            empty_name_count = 0
            
            for row in reader:
                total_count += 1
                if not row['external_gene_name'].strip():
                    empty_name_count += 1
            
            print(f"\n统计信息:")
            print(f"总行数: {total_count:,}")
            print(f"空基因名称: {empty_name_count:,}")
            print(f"有效基因名称: {total_count - empty_name_count:,}")
            print(f"有效基因名称比例: {(total_count - empty_name_count)/total_count*100:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"验证结果时出错: {e}")
        return False

def main():
    """主函数"""
    
    # 添加基因名称
    if add_gene_names_to_translation_indices():
        # 验证结果
        verify_results()
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()

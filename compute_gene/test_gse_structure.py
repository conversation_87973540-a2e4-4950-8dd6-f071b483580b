#!/usr/bin/env python3
import pandas as pd
import numpy as np

def test_gse_file_structure():
    """测试新的GSE文件结构"""
    
    file_path = 'GSE_match_new.csv'
    
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        print("=== GSE文件结构测试 ===")
        print(f"文件路径: {file_path}")
        print(f"总行数: {len(df):,}")
        print(f"列数: {len(df.columns)}")
        
        print("\n列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        print("\n前5行数据:")
        print(df.head())
        
        # 检查关键列是否存在
        required_columns = ['Project ID', 'SRA Accession', 'Tissue/Cell Type', 'Cell line', 'Condition', 'Strategy']
        missing_columns = []
        
        for col in required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"\n❌ 缺少必需的列: {missing_columns}")
        else:
            print(f"\n✅ 所有必需的列都存在")
        
        # 测试分组功能
        print("\n=== 测试分组功能 ===")
        
        # 选择一个项目进行测试
        project_ids = df['Project ID'].unique()
        test_project = project_ids[0] if len(project_ids) > 0 else None
        
        if test_project:
            print(f"测试项目: {test_project}")
            
            # 过滤出特定项目的行
            filtered_df = df[df['Project ID'] == test_project]
            print(f"项目 {test_project} 的行数: {len(filtered_df)}")
            
            # 将空字符串和'NA'替换为NaN
            filtered_df = filtered_df.replace(['NA', ''], np.nan)
            
            # 创建分组DataFrame
            df_for_grouping = filtered_df.copy()
            sentinel = "__NA__"  # 特殊标记
            
            # 替换NaN值 - 使用新的列名
            df_for_grouping['Tissue/Cell Type'] = df_for_grouping['Tissue/Cell Type'].fillna(sentinel)
            df_for_grouping['Cell line'] = df_for_grouping['Cell line'].fillna(sentinel)
            df_for_grouping['Condition'] = df_for_grouping['Condition'].fillna(sentinel)
            
            # 分组并收集SRA编号
            groups = {}
            for group_key, group_data in df_for_grouping.groupby(['Tissue/Cell Type', 'Cell line', 'Condition']):
                # 将特殊标记替换回None
                tissue_cell_type, cell_line, condition = group_key
                tissue_cell_type = None if tissue_cell_type == sentinel else tissue_cell_type
                cell_line = None if cell_line == sentinel else cell_line
                condition = None if condition == sentinel else condition
                
                # 获取该分组的SRA编号和策略类型
                group_info = {
                    'sra_accessions': group_data['SRA Accession'].tolist(),
                    'strategies': group_data['Strategy'].unique().tolist()
                }
                
                groups[(tissue_cell_type, cell_line, condition)] = group_info
            
            print(f"项目 {test_project} 的分组数: {len(groups)}")
            
            # 显示前几个分组
            print("\n前5个分组:")
            for i, (group_key, group_info) in enumerate(groups.items()):
                if i >= 5:
                    break
                tissue_cell_type, cell_line, condition = group_key
                sra_count = len(group_info['sra_accessions'])
                strategies = group_info['strategies']
                
                print(f"  分组 {i+1}:")
                print(f"    Tissue/Cell Type: {tissue_cell_type}")
                print(f"    Cell line: {cell_line}")
                print(f"    Condition: {condition}")
                print(f"    SRA数量: {sra_count}")
                print(f"    策略: {strategies}")
                print()
        
        # 统计信息
        print("=== 统计信息 ===")
        print(f"唯一项目数: {df['Project ID'].nunique()}")
        print(f"唯一SRA数: {df['SRA Accession'].nunique()}")
        print(f"唯一策略数: {df['Strategy'].nunique()}")
        print(f"策略类型: {df['Strategy'].unique().tolist()}")
        
        # 检查Tissue/Cell Type的唯一值
        tissue_cell_types = df['Tissue/Cell Type'].dropna().unique()
        print(f"唯一Tissue/Cell Type数: {len(tissue_cell_types)}")
        print(f"前10个Tissue/Cell Type: {tissue_cell_types[:10].tolist()}")
        
        # 检查Cell line的唯一值
        cell_lines = df['Cell line'].dropna().unique()
        print(f"唯一Cell line数: {len(cell_lines)}")
        print(f"前10个Cell line: {cell_lines[:10].tolist()}")
        
        # 检查Condition的唯一值
        conditions = df['Condition'].dropna().unique()
        print(f"唯一Condition数: {len(conditions)}")
        print(f"前10个Condition: {conditions[:10].tolist()}")
        
        print("\n✅ GSE文件结构测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gse_file_structure()

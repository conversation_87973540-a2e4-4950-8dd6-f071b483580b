#!/usr/bin/env python3
import csv
from collections import defaultdict

def process_gene_names():
    # 用于存储每个 ensembl_gene_id 对应的所有 external_gene_name
    gene_id_to_names = defaultdict(set)
    
    # 读取 CSV 文件
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/Trans_gene_name.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            ensembl_gene_id = row['ensembl_gene_id'].strip()
            external_gene_name = row['external_gene_name'].strip()
            
            # 只处理有效的 ensembl_gene_id
            if ensembl_gene_id:
                gene_id_to_names[ensembl_gene_id].add(external_gene_name)
    
    print(f"共处理了 {len(gene_id_to_names)} 个唯一的 ensembl_gene_id")
    
    # 检查是否存在一个 ID 对应多个名称的情况
    multiple_names = {}
    final_result = {}
    
    for gene_id, names in gene_id_to_names.items():
        # 移除空字符串
        names = {name for name in names if name}
        
        if len(names) > 1:
            multiple_names[gene_id] = list(names)
            print(f"警告: {gene_id} 对应多个名称: {list(names)}")
            # 对于多个名称的情况，选择第一个非空名称
            final_result[gene_id] = sorted(list(names))[0]
        elif len(names) == 1:
            final_result[gene_id] = list(names)[0]
        else:
            # 没有有效名称的情况
            final_result[gene_id] = ""
    
    print(f"\n发现 {len(multiple_names)} 个 ensembl_gene_id 对应多个名称")
    
    if multiple_names:
        print("\n详细信息:")
        for gene_id, names in multiple_names.items():
            print(f"  {gene_id}: {names}")
    
    # 保存结果到新的 CSV 文件
    with open('/Volumes/zhy/整合的所有TPM文件/compute_gene/unique_gene_names.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['ensembl_gene_id', 'external_gene_name'])
        
        for gene_id in sorted(final_result.keys()):
            writer.writerow([gene_id, final_result[gene_id]])
    
    print(f"\n结果已保存到 compute_gene/unique_gene_names.csv")
    print(f"共输出 {len(final_result)} 行数据")
    
    # 统计信息
    empty_names = sum(1 for name in final_result.values() if not name)
    print(f"其中 {empty_names} 个基因 ID 没有对应的基因名称")
    print(f"有效基因名称的数量: {len(final_result) - empty_names}")

if __name__ == "__main__":
    process_gene_names()

#!/usr/bin/env python3
"""
根据project_id查询project_unique_info.csv文件，获取相关信息并添加到gene结果文件中
"""

import pandas as pd
import os

def main():
    # 文件路径
    gene_file = "gene_count_by_project_results_with_translation_indices.csv"
    project_info_file = "../compute_transcript/project_unique_info.csv"
    output_file = "gene_count_by_project_results_with_translation_indices_and_project_info.csv"
    
    print("开始处理文件...")
    
    # 检查文件是否存在
    if not os.path.exists(gene_file):
        print(f"错误: 找不到文件 {gene_file}")
        return
    
    if not os.path.exists(project_info_file):
        print(f"错误: 找不到文件 {project_info_file}")
        return
    
    try:
        # 读取project信息文件
        print("读取project信息文件...")
        project_info_df = pd.read_csv(project_info_file)
        print(f"Project信息文件包含 {len(project_info_df)} 行数据")
        print(f"列名: {list(project_info_df.columns)}")
        
        # 检查必要的列是否存在
        required_cols = ['Project ID', 'Tissue/Cell Type', 'Cell line', 'Condition', 'Disease Category']
        missing_cols = [col for col in required_cols if col not in project_info_df.columns]
        if missing_cols:
            print(f"错误: Project信息文件缺少必要的列: {missing_cols}")
            return
        
        # 创建project_id到其他信息的映射
        print("创建project_id映射...")
        project_mapping = project_info_df.set_index('Project ID')[['Tissue/Cell Type', 'Cell line', 'Condition', 'Disease Category']].to_dict('index')
        
        # 读取gene结果文件（分块读取以处理大文件）
        print("读取gene结果文件...")
        chunk_size = 100000  # 每次读取10万行
        chunks = []
        
        for i, chunk in enumerate(pd.read_csv(gene_file, chunksize=chunk_size)):
            print(f"处理第 {i+1} 个数据块，包含 {len(chunk)} 行...")
            
            # 根据project_id添加信息
            chunk['Tissue_Cell_Type'] = chunk['project_id'].map(lambda x: project_mapping.get(x, {}).get('Tissue/Cell Type', 'NA'))
            chunk['Cell_line'] = chunk['project_id'].map(lambda x: project_mapping.get(x, {}).get('Cell line', 'NA'))
            chunk['Condition'] = chunk['project_id'].map(lambda x: project_mapping.get(x, {}).get('Condition', 'NA'))
            chunk['Disease_Category'] = chunk['project_id'].map(lambda x: project_mapping.get(x, {}).get('Disease Category', 'NA'))
            
            chunks.append(chunk)
        
        # 合并所有数据块
        print("合并所有数据块...")
        result_df = pd.concat(chunks, ignore_index=True)
        
        # 重新排列列的顺序
        column_order = [
            'ensembl_gene_id', 'external_gene_name', 'project_id', 
            'Tissue_Cell_Type', 'Cell_line', 'Condition', 'Disease_Category',
            'count', 'TR', 'EVI', 'TE'
        ]
        
        # 确保所有列都存在
        for col in column_order:
            if col not in result_df.columns:
                result_df[col] = 'NA'
        
        result_df = result_df[column_order]
        
        # 保存结果
        print(f"保存结果到 {output_file}...")
        result_df.to_csv(output_file, index=False)
        
        print("处理完成！")
        print(f"结果文件包含 {len(result_df)} 行数据")
        print(f"包含的project_id数量: {result_df['project_id'].nunique()}")
        
        # 显示一些统计信息
        print("\n统计信息:")
        print(f"疾病类别分布:")
        print(result_df['Disease_Category'].value_counts().head(10))
        
        print(f"\n条件分布:")
        print(result_df['Condition'].value_counts().head(10))
        
        print(f"\n组织/细胞类型分布:")
        print(result_df['Tissue_Cell_Type'].value_counts().head(10))
        
        print(f"\n细胞系分布:")
        print(result_df['Cell_line'].value_counts().head(10))
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
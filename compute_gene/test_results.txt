=== 基因统计分析脚本修改验证 ===

=== 测试输入文件结构 ===
文件列名: ['ensembl_gene_id', 'external_gene_name', 'project_id', 'count', 'TR', 'EVI', 'TE']
✅ 所有必需的列都存在

前3行数据:
  行 1:
    ensembl_gene_id: ENSG00000000003
    external_gene_name: TSPAN6
    project_id: TEDD00001
    TE: 3.7115243937016804

  行 2:
    ensembl_gene_id: ENSG00000000003
    external_gene_name: TSPAN6
    project_id: TEDD00002
    TE: 4.554636216659097

  行 3:
    ensembl_gene_id: ENSG00000000003
    external_gene_name: TSPAN6
    project_id: TEDD00003
    TE: 1.5645143865489237


=== 测试脚本语法 ===
测试 gene_statistical_analysis.py...
✅ gene_statistical_analysis.py 语法正确
测试 gene_statistical_analysis_original.py...
✅ gene_statistical_analysis_original.py 语法正确

=== 检查关键修改 ===

检查 gene_statistical_analysis.py:
✅ 包含 ensembl_gene_id
✅ 包含 external_gene_name
✅ 输入文件名已更新
✅ 已移除 transcript_id 引用
✅ 函数名已更新为 analyze_gene_data

检查 gene_statistical_analysis_original.py:
✅ 包含 ensembl_gene_id
✅ 包含 external_gene_name
✅ 输入文件名已更新
✅ 已移除 transcript_id 引用

=== 测试完成 ===
✅ 输入文件结构正确，脚本可以运行

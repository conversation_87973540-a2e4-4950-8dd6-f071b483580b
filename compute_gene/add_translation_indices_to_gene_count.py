#!/usr/bin/env python3
import csv
from collections import defaultdict

def add_translation_indices_to_gene_count():
    """
    为 gene_count_by_project_results.csv 添加 TR, EVI, TE 三列
    通过 (ensembl_gene_id, project_id) 从 translation_indices_results_grouped.csv 查询对应数据
    """
    
    # 文件路径
    translation_indices_file = 'translation_indices_results_grouped.csv'
    gene_count_file = 'gene_count_by_project_results.csv'
    output_file = 'gene_count_by_project_results_with_translation_indices.csv'
    
    print("=== 为基因计数结果添加翻译指数 ===")
    
    # 第一步：读取翻译指数数据并建立索引
    print("正在读取翻译指数数据...")
    translation_indices = {}  # key: (ensembl_gene_id, project_id), value: (TR, EVI, TE)
    
    try:
        with open(translation_indices_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            row_count = 0
            for row in reader:
                row_count += 1
                ensembl_gene_id = row['ensembl_gene_id'].strip()
                project_id = row['project_id'].strip()
                tr = row['TR'].strip()
                evi = row['EVI'].strip()
                te = row['TE'].strip()
                
                # 创建复合键
                key = (ensembl_gene_id, project_id)
                
                # 存储翻译指数数据
                translation_indices[key] = (tr, evi, te)
                
                # 进度显示
                if row_count % 100000 == 0:
                    print(f"  已读取 {row_count:,} 行翻译指数数据...")
        
        print(f"成功读取 {row_count:,} 行翻译指数数据")
        print(f"建立了 {len(translation_indices):,} 个唯一的 (基因ID, 项目ID) 组合索引")
        
    except Exception as e:
        print(f"读取翻译指数文件时出错: {e}")
        return False
    
    # 第二步：处理基因计数文件
    print("正在处理基因计数文件...")
    
    try:
        matched_count = 0
        unmatched_count = 0
        total_rows = 0
        unmatched_examples = []
        
        with open(gene_count_file, 'r', encoding='utf-8') as f_in, \
             open(output_file, 'w', encoding='utf-8', newline='') as f_out:
            
            reader = csv.DictReader(f_in)
            
            # 检查必需的列
            required_columns = ['ensembl_gene_id', 'project_id']
            missing_columns = []
            for col in required_columns:
                if col not in reader.fieldnames:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"错误: 输入文件中缺少必需的列: {missing_columns}")
                print(f"可用的列: {reader.fieldnames}")
                return False
            
            # 创建新的列名（添加 TR, EVI, TE）
            new_headers = list(reader.fieldnames) + ['TR', 'EVI', 'TE']
            writer = csv.DictWriter(f_out, fieldnames=new_headers)
            writer.writeheader()
            
            # 处理每一行
            for row in reader:
                total_rows += 1
                ensembl_gene_id = row['ensembl_gene_id'].strip()
                project_id = row['project_id'].strip()
                
                # 创建查询键
                key = (ensembl_gene_id, project_id)
                
                # 查找对应的翻译指数
                if key in translation_indices:
                    tr, evi, te = translation_indices[key]
                    matched_count += 1
                else:
                    tr, evi, te = '', '', ''
                    unmatched_count += 1
                    if len(unmatched_examples) < 5:  # 只保存前5个未匹配的例子
                        unmatched_examples.append((ensembl_gene_id, project_id))
                
                # 添加翻译指数到行数据
                row['TR'] = tr
                row['EVI'] = evi
                row['TE'] = te
                writer.writerow(row)
                
                # 进度显示
                if total_rows % 10000 == 0:
                    print(f"  已处理 {total_rows:,} 行...")
        
        # 输出统计信息
        print(f"\n处理完成!")
        print(f"总行数: {total_rows:,}")
        print(f"成功匹配: {matched_count:,}")
        print(f"未匹配: {unmatched_count:,}")
        print(f"匹配率: {matched_count/total_rows*100:.2f}%")
        
        if unmatched_examples:
            print(f"\n未匹配的 (基因ID, 项目ID) 示例:")
            for gene_id, project_id in unmatched_examples:
                print(f"  - ({gene_id}, {project_id})")
        
        print(f"\n结果已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"处理基因计数文件时出错: {e}")
        return False

def verify_results():
    """验证处理结果"""
    
    output_file = 'gene_count_by_project_results_with_translation_indices.csv'
    
    print("\n=== 验证处理结果 ===")
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"新文件的列名 (共 {len(headers)} 列):")
            for i, header in enumerate(headers):
                print(f"  {i+1}. {header}")
            
            # 检查前几行
            print(f"\n前5行数据示例:")
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n行 {i+1}:")
                print(f"  ensembl_gene_id: {row['ensembl_gene_id']}")
                print(f"  external_gene_name: {row['external_gene_name']}")
                print(f"  project_id: {row['project_id']}")
                print(f"  count: {row['count']}")
                print(f"  TR: {row['TR']}")
                print(f"  EVI: {row['EVI']}")
                print(f"  TE: {row['TE']}")
            
            # 统计翻译指数数据
            f.seek(0)  # 重置文件指针
            reader = csv.DictReader(f)
            next(reader)  # 跳过表头
            
            total_count = 0
            empty_tr_count = 0
            empty_evi_count = 0
            empty_te_count = 0
            all_empty_count = 0
            
            for row in reader:
                total_count += 1
                tr_empty = not row['TR'].strip()
                evi_empty = not row['EVI'].strip()
                te_empty = not row['TE'].strip()
                
                if tr_empty:
                    empty_tr_count += 1
                if evi_empty:
                    empty_evi_count += 1
                if te_empty:
                    empty_te_count += 1
                if tr_empty and evi_empty and te_empty:
                    all_empty_count += 1
            
            print(f"\n统计信息:")
            print(f"总行数: {total_count:,}")
            print(f"空 TR 值: {empty_tr_count:,} ({empty_tr_count/total_count*100:.2f}%)")
            print(f"空 EVI 值: {empty_evi_count:,} ({empty_evi_count/total_count*100:.2f}%)")
            print(f"空 TE 值: {empty_te_count:,} ({empty_te_count/total_count*100:.2f}%)")
            print(f"所有翻译指数都为空: {all_empty_count:,} ({all_empty_count/total_count*100:.2f}%)")
            print(f"至少有一个翻译指数有值: {total_count - all_empty_count:,} ({(total_count - all_empty_count)/total_count*100:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"验证结果时出错: {e}")
        return False

def main():
    """主函数"""
    
    # 添加翻译指数
    if add_translation_indices_to_gene_count():
        # 验证结果
        verify_results()
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()

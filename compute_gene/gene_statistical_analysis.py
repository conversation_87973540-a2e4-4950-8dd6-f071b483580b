#!/usr/bin/env python3
"""
基因翻译效率统计分析脚本 - 按疾病大类分析版本
对gene_count_by_project_results_with_translation_indices.csv文件按疾病大类进行统计分析
"""

import pandas as pd
import numpy as np
from scipy import stats
from statsmodels.stats.multitest import multipletests
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from functools import lru_cache
warnings.filterwarnings('ignore')

# 疾病大类与condition的映射关系
DISEASE_CATEGORIES = {
    "Infectious Disease": [
        "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
        "Adult Hepatocellular Carcinoma; HCV Transfection",
        "B95-8 Epstein-Barr Virus Infection",
        "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
        "Human Cytomegalovirus Infection",
        "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
        "IAV Transfection",
        "Lung Adenocarcinoma; IAV Infection",
        "Lung Adenocarcinoma; SARS-CoV-2 Infection",
        "M81 Epstein-Barr Virus Infection",
        "Toxoplasma Infection"
    ],
    "Gastrointestinal System Cancer": [
        "Adult Hepatocellular Carcinoma",
        "Childhood Hepatocellular Carcinoma",
        "Colon Adenocarcinoma",
        "Colon Carcinoma",
        "Esophageal Squamous Cell Carcinoma",
        "Hepatoblastoma",
        "Hepatocellular Carcinoma",
        "Intrahepatic Cholangiocarcinoma"
    ],
    "Musculoskeletal System Cancer": [
        "Osteosarcoma"
    ],
    "Reproductive Organ Cancer": [
        "Human Papillomavirus-related Endocervical Adenocarcinoma",
        "Ovarian Cancer",
        "Ovarian Endometrioid Carcinoma",
        "Prostate Cancer",
        "Prostate Carcinoma"
    ],
    "Respiratory System Cancer": [
        "Lung Adenocarcinoma",
        "Lung Large Cell Carcinoma"
    ],
    "Urinary System Cancer": [
        "Kidney Rhabdoid Cancer",
        "Kidney Tumor",
        "Renal Cell Carcinoma"
    ],
    "Genetic Disease": [
        "Duchenne Muscular Dystrophy",
        "Hbs1L Deficiency",
        "Roberts Syndrome",
        "Treacher Collins Syndrome",
        "Tuberous Sclerosis Complex"
    ],
    "Other": [
        "Cancer-derived"
    ],
    "Nervous System Cancer": [
        "Astrocytoma",
        "Brain Glioma",
        "Neuroblastoma"
    ],
    "Hematologic Cancer": [
        "Acute Myeloid Leukemia",
        "Adult Acute Myeloid Leukemia",
        "Childhood T Acute Lymphoblastic Leukemia",
        "Childhood T Lymphoblastic Lymphoma",
        "Chronic Myeloid Leukemia",
        "Multiple Myeloma"
    ],
    "Breast Cancer": [
        "Breast Adenocarcinoma",
        "Breast Carcinoma"
    ],
    "Head And Neck Cancer": [
        "Head And Neck Squamous Cell Carcinoma",
        "Tongue Squamous Cell Carcinoma"
    ],
    "Endocrine Gland Cancer": [
        "Pancreatic Adenocarcinoma"
    ]
}

@lru_cache(maxsize=None)
def create_condition_to_category_mapping():
    """创建condition到疾病大类的映射字典 - 使用缓存优化"""
    condition_to_category = {}
    for category, conditions in DISEASE_CATEGORIES.items():
        for condition in conditions:
            condition_to_category[condition] = category
    return condition_to_category

def load_and_explore_data(file_path):
    """优化的数据加载函数"""
    print("正在加载数据...")
    try:
        start_time = time.time()
        
        # 只读取必要的列来减少内存使用
        df_sample = pd.read_csv(file_path, nrows=5)
        required_cols = ['ensembl_gene_id', 'external_gene_name', 'Condition', 'TE', 'TR', 'EVI']
        available_cols = [col for col in required_cols if col in df_sample.columns]

        print(f"检测到的必要列: {available_cols}")

        # 优化数据类型，只加载需要的列
        dtype_dict = {'ensembl_gene_id': 'string', 'external_gene_name': 'string', 'Condition': 'string'}
        
        df = pd.read_csv(file_path, usecols=available_cols, dtype=dtype_dict, low_memory=False)
        
        load_time = time.time() - start_time
        print(f"数据加载完成！用时: {load_time:.2f}秒")
        print(f"数据形状: {df.shape}")
        print(f"内存使用: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def add_disease_category(df):
    """为数据添加疾病大类列"""
    print("\n添加疾病大类列...")
    condition_to_category = create_condition_to_category_mapping()
    
    df['disease_category'] = df['Condition'].map(condition_to_category)
    
    unmapped_conditions = df[df['disease_category'].isna()]['Condition'].unique()
    if len(unmapped_conditions) > 0:
        print(f"未映射的条件: {unmapped_conditions}")
        df.loc[df['disease_category'].isna(), 'disease_category'] = 'Unknown'
    
    print(f"疾病大类分布:")
    print(df['disease_category'].value_counts())
    
    return df

def preprocess_data(df):
    """优化的数据预处理"""
    print("\n开始数据预处理...")
    start_time = time.time()

    required_cols = ['ensembl_gene_id', 'external_gene_name', 'Condition']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        return None

    # 批量转换数据类型
    try:
        df = df.copy()  # 避免SettingWithCopyWarning
        df['ensembl_gene_id'] = df['ensembl_gene_id'].astype('string')
        df['external_gene_name'] = df['external_gene_name'].astype('string')
        df['Condition'] = df['Condition'].astype('string')
        print("字符串列数据类型转换完成")
    except Exception as e:
        print(f"字符串列数据类型转换失败: {e}")
        return None

    # 添加疾病大类
    df = add_disease_category(df)

    # 获取可用的分析列
    analysis_cols = ['TE', 'TR', 'EVI']
    available_cols = [col for col in analysis_cols if col in df.columns]
    print(f"可用于分析的变量列: {available_cols}")

    if not available_cols:
        print("未找到TE、TR、EVI列，请检查数据格式")
        return None

    # 批量转换数值列
    for col in available_cols:
        try:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            print(f"列 {col} 数据类型转换完成，缺失值数量: {df[col].isnull().sum()}")
        except Exception as e:
            print(f"列 {col} 数据类型转换失败: {e}")
            return None

    # 预先过滤掉没有Normal对照的基因
    normal_genes = set(df[df['Condition'] == 'Normal']['ensembl_gene_id'].unique())
    df = df[df['ensembl_gene_id'].isin(normal_genes)]
    print(f"过滤后保留 {df['ensembl_gene_id'].nunique()} 个有Normal对照的基因")

    disease_categories = df['disease_category'].unique()
    test_categories = [cat for cat in disease_categories if cat != 'Unknown']
    print(f"测试疾病大类: {test_categories}")

    preprocess_time = time.time() - start_time
    print(f"数据预处理完成，用时: {preprocess_time:.2f}秒")

    return df, available_cols, test_categories

def perform_statistical_tests_batch(data_groups):
    """批量执行统计检验 - 优化版本"""
    results = []

    for (ensembl_gene_id, external_gene_name, disease_category, variable, category_values, normal_values) in data_groups:
        # 快速数据验证
        if len(category_values) < 2 or len(normal_values) < 2:
            continue
            
        if not (np.isfinite(category_values).all() and np.isfinite(normal_values).all()):
            continue

        result = {
            'ensembl_gene_id': ensembl_gene_id,
            'external_gene_name': external_gene_name,
            'disease_category': disease_category,
            'variable': variable,
            'p_t': np.nan,
            'p_wilcox': np.nan,
            'p_ks': np.nan,
            'direction': 'unknown'
        }

        try:
            # 并行执行统计检验
            t_stat, p_t = stats.ttest_ind(category_values, normal_values)
            if np.isfinite(p_t):
                result['p_t'] = float(p_t)

            u_stat, p_wilcox = stats.mannwhitneyu(category_values, normal_values, alternative='two-sided')
            if np.isfinite(p_wilcox):
                result['p_wilcox'] = float(p_wilcox)

            ks_stat, p_ks = stats.ks_2samp(category_values, normal_values)
            if np.isfinite(p_ks):
                result['p_ks'] = float(p_ks)

            # 使用numpy的中位数计算更快
            category_median = np.median(category_values)
            normal_median = np.median(normal_values)

            result['direction'] = 'higher' if category_median > normal_median else 'lower'

            results.append(result)
            
        except Exception as e:
            continue
    
    return results

def analyze_gene_data(df, available_cols, test_categories):
    """优化的基因数据分析"""
    print("\n开始统计分析...")
    start_time = time.time()

    # 预处理：分离Normal数据和测试数据
    normal_df = df[df['Condition'] == 'Normal'].copy()
    test_df = df[df['disease_category'].isin(test_categories)].copy()

    print(f"Normal数据: {len(normal_df)} 行")
    print(f"测试数据: {len(test_df)} 行")

    # 构建数据批次用于批量处理
    data_batches = []
    batch_size = 1000  # 每批处理1000个测试

    # 使用更高效的groupby操作
    normal_grouped = normal_df.groupby('ensembl_gene_id')
    test_grouped = test_df.groupby(['ensembl_gene_id', 'disease_category'])
    
    total_combinations = 0

    for (ensembl_gene_id, category), test_group in test_grouped:
        if ensembl_gene_id not in normal_grouped.groups:
            continue

        normal_group = normal_grouped.get_group(ensembl_gene_id)

        # 获取基因名称
        external_gene_name = test_group['external_gene_name'].iloc[0] if len(test_group) > 0 else ''

        for variable in available_cols:
            # 预先清理数据
            normal_values = pd.to_numeric(normal_group[variable], errors='coerce').dropna().values
            category_values = pd.to_numeric(test_group[variable], errors='coerce').dropna().values

            if len(normal_values) >= 2 and len(category_values) >= 2:
                data_batches.append((ensembl_gene_id, external_gene_name, category, variable, category_values, normal_values))
                total_combinations += 1
    
    print(f"准备处理 {total_combinations} 个统计检验")
    
    # 批量处理统计检验
    all_results = []
    
    # 分批处理以避免内存问题
    for i in range(0, len(data_batches), batch_size):
        batch = data_batches[i:i+batch_size]
        batch_results = perform_statistical_tests_batch(batch)
        all_results.extend(batch_results)
        
        if i % (batch_size * 5) == 0:
            progress = (i + len(batch)) / len(data_batches) * 100
            print(f"处理进度: {progress:.1f}% ({len(all_results)} 个结果)")
    
    analysis_time = time.time() - start_time
    print(f"统计分析完成！用时: {analysis_time:.2f}秒，共生成 {len(all_results)} 个结果")
    
    return all_results

def apply_fdr_correction(results_df):
    """应用FDR多重检验校正"""
    print("\n应用FDR多重检验校正...")
    
    test_types = ['p_t', 'p_wilcox', 'p_ks']
    
    for test_type in test_types:
        p_values = results_df[test_type].dropna()
        
        if len(p_values) > 0:
            rejected, p_corrected, _, _ = multipletests(p_values, method='fdr_bh')
            
            fdr_col = f'fdr_{test_type[2:]}'
            results_df[fdr_col] = np.nan
            results_df.loc[p_values.index, fdr_col] = p_corrected
    
    return results_df

def save_results(results_df, output_file):
    """保存结果"""
    print(f"\n保存结果到: {output_file}")
    
    column_order = [
        'ensembl_gene_id', 'external_gene_name', 'disease_category', 'variable',
        'p_t', 'fdr_t', 'p_wilcox', 'fdr_wilcox', 'p_ks', 'fdr_ks',
        'direction'
    ]
    
    for col in column_order:
        if col not in results_df.columns:
            results_df[col] = np.nan
    
    results_df = results_df[column_order]
    
    results_df.to_csv(output_file, index=False)
    
    print("结果保存完成！")
    print(f"结果文件包含 {len(results_df)} 行数据")
    
    print("\n结果摘要:")
    print(f"分析的基因数量: {results_df['ensembl_gene_id'].nunique()}")
    print(f"分析的疾病大类数量: {results_df['disease_category'].nunique()}")
    print(f"分析的变量类型: {results_df['variable'].nunique()}")
    
    print(f"\n疾病大类分布:")
    print(results_df['disease_category'].value_counts())
    
    for test_type in ['fdr_t', 'fdr_wilcox', 'fdr_ks']:
        if test_type in results_df.columns:
            significant = (results_df[test_type] < 0.05).sum()
            total = results_df[test_type].notna().sum()
            print(f"{test_type} 显著性结果 (p<0.05): {significant}/{total}")

def main():
    """主函数"""
    input_file = "gene_count_by_project_results_with_translation_indices_and_project_info.csv"
    output_file = "gene_statistical_analysis_by_disease_category.csv"

    print("=== 基因翻译效率统计分析 - 按疾病大类 ===")

    df = load_and_explore_data(input_file)
    if df is None:
        return

    preprocessing_result = preprocess_data(df)
    if preprocessing_result is None:
        return

    df, available_cols, test_categories = preprocessing_result

    results = analyze_gene_data(df, available_cols, test_categories)
    if results is None or len(results) == 0:
        print("未生成任何分析结果")
        return
    
    results_df = pd.DataFrame(results)
    
    results_df = apply_fdr_correction(results_df)
    
    save_results(results_df, output_file)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
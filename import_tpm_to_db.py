import os
import mysql.connector
from mysql.connector import Error

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

BATCH_SIZE = 10000  # 每次批量插入的记录数

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None


def reset_table(connection):
    """删除已存在的表并创建新表"""
    drop_query = "DROP TABLE IF EXISTS Gene_TPM;"
    create_query = """
    CREATE TABLE Gene_TPM (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sraAccession VARCHAR(50) NOT NULL,
        geneId VARCHAR(50) NOT NULL,
        tpm FLOAT NOT NULL,
        externalGeneName VARCHAR(100) NOT NULL,
        INDEX idx_sra (sraAccession),
        INDEX idx_gene (geneId),
        INDEX idx_external_name (externalGeneName)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """
    try:
        cursor = connection.cursor()
        cursor.execute(drop_query)
        cursor.execute(create_query)
        connection.commit()
        print("表 Gene_TPM 已重建")
        return True
    except Error as e:
        print(f"重建表错误: {e}")
        return False


def process_tpm_files(connection):
    """批量导入目录中的所有TPM文件"""
    tpm_dir = "Gene"  # 当前目录
    if not os.path.exists(tpm_dir):
        print(f"目录 {tpm_dir} 不存在!")
        return

    insert_query = (
        "INSERT INTO Gene_TPM "
        "(sraAccession, geneId, tpm, externalGeneName) "
        "VALUES (%s, %s, %s, %s)"
    )
    cursor = connection.cursor()

    for filename in os.listdir(tpm_dir):
        if not (filename.endswith("_tpm.txt") or filename.endswith("_final_merged_tpm.txt") or filename.endswith("_final_merged_gene_tpm.txt")):
            continue
        file_path = os.path.join(tpm_dir, filename)
        print(f"正在处理文件: {filename}")
        sra_accession = filename.split('_')[0]

        batch_records = []
        total_lines = 0
        imported_lines = 0

        with open(file_path, 'r') as file:
            next(file)  # 跳过表头
            for line in file:
                total_lines += 1
                parts = line.strip().split('\t')
                if len(parts) < 4:
                    continue
                try:
                    tpm = float(parts[1])
                except ValueError:
                    continue
                if tpm < 1:
                    continue
                batch_records.append((
                    sra_accession,
                    parts[0],  # Geneid
                    tpm,
                    parts[2]   # external_gene_name
                ))
                if len(batch_records) >= BATCH_SIZE:
                    try:
                        cursor.executemany(insert_query, batch_records)
                        connection.commit()
                        imported_lines += len(batch_records)
                    except Error as e:
                        print(f"批量插入错误: {e}")
                    batch_records.clear()

        # 插入剩余记录
        if batch_records:
            try:
                cursor.executemany(insert_query, batch_records)
                connection.commit()
                imported_lines += len(batch_records)
            except Error as e:
                print(f"批量插入错误: {e}")

        print(f"文件 {filename} 处理完成: 总行数 {total_lines}, 导入行数 {imported_lines}")


def main():
    connection = create_connection()
    if not connection:
        return

    if reset_table(connection):
        process_tpm_files(connection)

    if connection.is_connected():
        connection.close()
        print("数据库连接已关闭")


if __name__ == "__main__":
    main()

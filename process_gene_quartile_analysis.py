#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Z-score的四分位数分组分析脚本
处理translation_indices_results_grouped_cleaned.csv文件
按照projectId分组，对TE、TR、EVI值进行四分位数分析
"""

import pandas as pd
import numpy as np
import math
import os

def calculate_quartile_groups_and_zscores(data, value_column):
    """
    计算基于Z-score的四分位数分组，同时返回Z-score值

    Args:
        data: DataFrame，包含数据
        value_column: str，要分析的列名（TE、TR或EVI）

    Returns:
        tuple: (quartile_groups, zscore_values)
    """
    # 步骤1：数据预处理和对数转换
    # 过滤出有效数据并进行对数转换
    valid_data = data[data[value_column] > 0].copy()

    if len(valid_data) == 0:
        return ([''] * len(data), [np.nan] * len(data))

    # 对原始值取log2
    log_values = np.log2(valid_data[value_column])

    # 步骤2：计算 Z-score
    # 计算均值
    mean = np.mean(log_values)

    # 计算样本标准差（除以 n-1）
    std_dev = np.std(log_values, ddof=1)

    if std_dev == 0:
        return (['Q2'] * len(data), [0.0] * len(data))  # 如果标准差为0，都分配到Q2，Z-score为0

    # 计算每个数据点的 Z-score
    zscores = (log_values - mean) / std_dev

    # 步骤3：计算分位数阈值
    # 对 Z-score 进行排序
    sorted_zscores = np.sort(zscores)
    n = len(sorted_zscores)

    # 计算分位数索引（Python 方式）
    q1_idx = int(n * 0.25)  # 25% 分位数索引
    q2_idx = int(n * 0.5)   # 50% 分位数索引（中位数）
    q3_idx = int(n * 0.75)  # 75% 分位数索引

    # 获取分位数值
    q1 = sorted_zscores[q1_idx]
    q2 = sorted_zscores[q2_idx]
    q3 = sorted_zscores[q3_idx]

    # 步骤4：分配四分位数等级和Z-score值
    quartile_groups = []
    zscore_values = []
    zscore_dict = dict(zip(valid_data.index, zscores))

    for idx in data.index:
        if idx in zscore_dict:
            zscore = zscore_dict[idx]
            zscore_values.append(zscore)

            if zscore <= q1:
                quartile_groups.append('Q1')      # 最低 25%
            elif zscore <= q2:
                quartile_groups.append('Q2')      # 25%-50%
            elif zscore <= q3:
                quartile_groups.append('Q3')      # 50%-75%
            else:
                quartile_groups.append('Q4')      # 最高 25%
        else:
            quartile_groups.append('')  # 无效数据
            zscore_values.append(np.nan)  # 无效数据的Z-score为NaN

    return quartile_groups, zscore_values

def process_gene_data():
    """
    处理转录本数据文件
    """
    input_file = "/Volumes/zhy/整合的所有TPM文件/process_transcript/translation_indices_results_grouped_cleaned.csv"
    output_file = "/Volumes/zhy/整合的所有TPM文件/process_transcript/transcript_quartile_analysis_results.csv"
    
    print("正在读取数据文件...")
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"成功读取数据，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        
        # 检查数据结构
        print("\n数据预览:")
        print(df.head())
        
        # 检查te、tr、evi列的数据情况
        print(f"\nte列非空值数量: {df['te'].notna().sum()}")
        print(f"tr列非空值数量: {df['tr'].notna().sum()}")
        print(f"evi列非空值数量: {df['evi'].notna().sum()}")
        
        # 获取所有唯一的projectId
        unique_projects = df['projectId'].unique()
        print(f"\n共有 {len(unique_projects)} 个不同的projectId")
        
        # 初始化结果列
        df['te_Level'] = ''
        df['tr_Level'] = ''
        df['evi_Level'] = ''
        df['te_log2_zscore'] = np.nan
        df['tr_log2_zscore'] = np.nan
        df['evi_log2_zscore'] = np.nan
        
        # 按projectId分组处理
        for project_id in unique_projects:
            print(f"\n处理项目: {project_id}")
            project_data = df[df['projectId'] == project_id].copy()
            
            # 处理te列
            if project_data['te'].notna().sum() > 0:
                te_levels, te_zscores = calculate_quartile_groups_and_zscores(project_data, 'te')
                df.loc[df['projectId'] == project_id, 'te_Level'] = te_levels
                df.loc[df['projectId'] == project_id, 'te_log2_zscore'] = te_zscores
                print(f"  te: 处理了 {len([x for x in te_levels if x])} 个有效值")

            # 处理tr列
            if project_data['tr'].notna().sum() > 0:
                tr_levels, tr_zscores = calculate_quartile_groups_and_zscores(project_data, 'tr')
                df.loc[df['projectId'] == project_id, 'tr_Level'] = tr_levels
                df.loc[df['projectId'] == project_id, 'tr_log2_zscore'] = tr_zscores
                print(f"  tr: 处理了 {len([x for x in tr_levels if x])} 个有效值")

            # 处理evi列
            if project_data['evi'].notna().sum() > 0:
                evi_levels, evi_zscores = calculate_quartile_groups_and_zscores(project_data, 'evi')
                df.loc[df['projectId'] == project_id, 'evi_Level'] = evi_levels
                df.loc[df['projectId'] == project_id, 'evi_log2_zscore'] = evi_zscores
                print(f"  evi: 处理了 {len([x for x in evi_levels if x])} 个有效值")
        
        # 创建最终输出DataFrame，包含所有需要的列
        output_df = df[['transcriptId', 'projectId', 'geneId', 'geneSymbol',
                       'te_Level', 'tr_Level', 'evi_Level',
                       'te_log2_zscore', 'tr_log2_zscore', 'evi_log2_zscore']].copy()

        # 列名已经是英文，无需重命名
        
        # 保存结果
        output_df.to_csv(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 显示结果统计
        print("\n结果统计:")
        for level_col in ['te_Level', 'tr_Level', 'evi_Level']:
            print(f"{level_col}:")
            value_counts = output_df[level_col].value_counts()
            for level, count in value_counts.items():
                if level:  # 只显示非空值
                    print(f"  {level}: {count}")
        
        print(f"\n处理完成！输出文件包含 {len(output_df)} 行数据")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始处理转录本数据...")
    try:
        process_gene_data()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()

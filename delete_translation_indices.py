#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除translationIndices表中的数据
根据compute_gene/deleted_rows_tr_evi_te_empty.csv文件中的ensembl_gene_id和project_id
删除utr_database数据库中translationIndices表的对应记录
"""

import pandas as pd
import pymysql
import logging
from typing import List, Tuple
import sys
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('delete_translation_indices.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'charset': 'utf8mb4'
}

def create_database_connection():
    """创建数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def read_csv_file(file_path: str) -> pd.DataFrame:
    """读取CSV文件"""
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        df = pd.read_csv(file_path)
        logger.info(f"成功读取CSV文件: {file_path}")
        logger.info(f"文件包含 {len(df)} 行数据")
        logger.info(f"列名: {list(df.columns)}")
        
        # 检查必要的列是否存在
        required_columns = ['ensembl_gene_id', 'project_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"CSV文件缺少必要的列: {missing_columns}")
        
        return df
    except Exception as e:
        logger.error(f"读取CSV文件失败: {e}")
        raise

def get_records_to_delete(connection, gene_project_pairs: List[Tuple[str, str]]) -> List[dict]:
    """查询需要删除的记录"""
    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建查询条件
        conditions = []
        params = []
        
        for gene_id, project_id in gene_project_pairs:
            conditions.append("(geneId = %s AND projectId = %s)")
            params.extend([gene_id, project_id])
        
        if not conditions:
            logger.warning("没有找到需要查询的记录")
            return []
        
        # 分批查询，避免SQL语句过长
        batch_size = 1000
        all_records = []
        
        for i in range(0, len(gene_project_pairs), batch_size):
            batch_pairs = gene_project_pairs[i:i + batch_size]
            batch_conditions = []
            batch_params = []
            
            for gene_id, project_id in batch_pairs:
                batch_conditions.append("(geneId = %s AND projectId = %s)")
                batch_params.extend([gene_id, project_id])
            
            query = f"""
            SELECT id, transcriptId, projectId, geneId, geneSymbol
            FROM translationIndices 
            WHERE {' OR '.join(batch_conditions)}
            """
            
            cursor.execute(query, batch_params)
            batch_records = cursor.fetchall()
            all_records.extend(batch_records)
            
            logger.info(f"批次 {i//batch_size + 1}: 查询到 {len(batch_records)} 条记录")
        
        cursor.close()
        logger.info(f"总共查询到 {len(all_records)} 条需要删除的记录")
        return all_records
        
    except Exception as e:
        logger.error(f"查询记录失败: {e}")
        raise

def delete_records(connection, records_to_delete: List[dict]) -> int:
    """删除记录"""
    if not records_to_delete:
        logger.info("没有需要删除的记录")
        return 0
    
    try:
        cursor = connection.cursor()
        
        # 提取所有需要删除的ID
        ids_to_delete = [record['id'] for record in records_to_delete]
        
        # 分批删除，避免SQL语句过长
        batch_size = 1000
        total_deleted = 0
        
        for i in range(0, len(ids_to_delete), batch_size):
            batch_ids = ids_to_delete[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_ids))
            
            delete_query = f"DELETE FROM translationIndices WHERE id IN ({placeholders})"
            
            cursor.execute(delete_query, batch_ids)
            deleted_count = cursor.rowcount
            total_deleted += deleted_count
            
            logger.info(f"批次 {i//batch_size + 1}: 删除了 {deleted_count} 条记录")
        
        # 提交事务
        connection.commit()
        cursor.close()
        
        logger.info(f"总共删除了 {total_deleted} 条记录")
        return total_deleted
        
    except Exception as e:
        logger.error(f"删除记录失败: {e}")
        connection.rollback()
        raise

def backup_records_to_delete(records_to_delete: List[dict], backup_file: str):
    """备份将要删除的记录"""
    try:
        df_backup = pd.DataFrame(records_to_delete)
        df_backup.to_csv(backup_file, index=False, encoding='utf-8')
        logger.info(f"已备份 {len(records_to_delete)} 条记录到文件: {backup_file}")
    except Exception as e:
        logger.error(f"备份记录失败: {e}")
        raise

def get_table_stats(connection) -> dict:
    """获取表的统计信息"""
    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取总记录数
        cursor.execute("SELECT COUNT(*) as total_count FROM translationIndices")
        total_count = cursor.fetchone()['total_count']

        # 获取唯一基因数
        cursor.execute("SELECT COUNT(DISTINCT geneId) as unique_genes FROM translationIndices")
        unique_genes = cursor.fetchone()['unique_genes']

        # 获取唯一项目数
        cursor.execute("SELECT COUNT(DISTINCT projectId) as unique_projects FROM translationIndices")
        unique_projects = cursor.fetchone()['unique_projects']

        cursor.close()

        return {
            'total_count': total_count,
            'unique_genes': unique_genes,
            'unique_projects': unique_projects
        }
    except Exception as e:
        logger.error(f"获取表统计信息失败: {e}")
        raise

def main():
    """主函数"""
    csv_file_path = "compute_gene/deleted_rows_tr_evi_te_empty.csv"
    backup_file = f"backup_deleted_records_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"

    try:
        # 读取CSV文件
        logger.info("开始处理删除任务...")
        df = read_csv_file(csv_file_path)

        # 去重处理
        original_count = len(df)
        df = df.drop_duplicates(subset=['ensembl_gene_id', 'project_id'])
        deduplicated_count = len(df)

        if original_count != deduplicated_count:
            logger.info(f"去重处理: 原始 {original_count} 行，去重后 {deduplicated_count} 行")

        # 准备基因ID和项目ID对
        gene_project_pairs = list(zip(df['ensembl_gene_id'], df['project_id']))
        logger.info(f"准备删除 {len(gene_project_pairs)} 个基因-项目组合的记录")

        # 创建数据库连接
        connection = create_database_connection()

        try:
            # 获取删除前的表统计信息
            logger.info("获取删除前的表统计信息...")
            stats_before = get_table_stats(connection)
            logger.info(f"删除前统计: 总记录数={stats_before['total_count']}, "
                       f"唯一基因数={stats_before['unique_genes']}, "
                       f"唯一项目数={stats_before['unique_projects']}")

            # 查询需要删除的记录
            logger.info("查询需要删除的记录...")
            records_to_delete = get_records_to_delete(connection, gene_project_pairs)

            if not records_to_delete:
                logger.info("没有找到匹配的记录需要删除")
                return

            # 备份将要删除的记录
            logger.info("备份将要删除的记录...")
            backup_records_to_delete(records_to_delete, backup_file)

            # 显示一些将要删除的记录信息
            logger.info("将要删除的记录示例:")
            for i, record in enumerate(records_to_delete[:5]):  # 显示前5条
                logger.info(f"  {i+1}. ID: {record['id']}, geneId: {record['geneId']}, "
                          f"projectId: {record['projectId']}, geneSymbol: {record['geneSymbol']}")

            if len(records_to_delete) > 5:
                logger.info(f"  ... 还有 {len(records_to_delete) - 5} 条记录")

            # 计算删除比例
            deletion_percentage = (len(records_to_delete) / stats_before['total_count']) * 100
            logger.info(f"将删除 {len(records_to_delete)} 条记录，占总记录数的 {deletion_percentage:.2f}%")

            # 安全检查：如果删除比例过高，需要额外确认
            if deletion_percentage > 10:  # 如果删除超过10%的数据
                logger.warning(f"⚠️  警告：将删除 {deletion_percentage:.2f}% 的数据，这是一个较大的删除操作")
                extra_confirm = input("这是一个大规模删除操作，请再次确认 (输入 'DELETE' 继续): ")
                if extra_confirm != 'DELETE':
                    logger.info("用户取消删除操作")
                    return

            # 确认删除
            confirm = input(f"\n确认删除 {len(records_to_delete)} 条记录吗? (y/N): ")
            if confirm.lower() != 'y':
                logger.info("用户取消删除操作")
                return

            # 执行删除
            logger.info("开始删除记录...")
            deleted_count = delete_records(connection, records_to_delete)

            # 获取删除后的表统计信息
            stats_after = get_table_stats(connection)
            logger.info(f"删除后统计: 总记录数={stats_after['total_count']}, "
                       f"唯一基因数={stats_after['unique_genes']}, "
                       f"唯一项目数={stats_after['unique_projects']}")

            logger.info(f"删除操作完成! 总共删除了 {deleted_count} 条记录")
            logger.info(f"备份文件: {backup_file}")

        finally:
            connection.close()
            logger.info("数据库连接已关闭")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
import pandas as pd
import os

def merge_kegg_with_gene_info():
    # 文件路径
    kegg_file = "process_gene/KEGG_final.csv"
    gene_info_file = "process_gene/unique_genes_with_project_info_updated.csv"
    output_file = "process_gene/KEGG_final_with_info.csv"
    
    # 读取KEGG文件
    print("读取KEGG_final.csv...")
    kegg_df = pd.read_csv(kegg_file)
    print(f"KEGG文件包含 {len(kegg_df)} 行数据")
    
    # 读取基因信息文件
    print("读取unique_genes_with_project_info_updated.csv...")
    gene_info_df = pd.read_csv(gene_info_file)
    print(f"基因信息文件包含 {len(gene_info_df)} 行数据")
    
    # 合并数据：根据Gene_ID匹配ensembl_gene_id
    print("合并数据...")
    merged_df = kegg_df.merge(
        gene_info_df[['ensembl_gene_id', 'Tissue/Cell Type', 'Cell line', 'Disease']], 
        left_on='Gene_ID', 
        right_on='ensembl_gene_id', 
        how='left'
    )
    
    # 删除重复的ensembl_gene_id列
    merged_df = merged_df.drop('ensembl_gene_id', axis=1)
    
    # 重新排列列的顺序
    columns_order = ['Gene symbol', 'Gene_ID', 'Pathway_Description', 'Pathway_ID', 
                     'Tissue/Cell Type', 'Cell line', 'Disease']
    merged_df = merged_df[columns_order]
    
    # 保存结果
    print(f"保存结果到 {output_file}...")
    merged_df.to_csv(output_file, index=False)
    
    # 显示统计信息
    print(f"合并完成！输出文件包含 {len(merged_df)} 行数据")
    
    # 检查匹配情况
    matched_count = merged_df['Tissue/Cell Type'].notna().sum()
    unmatched_count = len(merged_df) - matched_count
    
    print(f"成功匹配的基因数量: {matched_count}")
    print(f"未匹配的基因数量: {unmatched_count}")
    
    if unmatched_count > 0:
        print("\n未匹配的基因ID样例：")
        unmatched_genes = merged_df[merged_df['Tissue/Cell Type'].isna()]['Gene_ID'].head(5)
        for gene_id in unmatched_genes:
            print(f"  - {gene_id}")
    
    print(f"\n输出文件已保存: {output_file}")
    
    # 显示前几行结果
    print("\n前5行结果预览：")
    print(merged_df.head())

if __name__ == "__main__":
    merge_kegg_with_gene_info()
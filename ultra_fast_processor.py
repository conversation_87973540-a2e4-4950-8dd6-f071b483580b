#!/usr/bin/env python3
"""
超高性能版本 - 使用内存映射和预加载策略
适合大数据量处理
"""

import pandas as pd
import pymysql
import json
import logging
import time
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'port': 3306,
    'database': 'utr_database',
    'charset': 'utf8mb4'
}

def preload_all_data():
    """预加载所有转录本数据到内存"""
    logger.info("🚀 预加载所有转录本数据到内存...")
    start_time = time.time()
    
    connection = pymysql.connect(**DB_CONFIG)
    
    try:
        with connection.cursor() as cursor:
            # 一次性加载所有数据，使用GROUP_CONCAT减少数据传输
            sql = """
            SELECT transcriptId, 
                   GROUP_CONCAT(DISTINCT ensemblGeneId ORDER BY ensemblGeneId SEPARATOR '|') as ensembl_ids,
                   GROUP_CONCAT(DISTINCT externalGeneName ORDER BY externalGeneName SEPARATOR '|') as external_names
            FROM tpmData 
            GROUP BY transcriptId
            """
            
            logger.info("执行数据库查询...")
            cursor.execute(sql)
            
            logger.info("获取查询结果...")
            results = cursor.fetchall()
            
            logger.info("处理查询结果...")
            transcript_data = {}
            
            for transcript_id, ensembl_ids_str, external_names_str in results:
                ensembl_ids = []
                external_names = []
                
                if ensembl_ids_str:
                    ensembl_ids = [id.strip() for id in ensembl_ids_str.split('|') if id.strip()]
                
                if external_names_str:
                    external_names = [name.strip() for name in external_names_str.split('|') if name.strip()]
                
                transcript_data[transcript_id] = (ensembl_ids, external_names)
            
            load_time = time.time() - start_time
            logger.info(f"✅ 预加载完成! 用时: {load_time:.2f}秒")
            logger.info(f"📊 加载了 {len(transcript_data):,} 个转录本的数据")
            
            return transcript_data
            
    except Exception as e:
        logger.error(f"预加载数据时出错: {e}")
        return {}
    finally:
        connection.close()

def process_csv_ultra_fast(input_file: str, output_file: str):
    """超快速处理CSV文件"""
    total_start_time = time.time()
    
    try:
        # 预加载所有数据
        transcript_lookup = preload_all_data()
        
        if not transcript_lookup:
            logger.error("无法加载转录本数据，程序退出")
            return
        
        # 读取CSV文件
        logger.info(f"📖 读取CSV文件: {input_file}")
        read_start = time.time()
        df = pd.read_csv(input_file)
        read_time = time.time() - read_start
        logger.info(f"✅ CSV读取完成，用时: {read_time:.2f}秒")
        
        if 'Transcript' not in df.columns:
            raise ValueError("CSV文件中缺少 'Transcript' 列")
        
        logger.info(f"📊 CSV文件包含 {len(df):,} 行数据")
        
        # 统计唯一转录本
        unique_transcripts = df['Transcript'].dropna().nunique()
        logger.info(f"📊 发现 {unique_transcripts:,} 个唯一的转录本ID")
        
        # 快速数据填充
        logger.info("⚡ 开始快速数据填充...")
        fill_start = time.time()
        
        def fast_lookup(transcript_id):
            """快速查找函数"""
            if pd.isna(transcript_id) or transcript_id not in transcript_lookup:
                return '[]', '[]'
            else:
                ensembl_ids, external_names = transcript_lookup[transcript_id]
                return json.dumps(ensembl_ids), json.dumps(external_names)
        
        # 使用向量化操作
        result_data = df['Transcript'].apply(fast_lookup)
        df['ensembl_gene_id'] = [data[0] for data in result_data]
        df['external_gene_name'] = [data[1] for data in result_data]
        
        fill_time = time.time() - fill_start
        logger.info(f"✅ 数据填充完成，用时: {fill_time:.2f}秒")
        
        # 保存结果
        logger.info(f"💾 保存结果到: {output_file}")
        save_start = time.time()
        df.to_csv(output_file, index=False)
        save_time = time.time() - save_start
        logger.info(f"✅ 文件保存完成，用时: {save_time:.2f}秒")
        
        # 统计信息
        non_empty_ensembl = (df['ensembl_gene_id'] != '[]').sum()
        non_empty_external = (df['external_gene_name'] != '[]').sum()
        
        total_time = time.time() - total_start_time
        
        # 输出详细统计
        logger.info("=" * 60)
        logger.info("🎉 处理完成!")
        logger.info("=" * 60)
        logger.info(f"⏱️  总处理时间: {total_time:.2f} 秒")
        logger.info(f"🚀 平均处理速度: {len(df)/total_time:.0f} 行/秒")
        logger.info(f"📊 总行数: {len(df):,}")
        logger.info(f"🎯 找到ensembl_gene_id的行数: {non_empty_ensembl:,} ({non_empty_ensembl/len(df)*100:.1f}%)")
        logger.info(f"🎯 找到external_gene_name的行数: {non_empty_external:,} ({non_empty_external/len(df)*100:.1f}%)")
        logger.info("=" * 60)
        
        # 时间分解
        logger.info("⏱️  时间分解:")
        logger.info(f"   数据预加载: {load_time:.2f}秒 ({load_time/total_time*100:.1f}%)")
        logger.info(f"   CSV读取: {read_time:.2f}秒 ({read_time/total_time*100:.1f}%)")
        logger.info(f"   数据填充: {fill_time:.2f}秒 ({fill_time/total_time*100:.1f}%)")
        logger.info(f"   文件保存: {save_time:.2f}秒 ({save_time/total_time*100:.1f}%)")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        raise

def main():
    """主函数"""
    input_file = 'Transcript_id.csv'
    output_file = 'Transcript_id_processed_ultra_fast.csv'
    
    try:
        process_csv_ultra_fast(input_file, output_file)
        
    except FileNotFoundError:
        logger.error(f"找不到输入文件: {input_file}")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")

if __name__ == "__main__":
    print("⚡ 超高性能转录本数据处理器")
    print("🚀 使用内存预加载策略，适合大数据量处理")
    print("=" * 60)
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理KEGG_pathway_results文件夹下的所有xlsx文件 - 快速版本
根据projectId查询project_unique_info.csv文件，添加Condition和Disease Category列
使用向量化操作提高处理速度
"""

import pandas as pd
import os
from pathlib import Path
import time

def load_project_info():
    """
    加载项目信息CSV文件，创建projectId到Condition和Disease Category的映射
    """
    project_info_file = "/Volumes/zhy/整合的所有TPM文件/compute_gene/project_unique_info.csv"
    
    try:
        # 读取项目信息文件
        df = pd.read_csv(project_info_file)
        print(f"成功读取项目信息文件，共 {len(df)} 行")
        
        # 创建映射字典，使用向量化操作
        project_mapping = pd.Series(
            df[['Condition', 'Disease Category']].to_dict('records'),
            index=df['Project ID']
        ).to_dict()
        
        print(f"创建了 {len(project_mapping)} 个项目的映射信息")
        return project_mapping
        
    except Exception as e:
        print(f"❌ 读取项目信息文件失败: {e}")
        return None

def process_single_xlsx_file(file_path, project_mapping, output_dir):
    """
    处理单个xlsx文件，添加Condition和Disease Category列 - 优化版本
    """
    try:
        # 读取xlsx文件
        df = pd.read_excel(file_path, engine='openpyxl')
        
        if df.empty:
            print(f"  文件为空，跳过")
            return False
        
        # 检查是否有projectId列
        if 'projectId' not in df.columns:
            print(f"  文件中没有找到projectId列，跳过")
            return False
        
        print(f"  原始数据: {len(df)} 行")
        
        # 使用向量化操作添加新列
        df['projectId_str'] = df['projectId'].astype(str).str.strip()
        
        # 创建临时映射DataFrame
        mapping_df = pd.DataFrame([
            {'projectId': k, 'Condition': v['Condition'], 'Disease Category': v['Disease Category']}
            for k, v in project_mapping.items()
        ])
        
        # 使用merge进行快速匹配
        df_merged = df.merge(
            mapping_df, 
            left_on='projectId_str', 
            right_on='projectId', 
            how='left',
            suffixes=('', '_mapping')
        )
        
        # 填充未匹配的值
        df_merged['Condition'] = df_merged['Condition'].fillna('Unknown')
        df_merged['Disease Category'] = df_merged['Disease Category'].fillna('Unknown')
        
        # 删除临时列
        df_merged = df_merged.drop(['projectId_str', 'projectId_mapping'], axis=1, errors='ignore')
        
        # 统计匹配情况
        matched_count = (df_merged['Condition'] != 'Unknown').sum()
        unmatched_count = len(df_merged) - matched_count
        
        # 保存到输出目录
        filename = os.path.basename(file_path)
        output_path = os.path.join(output_dir, filename)
        df_merged.to_excel(output_path, index=False, engine='openpyxl')
        
        print(f"  处理完成: 匹配 {matched_count}, 未匹配 {unmatched_count}")
        print(f"  已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 处理文件失败: {e}")
        return False

def process_kegg_pathway_files_batch():
    """
    批量处理KEGG_pathway_results文件夹下的xlsx文件 - 优化版本
    """
    # 文件路径配置
    input_dir = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_pathway_results"
    output_dir = "/Volumes/zhy/整合的所有TPM文件/process_transcript/KEGG_pathway_results_with_info"
    
    start_time = time.time()
    print("=== 处理KEGG通路文件，添加项目信息 (快速版) ===")
    
    # 1. 加载项目信息映射
    print("正在加载项目信息...")
    project_mapping = load_project_info()
    
    if project_mapping is None:
        print("❌ 无法加载项目信息，退出处理")
        return False
    
    # 2. 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    print(f"输出目录已创建: {output_dir}")
    
    # 3. 获取所有xlsx文件，排除summary文件
    input_path = Path(input_dir)
    xlsx_files = [f for f in input_path.glob("*.xlsx") if "summary" not in f.name.lower()]
    
    if not xlsx_files:
        print("❌ 未找到任何xlsx文件")
        return False
    
    print(f"找到 {len(xlsx_files)} 个xlsx文件")
    
    # 4. 分批处理文件 (每次处理10个)
    batch_size = 10
    successful_count = 0
    failed_count = 0
    
    for i in range(0, len(xlsx_files), batch_size):
        batch_files = xlsx_files[i:i + batch_size]
        print(f"\n处理批次 {i//batch_size + 1}/{(len(xlsx_files) + batch_size - 1)//batch_size}")
        
        for j, file_path in enumerate(batch_files):
            filename = file_path.name
            file_index = i + j + 1
            print(f"\n处理文件 ({file_index}/{len(xlsx_files)}): {filename}")
            
            if process_single_xlsx_file(file_path, project_mapping, output_dir):
                successful_count += 1
            else:
                failed_count += 1
        
        # 每批次后显示进度
        progress = (i + batch_size) / len(xlsx_files) * 100
        print(f"\n进度: {min(progress, 100):.1f}%")
    
    # 5. 输出处理结果统计
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== 处理完成 ===")
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"总文件数: {len(xlsx_files)}")
    print(f"成功处理: {successful_count}")
    print(f"处理失败: {failed_count}")
    print(f"成功率: {successful_count/len(xlsx_files)*100:.2f}%")
    
    if successful_count > 0:
        print(f"\n✅ 处理完成的文件已保存到: {output_dir}")
    
    return successful_count > 0

def main():
    """
    主函数
    """
    process_kegg_pathway_files_batch()

if __name__ == "__main__":
    main()
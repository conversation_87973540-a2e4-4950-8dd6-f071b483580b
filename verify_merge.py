#!/usr/bin/env python3
import pandas as pd

print("验证合并结果...")

try:
    # 读取合并后的文件
    df = pd.read_csv('process_gene/ensembl_gene_id_with_full_info_2.csv')
    
    print(f"文件信息:")
    print(f"  行数: {len(df)}")
    print(f"  列数: {len(df.columns)}")
    print(f"  列名: {list(df.columns)}")
    
    # 检查新添加的列
    if 'transcript_count' in df.columns:
        print(f"\n✅ transcript_count 列已添加")
        print(f"  数据类型: {df['transcript_count'].dtype}")
        print(f"  空值数: {df['transcript_count'].isnull().sum()}")
        print(f"  统计: min={df['transcript_count'].min()}, max={df['transcript_count'].max()}, mean={df['transcript_count'].mean():.2f}")
    else:
        print(f"❌ transcript_count 列缺失")
    
    if 'transcripts' in df.columns:
        print(f"\n✅ transcripts 列已添加")
        print(f"  数据类型: {df['transcripts'].dtype}")
        print(f"  空值数: {df['transcripts'].isnull().sum()}")
    else:
        print(f"❌ transcripts 列缺失")
    
    # 显示前几行的关键信息
    print(f"\n前5行数据:")
    display_cols = ['ensembl_gene_id', 'external_gene_name', 'transcript_count']
    if 'transcript_count' in df.columns:
        print(df[display_cols].head().to_string(index=False))
    
    # 检查转录本数量分布
    if 'transcript_count' in df.columns:
        print(f"\n转录本数量分布:")
        count_dist = df['transcript_count'].value_counts().head(10)
        for count, freq in count_dist.items():
            print(f"  {count} 个转录本: {freq} 个基因")
    
    print(f"\n✅ 验证完成!")
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()

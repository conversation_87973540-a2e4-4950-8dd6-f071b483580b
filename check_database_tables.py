#!/usr/bin/env python3
"""
检查数据库中存在的表和可能的表名
"""

try:
    import pymysql
except ImportError:
    print("❌ pymysql 未安装，请运行: pip install pymysql")
    exit(1)

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'charset': 'utf8mb4'
}

def check_database():
    """检查数据库中的表"""
    connection = None
    
    try:
        print("🔍 连接数据库并检查表...")
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功!")
        
        with connection.cursor() as cursor:
            # 显示所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"\n📋 数据库 'utr_database' 中的所有表 ({len(tables)} 个):")
            for table in tables:
                print(f"  - {table[0]}")
            
            # 查找包含 'tpm' 的表
            tpm_tables = [table[0] for table in tables if 'tpm' in table[0].lower()]
            if tpm_tables:
                print(f"\n🎯 包含 'tpm' 的表:")
                for table in tpm_tables:
                    print(f"  - {table}")
                    
                    # 显示表结构
                    cursor.execute(f"DESCRIBE {table}")
                    columns = cursor.fetchall()
                    print(f"    列: {', '.join([col[0] for col in columns])}")
                    
                    # 显示记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"    记录数: {count:,}")
                    print()
            
            # 查找包含转录本相关字段的表
            print("🔍 查找包含转录本相关字段的表...")
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    column_names = [col[0].lower() for col in columns]
                    
                    # 检查是否包含转录本相关字段
                    transcript_fields = ['transcriptid', 'transcript_id', 'transcript']
                    gene_fields = ['geneid', 'gene_id', 'ensemblgeneid', 'ensembl_gene_id']
                    
                    has_transcript = any(field in column_names for field in transcript_fields)
                    has_gene = any(field in column_names for field in gene_fields)
                    
                    if has_transcript or has_gene:
                        print(f"\n📊 表 '{table_name}' 可能包含相关数据:")
                        print(f"    所有列: {', '.join([col[0] for col in columns])}")
                        
                        # 显示几行示例数据
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        sample_data = cursor.fetchall()
                        if sample_data:
                            print("    示例数据:")
                            for i, row in enumerate(sample_data):
                                print(f"      行{i+1}: {row}")
                        
                except Exception as e:
                    print(f"    ⚠️ 无法检查表 {table_name}: {e}")
                    
    except pymysql.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False
    finally:
        if connection:
            connection.close()
            print("\n🔒 数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("🔍 数据库表检查工具")
    print("=" * 50)
    check_database()

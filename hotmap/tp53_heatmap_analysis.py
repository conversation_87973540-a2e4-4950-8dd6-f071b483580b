import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import zscore
import warnings
warnings.filterwarnings('ignore')

# 疾病大类与condition的映射关系
DISEASE_CATEGORIES = {
    "Infectious Disease": [
        "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
        "Adult Hepatocellular Carcinoma; HCV Transfection",
        "B95-8 Epstein-Barr Virus Infection",
        "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
        "Human Cytomegalovirus Infection",
        "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
        "IAV Transfection",
        "Lung Adenocarcinoma; IAV Infection",
        "Lung Adenocarcinoma; SARS-CoV-2 Infection",
        "M81 Epstein-Barr Virus Infection",
        "Toxoplasma Infection"
    ],
    "Gastrointestinal System Cancer": [
        "Adult Hepatocellular Carcinoma",
        "Childhood Hepatocellular Carcinoma",
        "Colon Adenocarcinoma",
        "Colon Carcinoma",
        "Esophageal Squamous Cell Carcinoma",
        "Hepatoblastoma",
        "Hepatocellular Carcinoma",
        "Intrahepatic Cholangiocarcinoma"
    ],
    "Musculoskeletal System Cancer": [
        "Osteosarcoma"
    ],
    "Reproductive Organ Cancer": [
        "Human Papillomavirus-related Endocervical Adenocarcinoma",
        "Ovarian Cancer",
        "Ovarian Endometrioid Carcinoma",
        "Prostate Cancer",
        "Prostate Carcinoma"
    ],
    "Respiratory System Cancer": [
        "Lung Adenocarcinoma",
        "Lung Large Cell Carcinoma"
    ],
    "Urinary System Cancer": [
        "Kidney Rhabdoid Cancer",
        "Kidney Tumor",
        "Renal Cell Carcinoma"
    ],
    "Genetic Disease": [
        "Duchenne Muscular Dystrophy",
        "Hbs1L Deficiency",
        "Roberts Syndrome",
        "Treacher Collins Syndrome",
        "Tuberous Sclerosis Complex"
    ],
    "Other": [
        "Cancer-derived"
    ],
    "Nervous System Cancer": [
        "Astrocytoma",
        "Brain Glioma",
        "Neuroblastoma"
    ],
    "Hematologic Cancer": [
        "Acute Myeloid Leukemia",
        "Adult Acute Myeloid Leukemia",
        "Childhood T Acute Lymphoblastic Leukemia",
        "Childhood T Lymphoblastic Lymphoma",
        "Chronic Myeloid Leukemia",
        "Multiple Myeloma"
    ],
    "Breast Cancer": [
        "Breast Adenocarcinoma",
        "Breast Carcinoma"
    ],
    "Head And Neck Cancer": [
        "Head And Neck Squamous Cell Carcinoma",
        "Tongue Squamous Cell Carcinoma"
    ],
    "Endocrine Gland Cancer": [
        "Pancreatic Adenocarcinoma"
    ]
}

def map_condition_to_disease_category(condition):
    """将condition映射到疾病大类"""
    if 'Normal' in condition:
        return 'Normal'
    
    for category, conditions in DISEASE_CATEGORIES.items():
        if condition in conditions:
            return category
    
    return 'Unknown'

def main():
    # 读取数据
    print("正在读取数据...")
    df = pd.read_csv('gene_translation_data_TP53.csv')
    
    # 确保TE列是数值类型
    df['TE'] = pd.to_numeric(df['TE'], errors='coerce')
    df = df.dropna(subset=['TE'])
    
    # 创建CONDITION_DATASET_ID
    df['CONDITION_DATASET_ID'] = df['CONDITION'] + '_' + df['DATASET ID']
    
    # 映射疾病大类
    df['DISEASE_CATEGORY'] = df['CONDITION'].apply(map_condition_to_disease_category)
    
    print(f"数据形状: {df.shape}")
    print(f"疾病类别分布:")
    print(df['DISEASE_CATEGORY'].value_counts())
    
    # 为每个CONDITION计算和，选出最大的TRANSCRIPT ID
    condition_max_transcript = {}
    for condition in df['CONDITION'].unique():
        condition_data = df[df['CONDITION'] == condition]
        transcript_sums = condition_data.groupby('TRANSCRIPT ID')['TE'].sum()
        max_transcript = transcript_sums.idxmax()
        condition_max_transcript[condition] = max_transcript
    
    # 为每个疾病大类计算和，选出最大的TRANSCRIPT ID
    category_max_transcript = {}
    for category in df['DISEASE_CATEGORY'].unique():
        category_data = df[df['DISEASE_CATEGORY'] == category]
        transcript_sums = category_data.groupby('TRANSCRIPT ID')['TE'].sum()
        max_transcript = transcript_sums.idxmax()
        category_max_transcript[category] = max_transcript
    
    # 创建透视表用于热图
    heatmap_data = df.pivot_table(
        index='TRANSCRIPT ID',
        columns='CONDITION_DATASET_ID',
        values='TE',
        aggfunc='mean'
    )
    
    # 移除包含NaN的行和列
    heatmap_data = heatmap_data.dropna(axis=0, how='all').dropna(axis=1, how='all')
    
    # 对每列进行z-score标准化
    heatmap_data_zscore = heatmap_data.apply(zscore, axis=0, nan_policy='omit')
    
    # 将列按疾病大类分组排序
    condition_category_map = {}
    for idx, row in df[['CONDITION_DATASET_ID', 'DISEASE_CATEGORY']].drop_duplicates().iterrows():
        condition_category_map[row['CONDITION_DATASET_ID']] = row['DISEASE_CATEGORY']
    
    # 按疾病大类排序列
    category_order = ['Normal'] + [cat for cat in DISEASE_CATEGORIES.keys() if cat in df['DISEASE_CATEGORY'].unique()]
    
    sorted_columns = []
    for category in category_order:
        category_columns = [col for col in heatmap_data_zscore.columns 
                          if condition_category_map.get(col) == category]
        sorted_columns.extend(sorted(category_columns))
    
    # 添加未分类的列
    remaining_columns = [col for col in heatmap_data_zscore.columns if col not in sorted_columns]
    sorted_columns.extend(sorted(remaining_columns))
    
    heatmap_data_zscore = heatmap_data_zscore[sorted_columns]
    
    # 绘制热图
    plt.figure(figsize=(20, 12))
    
    # 创建颜色分隔线位置
    category_positions = []
    current_pos = 0
    for category in category_order:
        category_columns = [col for col in sorted_columns 
                          if condition_category_map.get(col) == category]
        if category_columns:
            current_pos += len(category_columns)
            category_positions.append(current_pos)
    
    # 绘制热图
    sns.heatmap(heatmap_data_zscore, 
                cmap='RdBu_r', 
                center=0,
                cbar_kws={'label': 'Z-score'},
                xticklabels=True,
                yticklabels=True)
    
    # 添加疾病大类分隔线
    ax = plt.gca()
    for pos in category_positions[:-1]:  # 不包括最后一个位置
        ax.axvline(x=pos, color='black', linewidth=2)
    
    plt.title('TP53 Gene Expression Heatmap by Disease Categories', fontsize=16, pad=20)
    plt.xlabel('Condition_Dataset_ID', fontsize=12)
    plt.ylabel('Transcript ID', fontsize=12)
    plt.xticks(rotation=90, fontsize=8)
    plt.yticks(fontsize=8)
    plt.tight_layout()
    
    # 保存热图
    plt.savefig('tp53_heatmap.png', dpi=300, bbox_inches='tight')
    plt.savefig('tp53_heatmap.pdf', bbox_inches='tight')
    print("热图已保存为 tp53_heatmap.png 和 tp53_heatmap.pdf")
    
    # 创建结果DataFrame
    results = []
    
    for condition in df['CONDITION'].unique():
        gene_id = df[df['CONDITION'] == condition]['GENE ID'].iloc[0]
        gene_symbol = df[df['CONDITION'] == condition]['GENE SYMBOL'].iloc[0]
        disease_category = df[df['CONDITION'] == condition]['DISEASE_CATEGORY'].iloc[0]
        condition_max_trans = condition_max_transcript[condition]
        category_max_trans = category_max_transcript[disease_category]
        
        # 计算该condition下该transcript的表达值总和
        condition_max_expr = df[(df['CONDITION'] == condition) & 
                               (df['TRANSCRIPT ID'] == condition_max_trans)]['TE'].sum()
        
        # 计算该disease category下该transcript的表达值总和
        category_max_expr = df[(df['DISEASE_CATEGORY'] == disease_category) & 
                              (df['TRANSCRIPT ID'] == category_max_trans)]['TE'].sum()
        
        results.append({
            'GENE_ID': gene_id,
            'GENE_SYMBOL': gene_symbol,
            'CONDITION': condition,
            'CONDITION_MAX_TRANSCRIPT_ID': condition_max_trans,
            'CONDITION_MAX_EXPRESSION': condition_max_expr,
            'DISEASE_CATEGORY': disease_category,
            'CATEGORY_MAX_TRANSCRIPT_ID': category_max_trans,
            'CATEGORY_MAX_EXPRESSION': category_max_expr,
            'CONDITION_DATASET_COUNT': len(df[df['CONDITION'] == condition]['DATASET ID'].unique()),
            'TOTAL_TRANSCRIPTS_IN_CONDITION': len(df[df['CONDITION'] == condition]['TRANSCRIPT ID'].unique())
        })
    
    result_df = pd.DataFrame(results)
    result_df = result_df.drop_duplicates().sort_values(['DISEASE_CATEGORY', 'CONDITION'])
    
    # 保存结果CSV
    result_df.to_csv('tp53_analysis_results.csv', index=False, encoding='utf-8')
    
    print(f"\n分析完成！")
    print(f"结果已保存到 'tp53_analysis_results.csv'")
    print(f"共处理了 {len(result_df)} 个不同的conditions")
    print(f"涉及 {len(result_df['DISEASE_CATEGORY'].unique())} 个疾病大类")
    
    # 显示结果预览
    print("\n结果预览:")
    print(result_df.head(10).to_string(index=False))
    
    # 显示每个疾病大类的统计信息
    print("\n各疾病大类统计:")
    category_stats = result_df.groupby('DISEASE_CATEGORY').agg({
        'CONDITION': 'count',
        'CONDITION_MAX_EXPRESSION': 'mean',
        'CATEGORY_MAX_EXPRESSION': 'mean'
    }).round(3)
    category_stats.columns = ['Condition_Count', 'Avg_Cond
    ition_Max_Expr', 'Avg_Category_Max_Expr']
    print(category_stats)
    
    # plt.show()  # 注释掉以避免阻塞

if __name__ == "__main__":
    main()
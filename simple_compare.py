#!/usr/bin/env python3
import pandas as pd

print("开始比较基因ID...")

# 读取文件
print("读取文件1: unique_genes.csv")
df1 = pd.read_csv('process_gene/unique_genes.csv')
print(f"文件1行数: {len(df1)}")

print("读取文件2: ensembl_gene_id_with_full_info_split_2_processed.csv")
df2 = pd.read_csv('process_gene/ensembl_gene_id_with_full_info_split_2_processed.csv')
print(f"文件2行数: {len(df2)}")

# 提取基因ID
genes1 = set(df1['ensembl_gene_id'])
genes2 = set(df2['GENE ID'])

print(f"文件1中唯一基因ID数: {len(genes1)}")
print(f"文件2中唯一基因ID数: {len(genes2)}")

# 找出差异
missing = genes1 - genes2
extra = genes2 - genes1
common = genes1 & genes2

print(f"共同基因数: {len(common)}")
print(f"文件1中但不在文件2中的基因数: {len(missing)}")
print(f"文件2中但不在文件1中的基因数: {len(extra)}")

if missing:
    print(f"\n前10个缺失的基因ID:")
    missing_list = sorted(list(missing))
    for i, gene_id in enumerate(missing_list[:10]):
        gene_name = df1[df1['ensembl_gene_id'] == gene_id]['external_gene_name'].iloc[0]
        print(f"  {i+1}. {gene_id} - {gene_name}")
    
    # 保存缺失的基因
    missing_df = df1[df1['ensembl_gene_id'].isin(missing)]
    missing_df.to_csv('process_gene/missing_genes.csv', index=False)
    print(f"\n已保存 {len(missing)} 个缺失基因到 process_gene/missing_genes.csv")

print("比较完成!")

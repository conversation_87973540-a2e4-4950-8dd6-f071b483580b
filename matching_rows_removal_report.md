# 匹配行删除处理报告

## 任务描述

1. **获取组合数据：** 从 `compute_gene/deleted_rows_tr_evi_te_empty.csv` 文件中获取 `ensembl_gene_id` 和 `project_id` 的组合
2. **删除匹配行：** 在 `compute_transcript/translation_indices_results_grouped_with_disease_category.csv` 文件中删除具有相同 `ensembl_gene_id` 和 `project_id` 组合的行

## 处理结果

### ✅ 任务成功完成

### 📊 数据统计

**输入数据：**
- **基因删除行文件：** `compute_gene/deleted_rows_tr_evi_te_empty.csv`
  - 总行数：101,999 行
  - 唯一基因-项目组合：101,999 个

- **原始transcript文件：** `compute_transcript/translation_indices_results_grouped_with_disease_category.csv`
  - 原始行数：12,935,373 行
  - 文件大小：1.72 GB

**处理结果：**
- **匹配并删除的行数：** 282,554 行
- **删除比例：** 2.18%
- **剩余行数：** 12,652,819 行

### 📁 输出文件

1. **清理后的主文件：** `compute_transcript/translation_indices_results_grouped_with_disease_category_cleaned.csv`
   - 行数：12,652,819 行
   - 文件大小：1.67 GB
   - 包含所有原始列，但删除了匹配的行

2. **被删除的行备份：** `compute_transcript/deleted_matching_rows.csv`
   - 行数：282,554 行
   - 文件大小：38.2 MB
   - 包含所有被删除的行的完整数据

### 🔍 数据示例

**被删除的行示例：**
```csv
transcript_id,project_id,bioproject_id,Tissue/Cell Type,Cell line,Condition,TR,EVI,TE,ensembl_gene_id,external_gene_name,Disease Category
ENST00000602384,TEDD00024,PRJNA244941,,U-2 OS,Osteosarcoma,,,12.130021855576052,ENSG00000270136,MICOS10-NBL1,Musculoskeletal System Cancer
ENST00000602293,TEDD00024,PRJNA244941,,U-2 OS,Osteosarcoma,,,4.975466948808027,ENSG00000270136,MICOS10-NBL1,Musculoskeletal System Cancer
ENST00000602450,TEDD00024,PRJNA244941,,U-2 OS,Osteosarcoma,,,12.64770299162061,ENSG00000270136,MICOS10-NBL1,Musculoskeletal System Cancer
```

### 🔧 处理逻辑

1. **读取基因删除行：** 从 `deleted_rows_tr_evi_te_empty.csv` 中提取所有 `ensembl_gene_id` 和 `project_id` 组合
2. **创建匹配键：** 将 `ensembl_gene_id` 和 `project_id` 组合成唯一标识符
3. **查找匹配行：** 在transcript文件中找到具有相同组合的行
4. **删除匹配行：** 从原始数据中移除所有匹配的行
5. **保存结果：** 生成清理后的文件和被删除行的备份

### ✅ 质量保证

- ✅ 数据完整性：所有非匹配行都被保留
- ✅ 备份安全：被删除的行已完整备份
- ✅ 列结构：保持原始文件的所有列结构
- ✅ 精确匹配：只删除完全匹配 `ensembl_gene_id` 和 `project_id` 组合的行

## 总结

成功完成了基于 `ensembl_gene_id` 和 `project_id` 组合的行删除任务：

- 🎯 **精确匹配：** 删除了 282,554 行完全匹配的数据
- 📊 **高效处理：** 处理了超过1200万行的大型数据集
- 🔒 **数据安全：** 保留了完整的备份和清理后的数据
- ✅ **任务完成：** 两个文件现在具有一致的数据结构

**文件位置：**
- 清理后的主文件：`compute_transcript/translation_indices_results_grouped_with_disease_category_cleaned.csv`
- 被删除行备份：`compute_transcript/deleted_matching_rows.csv`

# 转录本数据处理脚本

这个项目包含用于处理 `Transcript_id.csv` 文件的Python脚本，通过查询数据库为每个转录本ID添加对应的基因信息。

## 功能描述

脚本会为 `Transcript_id.csv` 文件中的每个转录本ID查询数据库，并添加两个新列：
- `ensembl_gene_id`: 包含该转录本对应的所有唯一Ensembl基因ID的JSON数组
- `external_gene_name`: 包含该转录本对应的所有唯一外部基因名的JSON数组

## 文件说明

- `process_transcript_data.py`: 完整版本，包含详细的错误处理和日志记录
- `simple_transcript_processor.py`: 简化版本，更直接的实现
- `requirements.txt`: Python依赖包列表
- `README.md`: 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pandas pymysql
```

## 使用方法

### 方法1: 使用完整版本

```bash
python process_transcript_data.py
```

### 方法2: 使用简化版本

```bash
python simple_transcript_processor.py
```

## 输入文件要求

确保当前目录下有 `Transcript_id.csv` 文件，且该文件包含名为 `Transcript` 的列。

## 输出文件

处理完成后会生成 `Transcript_id_processed.csv` 文件，包含原始数据和新添加的两列。

## 数据库配置

脚本中的数据库配置：
- 主机: **************
- 端口: 3306
- 用户: luty
- 数据库: utr_database
- 表: tpmData

## 输出格式示例

```csv
Transcript,ensembl_gene_id,external_gene_name
ENST00000123456,"[""ENSG00000123456""]","[""GENE1""]"
ENST00000789012,"[""ENSG00000789012"", ""ENSG00000789013""]","[""GENE2"", ""GENE3""]"
```

## 日志记录

完整版本会生成 `transcript_processing.log` 日志文件，记录处理过程中的详细信息。

## 错误处理

脚本包含以下错误处理：
- 数据库连接失败
- CSV文件不存在或格式错误
- 缺少必要的列
- 数据库查询错误
- 确保数据库连接的安全关闭

## 性能优化

- 使用批量查询减少数据库访问次数
- 使用集合去重，避免重复数据
- 内存高效的数据处理方式

## 注意事项

1. 确保数据库服务器可访问
2. 确保有足够的数据库访问权限
3. 对于大文件，处理可能需要一些时间
4. 建议在处理前备份原始CSV文件

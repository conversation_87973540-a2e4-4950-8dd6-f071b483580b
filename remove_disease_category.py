#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除process_gene/gene_count_by_project_results_cleaned.csv文件中的Disease_Category列
"""

import pandas as pd
import os
import shutil
from datetime import datetime

def remove_disease_category_column():
    """删除Disease_Category列"""
    
    input_file = "process_gene/gene_count_by_project_results_cleaned.csv"
    backup_file = f"process_gene/gene_count_by_project_results_cleaned_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    try:
        print("开始处理文件...")
        print(f"输入文件: {input_file}")
        
        # 检查文件是否存在
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"文件不存在: {input_file}")
        
        # 创建备份
        print(f"创建备份文件: {backup_file}")
        shutil.copy2(input_file, backup_file)
        
        # 读取CSV文件
        print("读取CSV文件...")
        df = pd.read_csv(input_file)
        
        print(f"原始文件信息:")
        print(f"  行数: {len(df):,}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        # 检查Disease_Category列是否存在
        if 'Disease_Category' not in df.columns:
            print("警告: Disease_Category列不存在于文件中")
            print("可用的列:", list(df.columns))
            return False
        
        # 显示Disease_Category列的一些统计信息
        print(f"\nDisease_Category列统计:")
        print(f"  唯一值数量: {df['Disease_Category'].nunique()}")
        print(f"  唯一值: {df['Disease_Category'].unique()}")
        print(f"  值计数:")
        value_counts = df['Disease_Category'].value_counts()
        for value, count in value_counts.items():
            print(f"    {value}: {count:,} 条记录")
        
        # 删除Disease_Category列
        print(f"\n删除Disease_Category列...")
        df_cleaned = df.drop('Disease_Category', axis=1)
        
        print(f"处理后文件信息:")
        print(f"  行数: {len(df_cleaned):,}")
        print(f"  列数: {len(df_cleaned.columns)}")
        print(f"  列名: {list(df_cleaned.columns)}")
        
        # 保存处理后的文件
        print(f"\n保存处理后的文件...")
        df_cleaned.to_csv(input_file, index=False)
        
        print(f"✅ 处理完成!")
        print(f"✅ 已删除Disease_Category列")
        print(f"✅ 原文件已更新: {input_file}")
        print(f"✅ 备份文件: {backup_file}")
        
        # 显示处理后的前几行
        print(f"\n处理后的文件前5行:")
        print(df_cleaned.head().to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("删除Disease_Category列")
    print("=" * 60)
    
    success = remove_disease_category_column()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ 任务完成!")
    else:
        print("\n" + "=" * 60)
        print("❌ 任务失败!")

if __name__ == "__main__":
    main()

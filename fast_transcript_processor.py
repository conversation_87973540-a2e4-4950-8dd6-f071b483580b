#!/usr/bin/env python3
"""
高性能版本的转录本数据处理脚本
优化了数据库查询和内存使用
"""

import pandas as pd
import pymysql
import json
import logging
from typing import Dict, Set
import time
from concurrent.futures import ThreadPoolExecutor
import threading

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'port': 3306,
    'database': 'utr_database',
    'charset': 'utf8mb4'
}

class FastTranscriptProcessor:
    """高性能转录本处理器"""
    
    def __init__(self, db_config: Dict):
        self.db_config = db_config
        self.connection_pool = []
        self.pool_lock = threading.Lock()
        
    def create_connection(self):
        """创建数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def get_connection(self):
        """从连接池获取连接"""
        with self.pool_lock:
            if self.connection_pool:
                return self.connection_pool.pop()
            else:
                return self.create_connection()
    
    def return_connection(self, conn):
        """归还连接到连接池"""
        with self.pool_lock:
            if len(self.connection_pool) < 5:  # 最多保持5个连接
                self.connection_pool.append(conn)
            else:
                conn.close()
    
    def query_batch_optimized(self, transcript_ids: list) -> Dict[str, tuple]:
        """优化的批量查询"""
        if not transcript_ids:
            return {}
        
        conn = self.get_connection()
        try:
            # 使用更大的批次大小
            placeholders = ','.join(['%s'] * len(transcript_ids))
            
            # 优化的SQL查询，使用DISTINCT减少重复数据传输
            sql = f"""
            SELECT transcriptId, 
                   GROUP_CONCAT(DISTINCT ensemblGeneId ORDER BY ensemblGeneId) as ensembl_ids,
                   GROUP_CONCAT(DISTINCT externalGeneName ORDER BY externalGeneName) as external_names
            FROM tpmData 
            WHERE transcriptId IN ({placeholders})
            GROUP BY transcriptId
            """
            
            with conn.cursor() as cursor:
                cursor.execute(sql, transcript_ids)
                results = cursor.fetchall()
                
                # 处理结果
                transcript_data = {}
                for transcript_id, ensembl_ids_str, external_names_str in results:
                    ensembl_ids = []
                    external_names = []
                    
                    if ensembl_ids_str:
                        ensembl_ids = [id.strip() for id in ensembl_ids_str.split(',') if id.strip()]
                    
                    if external_names_str:
                        external_names = [name.strip() for name in external_names_str.split(',') if name.strip()]
                    
                    transcript_data[transcript_id] = (ensembl_ids, external_names)
                
                return transcript_data
                
        except Exception as e:
            logger.error(f"批量查询出错: {e}")
            return {}
        finally:
            self.return_connection(conn)
    
    def process_csv_fast(self, input_file: str, output_file: str, batch_size: int = 5000):
        """快速处理CSV文件"""
        start_time = time.time()
        
        try:
            # 读取CSV文件
            logger.info(f"读取CSV文件: {input_file}")
            df = pd.read_csv(input_file)
            
            if 'Transcript' not in df.columns:
                raise ValueError("CSV文件中缺少 'Transcript' 列")
            
            logger.info(f"CSV文件包含 {len(df)} 行数据")
            
            # 获取所有唯一的转录本ID
            unique_transcripts = df['Transcript'].dropna().unique()
            logger.info(f"发现 {len(unique_transcripts)} 个唯一的转录本ID")
            
            # 初始化新列
            df['ensembl_gene_id'] = '[]'
            df['external_gene_name'] = '[]'
            
            # 分批处理，使用更大的批次
            all_transcript_data = {}
            total_batches = (len(unique_transcripts) + batch_size - 1) // batch_size
            
            for i in range(0, len(unique_transcripts), batch_size):
                batch_num = i // batch_size + 1
                batch = unique_transcripts[i:i + batch_size].tolist()
                
                logger.info(f"处理批次 {batch_num}/{total_batches}: {len(batch)} 个转录本")
                
                batch_data = self.query_batch_optimized(batch)
                all_transcript_data.update(batch_data)
                
                # 显示进度
                processed = min(i + batch_size, len(unique_transcripts))
                progress = (processed / len(unique_transcripts)) * 100
                logger.info(f"查询进度: {progress:.1f}% ({processed}/{len(unique_transcripts)})")
            
            logger.info(f"数据库查询完成，获得 {len(all_transcript_data)} 个转录本的数据")
            
            # 使用向量化操作填充数据
            logger.info("填充数据到DataFrame...")
            
            def get_gene_data(transcript_id):
                if pd.isna(transcript_id) or transcript_id not in all_transcript_data:
                    return json.dumps([]), json.dumps([])
                else:
                    ensembl_ids, external_names = all_transcript_data[transcript_id]
                    return json.dumps(ensembl_ids), json.dumps(external_names)
            
            # 使用apply进行向量化操作
            gene_data = df['Transcript'].apply(get_gene_data)
            df['ensembl_gene_id'] = [data[0] for data in gene_data]
            df['external_gene_name'] = [data[1] for data in gene_data]
            
            # 保存结果
            logger.info(f"保存结果到: {output_file}")
            df.to_csv(output_file, index=False)
            
            # 统计信息
            non_empty_ensembl = df[df['ensembl_gene_id'] != '[]'].shape[0]
            non_empty_external = df[df['external_gene_name'] != '[]'].shape[0]
            
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.info("=" * 50)
            logger.info("处理完成!")
            logger.info(f"总处理时间: {total_time:.2f} 秒")
            logger.info(f"平均速度: {len(df)/total_time:.0f} 行/秒")
            logger.info(f"总行数: {len(df):,}")
            logger.info(f"找到ensembl_gene_id的行数: {non_empty_ensembl:,}")
            logger.info(f"找到external_gene_name的行数: {non_empty_external:,}")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
            raise
        finally:
            # 关闭所有连接
            with self.pool_lock:
                for conn in self.connection_pool:
                    conn.close()
                self.connection_pool.clear()

def main():
    """主函数"""
    input_file = 'Transcript_id.csv'
    output_file = 'Transcript_id_processed_fast.csv'
    
    processor = FastTranscriptProcessor(DB_CONFIG)
    
    try:
        # 使用更大的批次大小提高性能
        processor.process_csv_fast(input_file, output_file, batch_size=10000)
        
    except FileNotFoundError:
        logger.error(f"找不到输入文件: {input_file}")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")

if __name__ == "__main__":
    print("🚀 高性能转录本数据处理器")
    print("=" * 50)
    main()

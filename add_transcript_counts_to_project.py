#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys

def add_translated_transcripts_number():
    """
    为project_unique_info.csv添加translatedTranscriptsNumber列
    通过Project ID查询translation_indices_results_grouped.csv中的行数
    """
    
    # 文件路径
    project_info_file = 'compute_gene/project_unique_info.csv'
    translation_file = 'process_transcript/translation_indices_results_grouped.csv'
    output_file = 'compute_gene/project_unique_info_with_transcript_counts.csv'
    log_file = 'add_transcript_counts_to_project_log.txt'
    
    try:
        # 写入处理日志
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("开始处理添加translatedTranscriptsNumber列\n")
            f.write("=" * 50 + "\n")
        
        print("正在读取项目信息文件...")
        
        # 检查文件是否存在
        if not os.path.exists(project_info_file):
            error_msg = f"错误: 文件 {project_info_file} 不存在"
            print(error_msg)
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(error_msg + "\n")
            return False
            
        if not os.path.exists(translation_file):
            error_msg = f"错误: 文件 {translation_file} 不存在"
            print(error_msg)
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(error_msg + "\n")
            return False
        
        # 读取项目信息文件
        project_df = pd.read_csv(project_info_file)
        print(f"项目信息文件读取成功! 数据形状: {project_df.shape}")
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"项目信息文件形状: {project_df.shape}\n")
            f.write(f"项目信息文件列名: {list(project_df.columns)}\n")
            f.write("项目信息文件前5行:\n")
            f.write(str(project_df.head()) + "\n\n")
        
        # 检查Project ID列
        if 'Project ID' not in project_df.columns:
            error_msg = "错误: 项目信息文件中未找到'Project ID'列"
            print(error_msg)
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(error_msg + "\n")
                f.write(f"可用列: {list(project_df.columns)}\n")
            return False
        
        print("正在读取translation indices文件的projectId列...")
        
        # 只读取projectId列以节省内存和时间
        translation_df = pd.read_csv(translation_file, usecols=['projectId'])
        print(f"Translation文件读取成功! 数据形状: {translation_df.shape}")
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"Translation文件形状: {translation_df.shape}\n")
            f.write(f"Translation文件projectId唯一值数量: {translation_df['projectId'].nunique()}\n")
        
        # 计算每个projectId的行数
        print("正在计算每个Project ID的transcript数量...")
        project_counts = translation_df['projectId'].value_counts().to_dict()
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"计算得到的项目数量: {len(project_counts)}\n")
            f.write("前10个项目的transcript数量:\n")
            for i, (pid, count) in enumerate(list(project_counts.items())[:10]):
                f.write(f"  {pid}: {count}\n")
            f.write("\n")
        
        # 为项目信息添加translatedTranscriptsNumber列
        print("正在添加translatedTranscriptsNumber列...")
        
        def get_transcript_count(project_id):
            return project_counts.get(project_id, 0)
        
        project_df['translatedTranscriptsNumber'] = project_df['Project ID'].apply(get_transcript_count)
        
        # 统计信息
        total_projects = len(project_df)
        projects_with_transcripts = len(project_df[project_df['translatedTranscriptsNumber'] > 0])
        projects_without_transcripts = total_projects - projects_with_transcripts
        
        print(f"处理完成!")
        print(f"总项目数: {total_projects}")
        print(f"有transcript数据的项目: {projects_with_transcripts}")
        print(f"无transcript数据的项目: {projects_without_transcripts}")
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("处理结果统计:\n")
            f.write(f"总项目数: {total_projects}\n")
            f.write(f"有transcript数据的项目: {projects_with_transcripts}\n")
            f.write(f"无transcript数据的项目: {projects_without_transcripts}\n")
            f.write(f"translatedTranscriptsNumber列统计:\n")
            f.write(f"  最小值: {project_df['translatedTranscriptsNumber'].min()}\n")
            f.write(f"  最大值: {project_df['translatedTranscriptsNumber'].max()}\n")
            f.write(f"  平均值: {project_df['translatedTranscriptsNumber'].mean():.2f}\n")
            f.write(f"  中位数: {project_df['translatedTranscriptsNumber'].median()}\n")
            f.write("\n前10行结果:\n")
            f.write(str(project_df.head(10)) + "\n")
        
        # 保存结果
        project_df.to_csv(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"结果已保存到: {output_file}\n")
        
        # 显示一些示例结果
        print("\n前10行结果:")
        print(project_df.head(10))
        
        print(f"\ntranslatedTranscriptsNumber列统计:")
        print(f"最小值: {project_df['translatedTranscriptsNumber'].min()}")
        print(f"最大值: {project_df['translatedTranscriptsNumber'].max()}")
        print(f"平均值: {project_df['translatedTranscriptsNumber'].mean():.2f}")
        print(f"中位数: {project_df['translatedTranscriptsNumber'].median()}")
        
        return True
        
    except Exception as e:
        error_msg = f"处理过程中出现错误: {e}"
        print(error_msg)
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(error_msg + "\n")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_translated_transcripts_number()
    if success:
        print("\n处理完成!")
    else:
        print("\n处理失败!")
        sys.exit(1)

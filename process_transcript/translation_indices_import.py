#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import sys
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connection_timeout': 600,  # 10分钟连接超时
    'use_unicode': True,
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translation_indices_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        logging.info("成功连接到MySQL数据库")
        return connection
    except Error as e:
        logging.error(f"连接数据库时出错: {e}")
        return None

def check_connection(connection):
    """检查并重新连接数据库"""
    try:
        if not connection.is_connected():
            logging.warning("数据库连接已断开，尝试重新连接...")
            connection.reconnect(attempts=3, delay=2)
            logging.info("数据库重新连接成功")
        return True
    except Error as e:
        logging.error(f"重新连接数据库失败: {e}")
        return False

def create_translation_indices_table(connection):
    """创建translationIndices表"""
    cursor = connection.cursor()
    
    try:
        # 删除已存在的表（如果存在）
        drop_table_query = "DROP TABLE IF EXISTS translationIndices"
        cursor.execute(drop_table_query)
        logging.info("已删除现有的translationIndices表（如果存在）")
        
        # 创建新表
        create_table_query = """
        CREATE TABLE translationIndices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transcriptId VARCHAR(255) NOT NULL,
            projectId VARCHAR(255) NOT NULL,
            bioprojectId VARCHAR(255),
            tissueCellType VARCHAR(500),
            cellLine VARCHAR(255),
            disease VARCHAR(255),
            tr DECIMAL(15,6),
            evi DECIMAL(15,6),
            te DECIMAL(15,6),
            geneId VARCHAR(255) NOT NULL,
            geneSymbol VARCHAR(255) NOT NULL,
            threeUtrComp TEXT COMMENT '3 UTR component characteristics',
            fiveUtrComp TEXT COMMENT '5 UTR component characteristics'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_query)
        logging.info("成功创建translationIndices表")
        
        # 创建索引以提高检索速度
        indexes = [
            "CREATE INDEX idx_transcript_id ON translationIndices (transcriptId)",
            "CREATE INDEX idx_project_id ON translationIndices (projectId)",
            "CREATE INDEX idx_gene_id ON translationIndices (geneId)",
            "CREATE INDEX idx_gene_symbol ON translationIndices (geneSymbol)",
            "CREATE INDEX idx_bioproject_id ON translationIndices (bioprojectId)",
            "CREATE INDEX idx_transcript_project ON translationIndices (transcriptId, projectId)",  # 复合索引
            "CREATE INDEX idx_gene_project ON translationIndices (geneId, projectId)"  # 复合索引
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
            logging.info(f"成功创建索引: {index_query.split('INDEX ')[1].split(' ON')[0]}")
        
        # 提交事务
        connection.commit()
        
    except Error as e:  
        logging.error(f"创建表时出错: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def import_translation_indices_data(connection, csv_file_path):
    """导入翻译指数数据到数据库"""
    try:
        # 读取CSV文件
        logging.info(f"开始读取CSV文件: {csv_file_path}")
        
        # 由于文件很大，使用分块读取
        chunk_size = 5000
        total_processed = 0
        success_count = 0
        
        # 准备插入语句（不包含id，因为是自增字段）
        insert_query = """
        INSERT INTO translationIndices (
            transcriptId, projectId, bioprojectId, tissueCellType, cellLine, disease,
            tr, evi, te, geneId, geneSymbol, threeUtrComp, fiveUtrComp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 分块处理CSV文件
        for chunk_df in pd.read_csv(csv_file_path, chunksize=chunk_size):
            # 检查连接状态
            if not check_connection(connection):
                logging.error("数据库连接检查失败，停止导入")
                break
            
            # 清理数据 - 处理NaN值
            chunk_df = chunk_df.fillna('')
            
            cursor = connection.cursor()
            
            try:
                # 准备批量数据
                batch_data = []
                for _, row in chunk_df.iterrows():
                    # 处理数值字段
                    def safe_float(value):
                        if pd.isna(value) or value == '' or value == 'nan':
                            return None
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            return None
                    
                    batch_data.append((
                        str(row['transcriptId']),
                        str(row['projectId']),
                        str(row['bioprojectId']),
                        str(row['tissueCellType']),
                        str(row['cellLine']),
                        str(row['disease']),
                        safe_float(row['tr']),
                        safe_float(row['evi']),
                        safe_float(row['te']),
                        str(row['geneId']),
                        str(row['geneSymbol']),
                        str(row['threeUtrComp']),
                        str(row['fiveUtrComp'])
                    ))
                
                # 执行批量插入
                cursor.executemany(insert_query, batch_data)
                connection.commit()
                
                success_count += len(batch_data)
                total_processed += len(chunk_df)
                
                logging.info(f"已处理 {total_processed} 行数据 (成功导入: {success_count})")
                
                # 每处理10个块休息一下，避免连接超时
                if (total_processed // chunk_size) % 10 == 0:
                    time.sleep(0.2)
                
            except Error as e:
                logging.error(f"批次导入失败 (行数 {total_processed-len(chunk_df)+1}-{total_processed}): {e}")
                connection.rollback()
                # 继续处理下一批次
                continue
            finally:
                cursor.close()
        
        logging.info(f"数据导入完成！总共处理 {total_processed} 行，成功导入 {success_count} 行数据")
        
        # 显示统计信息
        show_statistics(connection)
        
    except Exception as e:
        logging.error(f"导入数据时出错: {e}")
        raise

def show_statistics(connection):
    """显示导入统计信息"""
    if not check_connection(connection):
        logging.error("无法连接数据库，跳过统计信息显示")
        return
        
    cursor = connection.cursor()
    
    try:
        # 总记录数
        cursor.execute("SELECT COUNT(*) FROM translationIndices")
        total_count = cursor.fetchone()[0]
        logging.info(f"表中总记录数: {total_count}")
        
        # 唯一转录本数
        cursor.execute("SELECT COUNT(DISTINCT transcriptId) FROM translationIndices")
        unique_transcripts = cursor.fetchone()[0]
        logging.info(f"唯一转录本数: {unique_transcripts}")
        
        # 唯一基因数
        cursor.execute("SELECT COUNT(DISTINCT geneSymbol) FROM translationIndices")
        unique_genes = cursor.fetchone()[0]
        logging.info(f"唯一基因数: {unique_genes}")
        
        # 唯一项目数
        cursor.execute("SELECT COUNT(DISTINCT projectId) FROM translationIndices")
        unique_projects = cursor.fetchone()[0]
        logging.info(f"唯一项目数: {unique_projects}")
        
        # 翻译效率统计
        cursor.execute("""
            SELECT 
                COUNT(CASE WHEN te IS NOT NULL THEN 1 END) as te_count,
                AVG(te) as avg_te,
                MIN(te) as min_te,
                MAX(te) as max_te
            FROM translationIndices
        """)
        
        te_stats = cursor.fetchone()
        if te_stats[0] > 0:
            logging.info(f"翻译效率(TE)统计: 记录数={te_stats[0]}, 平均值={te_stats[1]:.6f}, 最小值={te_stats[2]:.6f}, 最大值={te_stats[3]:.6f}")
        
        # 按疾病分组统计
        cursor.execute("""
            SELECT disease, COUNT(*) as count 
            FROM translationIndices 
            WHERE disease != '' 
            GROUP BY disease 
            ORDER BY count DESC 
            LIMIT 10
        """)
        
        disease_stats = cursor.fetchall()
        logging.info("前10种疾病类型统计:")
        for disease, count in disease_stats:
            logging.info(f"  {disease}: {count}")
            
    except Error as e:
        logging.error(f"显示统计信息时出错: {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    csv_file_path = "translation_indices_results_grouped_with_utr.csv"
    
    # 检查文件是否存在
    import os
    if not os.path.exists(csv_file_path):
        logging.error(f"CSV文件不存在: {csv_file_path}")
        return
    
    # 创建数据库连接
    connection = create_connection()
    if connection is None:
        logging.error("无法连接到数据库，程序退出")
        return
    
    try:
        # 创建表
        create_translation_indices_table(connection)
        
        # 导入数据
        import_translation_indices_data(connection, csv_file_path)
        
        logging.info("翻译指数数据导入任务完成！")
        
    except Exception as e:
        logging.error(f"程序执行出错: {e}")
    finally:
        if connection and connection.is_connected():
            connection.close()
            logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 
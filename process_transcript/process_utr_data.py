#!/usr/bin/env python3
"""
处理CSV文件，通过transcriptId查询并添加fiveUtrComp和threeUtrComp两列
"""

import pandas as pd
import numpy as np
import os

def process_utr_data():
    """
    处理process_transcript/translation_indices_results_grouped.csv文件，
    通过transcriptId查询process_transcript/merged_transcriptID_utr_table_data_3_with_json_data.csv文件，
    通过 TranscriptID 和 UTR_Type 进行精确定位，添加fiveUtrComp和threeUtrComp两列。
    """
    
    # 设置工作目录
    base_dir = "/Volumes/zhy/整合的所有TPM文件"
    os.chdir(base_dir)
    
    print("开始处理UTR数据...")
    
    # 读取主文件
    print("读取 translation_indices_results_grouped.csv...")
    try:
        main_df = pd.read_csv('process_transcript/translation_indices_results_grouped.csv')
        print(f"主文件读取成功，形状: {main_df.shape}")
        print(f"主文件列名: {main_df.columns.tolist()}")
        print("主文件前3行:")
        print(main_df.head(3))
    except Exception as e:
        print(f"读取主文件失败: {e}")
        return
    
    # 读取查询文件
    print("\n读取 merged_transcriptID_utr_table_data_3_with_json_data.csv...")
    try:
        utr_df = pd.read_csv('process_transcript/merged_transcriptID_utr_table_data_3_with_json_data.csv')
        print(f"UTR文件读取成功，形状: {utr_df.shape}")
        print(f"UTR文件列名: {utr_df.columns.tolist()}")
        print("UTR文件前3行:")
        print(utr_df.head(3))
    except Exception as e:
        print(f"读取UTR文件失败: {e}")
        return
    
    # 检查必要的列是否存在
    if 'TranscriptID' not in utr_df.columns:
        print("错误: UTR文件中没有找到 'TranscriptID' 列")
        return
    
    if 'UTR_Type' not in utr_df.columns:
        print("错误: UTR文件中没有找到 'UTR_Type' 列")
        return
    
    if 'UtrComp' not in utr_df.columns:
        print("错误: UTR文件中没有找到 'UtrComp' 列")
        return
    
    # 检查主文件中的transcriptId列名
    transcript_col = None
    for col in main_df.columns:
        if 'transcript' in col.lower() and 'id' in col.lower():
            transcript_col = col
            break
    
    if transcript_col is None:
        print("错误: 主文件中没有找到包含 'transcript' 和 'id' 的列")
        print("可用列名:", main_df.columns.tolist())
        return
    
    print(f"使用主文件中的列: {transcript_col}")
    
    # 初始化新列
    main_df['fiveUtrComp'] = ''
    main_df['threeUtrComp'] = ''
    
    # 创建UTR数据的查询字典
    print("\n创建UTR数据查询字典...")
    utr_5_dict = {}
    utr_3_dict = {}
    
    for _, row in utr_df.iterrows():
        transcript_id = row['TranscriptID']
        utr_type = row['UTR_Type']
        utr_comp = row['UtrComp']
        
        if pd.isna(transcript_id) or pd.isna(utr_type):
            continue
            
        if utr_type == '5UTR':
            utr_5_dict[transcript_id] = utr_comp
        elif utr_type == '3UTR':
            utr_3_dict[transcript_id] = utr_comp
    
    print(f"5UTR数据条数: {len(utr_5_dict)}")
    print(f"3UTR数据条数: {len(utr_3_dict)}")
    
    # 更新主文件
    print("\n更新主文件数据...")
    updated_count = 0
    
    for idx, row in main_df.iterrows():
        transcript_id = row[transcript_col]
        
        if pd.isna(transcript_id):
            continue
            
        # 查找5UTR数据
        if transcript_id in utr_5_dict:
            main_df.at[idx, 'fiveUtrComp'] = utr_5_dict[transcript_id]
            updated_count += 1
        
        # 查找3UTR数据
        if transcript_id in utr_3_dict:
            main_df.at[idx, 'threeUtrComp'] = utr_3_dict[transcript_id]
            updated_count += 1
    
    print(f"更新了 {updated_count} 个数据项")
    
    # 保存结果
    output_file = 'process_transcript/translation_indices_results_grouped_with_utr.csv'
    print(f"\n保存结果到: {output_file}")
    main_df.to_csv(output_file, index=False)
    
    # 显示统计信息
    print("\n处理完成！统计信息:")
    print(f"总行数: {len(main_df)}")
    print(f"有5UTR数据的行数: {(main_df['fiveUtrComp'] != '').sum()}")
    print(f"有3UTR数据的行数: {(main_df['threeUtrComp'] != '').sum()}")
    
    # 显示示例结果
    print("\n示例结果（前5行）:")
    print(main_df[['fiveUtrComp', 'threeUtrComp']].head())

if __name__ == "__main__":
    process_utr_data()

#!/usr/bin/env python3
"""
不依赖pandas的简单CSV格式化脚本
将JSON格式的列转换为分号分隔格式
"""

import csv
import json

def format_json_value(value):
    """将JSON格式转换为分号分隔格式"""
    try:
        if not value or value == '' or value == '[]':
            return ''
        
        # 解析JSON
        data_list = json.loads(value)
        
        if not data_list:
            return ''
        elif len(data_list) == 1:
            return data_list[0]
        else:
            return '; '.join(data_list)
    except:
        return str(value)

def main():
    input_file = 'Transcript_id_processed_fast.csv'
    output_file = 'Transcript_id_formatted.csv'
    
    print(f"读取文件: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            
            # 读取表头
            header = next(reader)
            print(f"表头: {header}")
            
            # 找到需要处理的列的索引
            ensembl_col = None
            external_col = None
            
            for i, col_name in enumerate(header):
                if col_name == 'ensembl_gene_id':
                    ensembl_col = i
                elif col_name == 'external_gene_name':
                    external_col = i
            
            if ensembl_col is None or external_col is None:
                print("错误: 找不到需要的列")
                return
            
            print(f"ensembl_gene_id 列索引: {ensembl_col}")
            print(f"external_gene_name 列索引: {external_col}")
            
            # 读取所有数据
            rows = []
            for row in reader:
                rows.append(row)
            
            print(f"读取了 {len(rows)} 行数据")
            
        # 处理数据并写入新文件
        print(f"处理数据并保存到: {output_file}")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            
            # 写入表头
            writer.writerow(header)
            
            # 处理并写入数据
            processed_count = 0
            for i, row in enumerate(rows):
                if len(row) > max(ensembl_col, external_col):
                    # 处理两个目标列
                    row[ensembl_col] = format_json_value(row[ensembl_col])
                    row[external_col] = format_json_value(row[external_col])
                    processed_count += 1
                
                writer.writerow(row)
                
                # 显示进度
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
        
        print("=" * 40)
        print("处理完成!")
        print(f"总行数: {len(rows)}")
        print(f"成功处理: {processed_count} 行")
        print(f"输出文件: {output_file}")
        print("=" * 40)
        
        # 显示一些示例
        print("\n处理后的示例数据:")
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            
            count = 0
            for row in reader:
                if len(row) > max(ensembl_col, external_col):
                    if row[ensembl_col] and row[ensembl_col] != '':
                        print(f"行 {count + 1}:")
                        print(f"  ensembl_gene_id: {row[ensembl_col]}")
                        print(f"  external_gene_name: {row[external_col]}")
                        count += 1
                        if count >= 3:
                            break
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("🔧 CSV格式化工具 (不依赖pandas)")
    print("📝 将JSON格式的列转换为分号分隔格式")
    print("=" * 50)
    main()

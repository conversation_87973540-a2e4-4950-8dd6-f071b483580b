#!/usr/bin/env python3
"""
为 Sample.csv 文件添加 TRANSLATED TRANSCRIPTS NUMBER 列
通过 SRA Accession 查询数据库中对应的转录本数量
"""

import csv
import mysql.connector
from collections import defaultdict
import time

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def connect_to_database():
    """连接到数据库"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def get_transcript_counts_batch(connection, sra_accessions):
    """批量查询 SRA Accession 对应的转录本数量"""
    
    if not sra_accessions:
        return {}
    
    try:
        cursor = connection.cursor()
        
        # 构建批量查询的 SQL
        placeholders = ','.join(['%s'] * len(sra_accessions))
        query = f"""
        SELECT sraAccession, COUNT(*) as transcript_count
        FROM tpmData 
        WHERE sraAccession IN ({placeholders})
        GROUP BY sraAccession
        """
        
        cursor.execute(query, list(sra_accessions))
        results = cursor.fetchall()
        
        # 转换为字典
        counts = {}
        for sra_accession, count in results:
            counts[sra_accession] = count
        
        cursor.close()
        return counts
        
    except mysql.connector.Error as e:
        print(f"❌ 查询数据库时出错: {e}")
        return {}

def process_sample_file():
    """处理 Sample.csv 文件，添加转录本数量列"""
    
    input_file = 'Sample.csv'
    output_file = 'Sample_with_transcript_counts.csv'
    
    print("=== 为 Sample.csv 添加转录本数量 ===")
    
    # 连接数据库
    connection = connect_to_database()
    if not connection:
        return False
    
    try:
        # 第一步：读取所有 SRA Accession
        print("正在读取 Sample.csv 文件...")
        
        all_sra_accessions = set()
        sample_data = []
        
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                sra_accession = row['SRA Accession'].strip()
                all_sra_accessions.add(sra_accession)
                sample_data.append(row)
        
        print(f"读取了 {len(sample_data)} 行样本数据")
        print(f"发现 {len(all_sra_accessions)} 个唯一的 SRA Accession")
        
        # 第二步：批量查询转录本数量
        print("正在查询数据库中的转录本数量...")
        
        batch_size = 100  # 每批查询100个
        all_counts = {}
        
        sra_list = list(all_sra_accessions)
        for i in range(0, len(sra_list), batch_size):
            batch = sra_list[i:i + batch_size]
            batch_counts = get_transcript_counts_batch(connection, batch)
            all_counts.update(batch_counts)
            
            print(f"  已查询 {min(i + batch_size, len(sra_list))}/{len(sra_list)} 个 SRA Accession")
        
        print(f"查询完成，获得 {len(all_counts)} 个 SRA Accession 的转录本数量")
        
        # 第三步：写入新文件
        print("正在生成新文件...")
        
        matched_count = 0
        unmatched_count = 0
        unmatched_examples = []
        
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            # 创建新的列名
            fieldnames = list(sample_data[0].keys()) + ['TRANSLATED TRANSCRIPTS NUMBER']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for row in sample_data:
                sra_accession = row['SRA Accession'].strip()
                
                # 获取转录本数量
                if sra_accession in all_counts:
                    transcript_count = all_counts[sra_accession]
                    matched_count += 1
                else:
                    transcript_count = 0
                    unmatched_count += 1
                    if len(unmatched_examples) < 5:
                        unmatched_examples.append(sra_accession)
                
                # 添加转录本数量
                row['TRANSLATED TRANSCRIPTS NUMBER'] = transcript_count
                writer.writerow(row)
        
        # 输出统计信息
        print(f"\n处理完成!")
        print(f"总样本数: {len(sample_data):,}")
        print(f"成功匹配: {matched_count:,}")
        print(f"未匹配: {unmatched_count:,}")
        print(f"匹配率: {matched_count/len(sample_data)*100:.2f}%")
        
        if unmatched_examples:
            print(f"\n未匹配的 SRA Accession 示例:")
            for sra in unmatched_examples:
                print(f"  - {sra}")
        
        # 显示一些统计信息
        transcript_counts = [count for count in all_counts.values() if count > 0]
        if transcript_counts:
            print(f"\n转录本数量统计:")
            print(f"  最小值: {min(transcript_counts):,}")
            print(f"  最大值: {max(transcript_counts):,}")
            print(f"  平均值: {sum(transcript_counts)/len(transcript_counts):.0f}")
        
        print(f"\n结果已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return False
    
    finally:
        connection.close()
        print("数据库连接已关闭")

def verify_results():
    """验证处理结果"""
    
    output_file = 'Sample_with_transcript_counts.csv'
    
    print("\n=== 验证处理结果 ===")
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"新文件的列名 (共 {len(headers)} 列):")
            for i, header in enumerate(headers):
                print(f"  {i+1}. {header}")
            
            # 检查前几行
            print(f"\n前5行数据示例:")
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n行 {i+1}:")
                print(f"  SRA Accession: {row['SRA Accession']}")
                print(f"  Dataset ID: {row['Dataset ID']}")
                print(f"  Condition: {row['Condition']}")
                print(f"  TRANSLATED TRANSCRIPTS NUMBER: {row['TRANSLATED TRANSCRIPTS NUMBER']}")
            
            # 统计转录本数量分布
            f.seek(0)  # 重置文件指针
            reader = csv.DictReader(f)
            next(reader)  # 跳过表头
            
            transcript_counts = []
            zero_count = 0
            total_count = 0
            
            for row in reader:
                total_count += 1
                count = int(row['TRANSLATED TRANSCRIPTS NUMBER']) if row['TRANSLATED TRANSCRIPTS NUMBER'].isdigit() else 0
                transcript_counts.append(count)
                if count == 0:
                    zero_count += 1
            
            print(f"\n统计信息:")
            print(f"总样本数: {total_count:,}")
            print(f"转录本数量为0的样本: {zero_count:,}")
            print(f"有转录本数据的样本: {total_count - zero_count:,}")
            print(f"有效数据比例: {(total_count - zero_count)/total_count*100:.2f}%")
            
            if transcript_counts:
                non_zero_counts = [c for c in transcript_counts if c > 0]
                if non_zero_counts:
                    print(f"转录本数量范围: {min(non_zero_counts):,} - {max(non_zero_counts):,}")
                    print(f"平均转录本数量: {sum(non_zero_counts)/len(non_zero_counts):.0f}")
        
        return True
        
    except Exception as e:
        print(f"验证结果时出错: {e}")
        return False

def main():
    """主函数"""
    
    # 处理文件
    if process_sample_file():
        # 验证结果
        verify_results()
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()

# 基因ID比较分析报告

## 分析概述
比较 `process_gene/unique_genes.csv` 和 `process_gene/ensembl_gene_id_with_full_info_split_2_processed.csv` 两个文件中的基因ID。

## 文件信息

### 文件1: unique_genes.csv
- **行数**: 23,987 (包含表头)
- **唯一基因ID数**: 23,987
- **列**: ensembl_gene_id, external_gene_name

### 文件2: ensembl_gene_id_with_full_info_split_2_processed.csv  
- **行数**: 25,787 (包含表头)
- **唯一基因ID数**: 25,787
- **列**: GENE ID, GENE symbol, Approved Name, Locus Type, Chromosome, Chromosome_URL, transcript_count, transcripts

## 比较结果

### 📊 统计摘要
| 指标 | 数量 | 百分比 |
|------|------|--------|
| 共同基因数 | 22,998 | 95.88% |
| 文件1独有基因数 | 989 | 4.12% |
| 文件2独有基因数 | 2,789 | - |
| 总匹配率 | - | 95.88% |

### ⚠️ 关键发现
- **989个基因ID** 在 `unique_genes.csv` 中存在，但在 `ensembl_gene_id_with_full_info_split_2_processed.csv` 的 `GENE ID` 列中**不存在**
- 匹配率为 **95.88%**，说明大部分基因ID是匹配的
- 文件2比文件1多了2,789个基因ID

## 缺失基因分析

### 前20个缺失的基因ID
1. ENSG00000007306 - CEACAM7
2. ENSG00000016490 - CLCA1  
3. ENSG00000077498 - TYR
4. ENSG00000086717 - PPEF1
5. ENSG00000096264 - NCR2
6. ENSG00000096395 - MLN
7. ENSG00000101292 - PROKR2
8. ENSG00000102104 - RS1
9. ENSG00000102145 - GATA1
10. ENSG00000104499 - GML
11. ENSG00000108958 - SDHCP5
12. ENSG00000109101 - FOXN1
13. ENSG00000110484 - SCGB2A2
14. ENSG00000111701 - APOBEC1
15. ENSG00000112041 - TULP1
16. ENSG00000113302 - IL12B
17. ENSG00000114547 - ROPN1B
18. ENSG00000116726 - PRAMEF12
19. ENSG00000122133 - PAEP
20. ENSG00000123171 - CCDC70

### 基因类型分析
从缺失的基因名称可以看出，这些基因包括：
- **免疫相关基因**: IL12B, NCR2, GATA1
- **代谢相关基因**: TYR, APOBEC1
- **发育相关基因**: FOXN1, TULP1
- **其他功能基因**: 各种蛋白编码基因

## 输出文件

### 生成的文件
1. **`process_gene/missing_genes.csv`** - 包含989个缺失基因的完整列表
   - 列: ensembl_gene_id, external_gene_name
   - 按基因ID排序

2. **`comparison_results.txt`** - 详细的比较分析结果

## 可能原因分析

### 为什么会有缺失的基因ID？
1. **数据源不同**: 两个文件可能来自不同的数据源或版本
2. **过滤条件**: 文件2可能应用了特定的过滤条件
3. **基因类型**: 缺失的基因可能属于特定类型（如假基因、非编码基因等）
4. **数据更新**: 基因注释数据库的版本差异

## 建议

### 下一步行动
1. **检查缺失基因**: 验证这989个基因是否应该包含在文件2中
2. **数据源核实**: 确认两个文件的数据源和版本
3. **业务影响评估**: 评估缺失基因对下游分析的影响
4. **数据补充**: 如需要，从原始数据源补充缺失的基因信息

---
*报告生成时间: 2025-07-31*
*分析工具: Python pandas*

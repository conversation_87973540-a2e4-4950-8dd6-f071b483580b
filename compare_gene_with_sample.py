#!/usr/bin/env python3
"""
脚本功能：
1. 读取gene_sample_ids.csv文件中的样本ID
2. 读取Sample.csv文件的第一列（SRA Accession）
3. 比较两者，找出Gene文件夹中有但Sample.csv中没有的样本ID
4. 生成比较结果报告
"""

import csv
from collections import defaultdict

def read_gene_sample_ids(csv_file):
    """读取gene_sample_ids.csv文件中的所有样本ID"""
    all_sample_ids = set()
    folder_sample_ids = {'Gene_01': set(), 'Gene_02': set(), 'Gene_03': set()}
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)  # 读取表头
            print(f"Gene文件表头: {header}")
            
            for row in reader:
                if len(row) >= 3:
                    # Gene_01
                    if row[0].strip():
                        sample_id = row[0].strip()
                        all_sample_ids.add(sample_id)
                        folder_sample_ids['Gene_01'].add(sample_id)
                    
                    # Gene_02
                    if row[1].strip():
                        sample_id = row[1].strip()
                        all_sample_ids.add(sample_id)
                        folder_sample_ids['Gene_02'].add(sample_id)
                    
                    # Gene_03
                    if row[2].strip():
                        sample_id = row[2].strip()
                        all_sample_ids.add(sample_id)
                        folder_sample_ids['Gene_03'].add(sample_id)
    
    except FileNotFoundError:
        print(f"错误：找不到文件 {csv_file}")
        return set(), {}
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return set(), {}
    
    return all_sample_ids, folder_sample_ids

def read_sample_csv(csv_file):
    """读取Sample.csv文件的第一列（SRA Accession）"""
    sample_ids = set()
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)  # 跳过表头
            print(f"Sample.csv表头: {header[0]}")
            
            for row in reader:
                if row and row[0]:  # 确保第一列不为空
                    # 去除可能的引号
                    sample_id = row[0].strip().strip('"')
                    sample_ids.add(sample_id)
    except FileNotFoundError:
        print(f"错误：找不到文件 {csv_file}")
        return set()
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return set()
    
    return sample_ids

def main():
    # 读取Gene文件夹的样本ID
    print("正在读取gene_sample_ids.csv文件...")
    all_gene_sample_ids, folder_sample_ids = read_gene_sample_ids('gene_sample_ids.csv')
    
    print(f"\nGene文件夹样本ID统计:")
    for folder, sample_ids in folder_sample_ids.items():
        print(f"  {folder}: {len(sample_ids)} 个样本ID")
    print(f"Gene文件夹总计: {len(all_gene_sample_ids)} 个唯一样本ID")
    
    # 读取Sample.csv文件
    print("\n正在读取Sample.csv文件...")
    sample_csv_ids = read_sample_csv('Sample.csv')
    print(f"Sample.csv中有 {len(sample_csv_ids)} 个样本ID")
    
    # 比较分析
    print("\n正在进行比较分析...")
    
    # 在Gene文件夹中但不在Sample.csv中的样本ID
    missing_in_sample = all_gene_sample_ids - sample_csv_ids
    
    # 在Sample.csv中但不在Gene文件夹中的样本ID
    missing_in_gene = sample_csv_ids - all_gene_sample_ids
    
    # 共同的样本ID
    common_samples = all_gene_sample_ids & sample_csv_ids
    
    # 输出结果
    print(f"\n=== 比较结果 ===")
    print(f"Gene文件夹中的样本ID总数: {len(all_gene_sample_ids)}")
    print(f"Sample.csv中的样本ID总数: {len(sample_csv_ids)}")
    print(f"共同的样本ID数量: {len(common_samples)}")
    print(f"Gene文件夹中有但Sample.csv中没有的: {len(missing_in_sample)}")
    print(f"Sample.csv中有但Gene文件夹中没有的: {len(missing_in_gene)}")
    
    # 计算覆盖率
    if len(all_gene_sample_ids) > 0:
        coverage_rate = len(common_samples) / len(all_gene_sample_ids) * 100
        print(f"Gene文件夹样本在Sample.csv中的覆盖率: {coverage_rate:.2f}%")
    
    # 保存Gene文件夹中有但Sample.csv中没有的样本ID
    if missing_in_sample:
        output_file = 'gene_missing_in_sample.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID', 'Found_In_Folders', 'Status'])
            
            for sample_id in sorted(missing_in_sample):
                # 检查这个样本ID在哪些Gene文件夹中出现
                found_in = []
                for folder, sample_ids in folder_sample_ids.items():
                    if sample_id in sample_ids:
                        found_in.append(folder)
                
                writer.writerow([sample_id, ', '.join(found_in), 'Missing in Sample.csv'])
        
        print(f"\nGene文件夹中有但Sample.csv中没有的样本ID已保存到: {output_file}")
        
        # 显示前20个缺失的样本ID
        print(f"\n前20个Gene文件夹中有但Sample.csv中没有的样本ID:")
        for i, sample_id in enumerate(sorted(missing_in_sample)[:20]):
            # 显示这个样本ID在哪些文件夹中出现
            found_in = []
            for folder, sample_ids in folder_sample_ids.items():
                if sample_id in sample_ids:
                    found_in.append(folder)
            print(f"  {i+1}. {sample_id} (出现在: {', '.join(found_in)})")
        
        if len(missing_in_sample) > 20:
            print(f"  ... 还有 {len(missing_in_sample) - 20} 个")
    else:
        print("\n所有Gene文件夹中的样本ID都在Sample.csv中找到了！")
    
    # 保存Sample.csv中有但Gene文件夹中没有的样本ID
    if missing_in_gene:
        output_file2 = 'sample_missing_in_gene.csv'
        with open(output_file2, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample_ID', 'Status'])
            
            for sample_id in sorted(missing_in_gene):
                writer.writerow([sample_id, 'Missing in Gene folders'])
        
        print(f"\nSample.csv中有但Gene文件夹中没有的样本ID已保存到: {output_file2}")
        
        # 显示前20个
        print(f"\n前20个Sample.csv中有但Gene文件夹中没有的样本ID:")
        for i, sample_id in enumerate(sorted(missing_in_gene)[:20]):
            print(f"  {i+1}. {sample_id}")
        
        if len(missing_in_gene) > 20:
            print(f"  ... 还有 {len(missing_in_gene) - 20} 个")
    
    # 保存完整的比较报告
    report_file = 'gene_sample_comparison_report.csv'
    with open(report_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Sample_ID', 'In_Gene_01', 'In_Gene_02', 'In_Gene_03', 'In_Sample_CSV', 'Status'])
        
        # 所有样本ID的并集
        all_sample_ids = all_gene_sample_ids | sample_csv_ids
        
        for sample_id in sorted(all_sample_ids):
            in_gene_01 = sample_id in folder_sample_ids['Gene_01']
            in_gene_02 = sample_id in folder_sample_ids['Gene_02']
            in_gene_03 = sample_id in folder_sample_ids['Gene_03']
            in_sample = sample_id in sample_csv_ids
            in_any_gene = sample_id in all_gene_sample_ids
            
            if in_any_gene and in_sample:
                status = 'Common'
            elif in_any_gene and not in_sample:
                status = 'Missing in Sample.csv'
            elif not in_any_gene and in_sample:
                status = 'Missing in Gene folders'
            else:
                status = 'Unknown'
            
            writer.writerow([sample_id, in_gene_01, in_gene_02, in_gene_03, in_sample, status])
    
    print(f"\n完整比较报告已保存到: {report_file}")
    print("\n处理完成！")

if __name__ == "__main__":
    main()

=== translation_indices_results_grouped.csv 文件结构 ===
列名:
  1. transcript_id
  2. project_id
  3. bioproject_id
  4. Tissue/Cell Type
  5. Cell line
  6. Condition
  7. TR
  8. EVI
  9. TE
  10. ensembl_gene_id
  11. external_gene_name

前3行数据:
  行 1: ['ENST00000673477', 'TEDD00024', 'PRJNA244941', '', 'U-2 OS', 'Osteosarcoma', '', '', '1.191571878142555', 'ENSG00000160072', 'ATAD3B']
  行 2: ['ENST00000308647', 'TEDD00024', 'PRJNA244941', '', 'U-2 OS', 'Osteosarcoma', '', '', '0.49428534644472677', 'ENSG00000160072', 'ATAD3B']
  行 3: ['ENST00000288774', 'TEDD00024', 'PRJNA244941', '', 'U-2 OS', 'Osteosarcoma', '', '', '0.8021144808212038', 'ENSG00000157911', 'PEX10']

============================================================
=== project_unique_info.csv 文件结构 ===
列名:
  1. Project ID
  2. Tissue/Cell Type
  3. Cell line
  4. Condition
  5. Disease Category

前3行数据:
  行 1: ['TEDD00001', 'NA', 'Huh7', 'Adult Hepatocellular Carcinoma; Dengue Virus Infection', 'Infectious Disease']
  行 2: ['TEDD00002', 'NA', 'Huh7', 'Adult Hepatocellular Carcinoma; Dengue Virus Infection', 'Infectious Disease']
  行 3: ['TEDD00003', 'NA', 'Huh7', 'Adult Hepatocellular Carcinoma; Dengue Virus Infection', 'Infectious Disease']

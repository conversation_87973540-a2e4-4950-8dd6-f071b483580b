#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def show_results():
    """显示处理结果"""
    
    # 检查删除的行文件
    deleted_file = "compute_gene/deleted_rows_tr_evi_te_empty.csv"
    cleaned_file = "compute_gene/gene_count_by_project_results_cleaned.csv"
    original_file = "compute_gene/gene_count_by_project_results_with_translation_indices_and_project_info.csv"
    
    print("=== 文件处理结果报告 ===\n")
    
    # 检查删除的行文件
    if os.path.exists(deleted_file):
        deleted_df = pd.read_csv(deleted_file)
        print(f"✅ 已保存TR,EVI,TE同时为空的行到: {deleted_file}")
        print(f"   删除的行数: {len(deleted_df)}")
        print(f"   包含的列: {list(deleted_df.columns)}")
        print(f"   前5行示例:")
        print(deleted_df.head().to_string(index=False))
        print()
    else:
        print(f"❌ 删除的行文件不存在: {deleted_file}")
    
    # 检查清理后的文件
    if os.path.exists(cleaned_file):
        print(f"✅ 已保存清理后的数据到: {cleaned_file}")
        
        # 获取文件大小
        file_size = os.path.getsize(cleaned_file)
        print(f"   清理后文件大小: {file_size:,} bytes")
        
        try:
            # 读取少量数据查看结构
            cleaned_df = pd.read_csv(cleaned_file, nrows=0)  # 只读取列名
            print(f"   清理后数据列数: {len(cleaned_df.columns)}")
            
            # 计算总行数（不包括标题行）
            with open(cleaned_file, 'r') as f:
                line_count = sum(1 for line in f) - 1  # 减去标题行
            print(f"   清理后数据行数: {line_count:,}")
            
        except Exception as e:
            print(f"   读取清理后文件时出错: {e}")
        print()
    else:
        print(f"❌ 清理后的文件不存在: {cleaned_file}")
    
    # 检查原始文件
    if os.path.exists(original_file):
        file_size = os.path.getsize(original_file)
        print(f"📊 原始文件信息: {original_file}")
        print(f"   原始文件大小: {file_size:,} bytes")
        
        try:
            # 计算原始文件总行数
            with open(original_file, 'r') as f:
                original_line_count = sum(1 for line in f) - 1  # 减去标题行
            print(f"   原始数据行数: {original_line_count:,}")
            
            if os.path.exists(deleted_file) and os.path.exists(cleaned_file):
                deleted_count = len(pd.read_csv(deleted_file))
                with open(cleaned_file, 'r') as f:
                    cleaned_count = sum(1 for line in f) - 1
                
                print(f"\n📈 处理统计:")
                print(f"   原始行数: {original_line_count:,}")
                print(f"   删除行数: {deleted_count:,}")
                print(f"   剩余行数: {cleaned_count:,}")
                print(f"   删除比例: {deleted_count/original_line_count*100:.2f}%")
                
        except Exception as e:
            print(f"   分析原始文件时出错: {e}")
    else:
        print(f"❌ 原始文件不存在: {original_file}")
    
    print("\n=== 处理完成 ===")
    print("✅ TR, EVI, TE同时为空的行已被删除")
    print("✅ 删除的行的ensembl_gene_id, external_gene_name, project_id已保存到新文件")
    print("✅ 清理后的完整数据已保存到新文件")

if __name__ == "__main__":
    show_results()

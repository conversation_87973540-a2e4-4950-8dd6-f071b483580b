#!/usr/bin/env python3
import pandas as pd

# 将验证结果写入文件
with open('merge_verification.txt', 'w', encoding='utf-8') as f:
    f.write("合并结果验证报告\n")
    f.write("=" * 50 + "\n\n")
    
    try:
        # 读取合并后的文件
        df = pd.read_csv('process_gene/ensembl_gene_id_with_full_info_2.csv')
        
        f.write(f"文件信息:\n")
        f.write(f"  行数: {len(df)}\n")
        f.write(f"  列数: {len(df.columns)}\n")
        f.write(f"  列名: {list(df.columns)}\n\n")
        
        # 检查新添加的列
        if 'transcript_count' in df.columns:
            f.write(f"✅ transcript_count 列已添加\n")
            f.write(f"  数据类型: {df['transcript_count'].dtype}\n")
            f.write(f"  空值数: {df['transcript_count'].isnull().sum()}\n")
            f.write(f"  统计: min={df['transcript_count'].min()}, max={df['transcript_count'].max()}, mean={df['transcript_count'].mean():.2f}\n")
        else:
            f.write(f"❌ transcript_count 列缺失\n")
        
        if 'transcripts' in df.columns:
            f.write(f"\n✅ transcripts 列已添加\n")
            f.write(f"  数据类型: {df['transcripts'].dtype}\n")
            f.write(f"  空值数: {df['transcripts'].isnull().sum()}\n")
        else:
            f.write(f"❌ transcripts 列缺失\n")
        
        # 显示前几行的关键信息
        f.write(f"\n前5行数据:\n")
        display_cols = ['ensembl_gene_id', 'external_gene_name']
        if 'transcript_count' in df.columns:
            display_cols.append('transcript_count')
        
        for i, (_, row) in enumerate(df[display_cols].head().iterrows()):
            f.write(f"  {i+1}. {row['ensembl_gene_id']} - {row['external_gene_name']}")
            if 'transcript_count' in df.columns:
                f.write(f" - {row['transcript_count']} 个转录本")
            f.write(f"\n")
        
        # 检查转录本数量分布
        if 'transcript_count' in df.columns:
            f.write(f"\n转录本数量分布 (前10):\n")
            count_dist = df['transcript_count'].value_counts().head(10)
            for count, freq in count_dist.items():
                f.write(f"  {count} 个转录本: {freq} 个基因\n")
        
        f.write(f"\n✅ 验证完成!\n")
        
    except Exception as e:
        f.write(f"❌ 验证失败: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("验证结果已写入 merge_verification.txt")

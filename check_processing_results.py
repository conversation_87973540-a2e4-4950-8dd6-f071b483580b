#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def check_results():
    """检查处理结果"""
    
    print("=== 检查处理结果 ===\n")
    
    # 检查输入文件
    deleted_rows_file = "compute_gene/deleted_rows_tr_evi_te_empty.csv"
    original_transcript_file = "compute_transcript/translation_indices_results_grouped_with_disease_category.csv"
    cleaned_transcript_file = "compute_transcript/translation_indices_results_grouped_with_disease_category_cleaned.csv"
    deleted_matching_file = "compute_transcript/deleted_matching_rows.csv"
    
    # 检查删除行文件
    if os.path.exists(deleted_rows_file):
        try:
            deleted_df = pd.read_csv(deleted_rows_file)
            unique_combinations = deleted_df[['ensembl_gene_id', 'project_id']].drop_duplicates()
            print(f"✅ 删除行文件: {len(deleted_df):,} 行")
            print(f"   唯一基因-项目组合: {len(unique_combinations):,}")
        except Exception as e:
            print(f"❌ 读取删除行文件出错: {e}")
    else:
        print(f"❌ 删除行文件不存在")
    
    # 检查原始transcript文件
    if os.path.exists(original_transcript_file):
        try:
            # 只读取文件大小和行数，不加载全部数据
            with open(original_transcript_file, 'r') as f:
                original_lines = sum(1 for line in f) - 1  # 减去标题行
            file_size = os.path.getsize(original_transcript_file)
            print(f"✅ 原始transcript文件: {original_lines:,} 行, {file_size:,} bytes")
        except Exception as e:
            print(f"❌ 检查原始transcript文件出错: {e}")
    else:
        print(f"❌ 原始transcript文件不存在")
    
    # 检查清理后的文件
    if os.path.exists(cleaned_transcript_file):
        try:
            with open(cleaned_transcript_file, 'r') as f:
                cleaned_lines = sum(1 for line in f) - 1  # 减去标题行
            file_size = os.path.getsize(cleaned_transcript_file)
            print(f"✅ 清理后transcript文件: {cleaned_lines:,} 行, {file_size:,} bytes")
            
            if os.path.exists(original_transcript_file):
                with open(original_transcript_file, 'r') as f:
                    original_lines = sum(1 for line in f) - 1
                deleted_count = original_lines - cleaned_lines
                print(f"   删除了: {deleted_count:,} 行 ({deleted_count/original_lines*100:.2f}%)")
                
        except Exception as e:
            print(f"❌ 检查清理后文件出错: {e}")
    else:
        print(f"❌ 清理后文件不存在")
    
    # 检查被删除的匹配行文件
    if os.path.exists(deleted_matching_file):
        try:
            with open(deleted_matching_file, 'r') as f:
                deleted_matching_lines = sum(1 for line in f) - 1  # 减去标题行
            file_size = os.path.getsize(deleted_matching_file)
            print(f"✅ 被删除的匹配行文件: {deleted_matching_lines:,} 行, {file_size:,} bytes")
        except Exception as e:
            print(f"❌ 检查被删除匹配行文件出错: {e}")
    else:
        print(f"❌ 被删除匹配行文件不存在")
    
    # 检查文件是否成功创建
    print(f"\n📁 输出文件状态:")
    files_to_check = [
        cleaned_transcript_file,
        deleted_matching_file
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size:,} bytes)")
        else:
            print(f"   ❌ {file_path} (不存在)")
    
    print(f"\n=== 检查完成 ===")

if __name__ == "__main__":
    check_results()

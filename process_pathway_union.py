import pandas as pd

def union_project_ids(project_ids_list):
    """将分号分隔的project_ids字符串列表合并为唯一值"""
    all_ids = set()
    for ids_str in project_ids_list:
        if pd.notna(ids_str) and ids_str.strip():
            ids = [id.strip() for id in str(ids_str).split(';') if id.strip()]
            all_ids.update(ids)
    return ';'.join(sorted(all_ids))

# 读取CSV文件
df = pd.read_csv('process_gene/KEGG_final_with_project_ids.csv')

# 按Pathway_Description分组，对每组的translation_project_ids求并集
result = df.groupby('Pathway_Description')['translation_project_ids'].apply(
    lambda x: union_project_ids(x.tolist())
).reset_index()

# 保存结果
result.to_csv('process_gene/pathway_union_result.csv', index=False)

print(f"处理完成！生成了 {len(result)} 个唯一的Pathway_Description")
print(f"结果已保存到 process_gene/pathway_union_result.csv")
print("\n前5行预览：")
print(result.head())
#!/usr/bin/env python3
"""
为 Transcript_id_formatted.csv 添加一列，标识是否存在多个值
如果 ensembl_gene_id 或 external_gene_name 存在两个或更多值，则为 false，否则为 yes
"""

import csv

def has_multiple_values(value):
    """
    检查字符串中是否包含多个值（通过分号分隔符判断）
    
    Args:
        value: 要检查的字符串值
        
    Returns:
        bool: True 如果有多个值，False 如果只有一个或没有值
    """
    if not value or value.strip() == '':
        return False
    
    # 检查是否包含分号分隔符
    return '; ' in value

def process_csv_add_column(input_file='Transcript_id_formatted.csv', 
                          output_file='Transcript_id_with_multiple_flag.csv'):
    """
    处理CSV文件，添加多值标识列
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    try:
        print(f"读取文件: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            
            # 读取表头
            header = next(reader)
            print(f"原始表头: {header}")
            
            # 找到需要检查的列的索引
            ensembl_col = None
            external_col = None
            
            for i, col_name in enumerate(header):
                if col_name == 'ensembl_gene_id':
                    ensembl_col = i
                elif col_name == 'external_gene_name':
                    external_col = i
            
            if ensembl_col is None or external_col is None:
                print("错误: 找不到需要的列")
                return
            
            print(f"ensembl_gene_id 列索引: {ensembl_col}")
            print(f"external_gene_name 列索引: {external_col}")
            
            # 读取所有数据
            rows = []
            for row in reader:
                rows.append(row)
            
            print(f"读取了 {len(rows)} 行数据")
        
        # 添加新列到表头
        new_header = header + ['single_value']
        
        # 处理数据并写入新文件
        print(f"处理数据并保存到: {output_file}")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            
            # 写入新表头
            writer.writerow(new_header)
            
            # 处理并写入数据
            yes_count = 0
            false_count = 0
            
            for i, row in enumerate(rows):
                if len(row) > max(ensembl_col, external_col):
                    # 检查两列是否有多个值
                    ensembl_has_multiple = has_multiple_values(row[ensembl_col])
                    external_has_multiple = has_multiple_values(row[external_col])
                    
                    # 如果任一列有多个值，则标记为 false，否则为 yes
                    if ensembl_has_multiple or external_has_multiple:
                        single_value_flag = 'false'
                        false_count += 1
                    else:
                        single_value_flag = 'yes'
                        yes_count += 1
                    
                    # 添加新列值
                    new_row = row + [single_value_flag]
                else:
                    # 如果行数据不完整，默认为 yes
                    new_row = row + ['yes']
                    yes_count += 1
                
                writer.writerow(new_row)
                
                # 显示进度
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
        
        print("=" * 50)
        print("处理完成!")
        print(f"总行数: {len(rows)}")
        print(f"single_value = 'yes' 的行数: {yes_count}")
        print(f"single_value = 'false' 的行数: {false_count}")
        print(f"输出文件: {output_file}")
        print("=" * 50)
        
        # 显示一些示例
        print("\n处理后的示例数据:")
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            
            print(f"新表头: {header}")
            print("\n示例行:")
            
            count = 0
            for row in reader:
                if len(row) > max(ensembl_col, external_col):
                    print(f"  {row[0]} | {row[ensembl_col]} | {row[external_col]} | {row[-1]}")
                    count += 1
                    if count >= 5:
                        break
        
        # 显示一些 false 的示例
        print("\n包含多个值的示例 (single_value = false):")
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            
            count = 0
            for row in reader:
                if len(row) > max(ensembl_col, external_col) and row[-1] == 'false':
                    print(f"  {row[0]} | {row[ensembl_col]} | {row[external_col]} | {row[-1]}")
                    count += 1
                    if count >= 3:
                        break
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("🔧 添加多值标识列工具")
    print("📝 检查 ensembl_gene_id 和 external_gene_name 是否包含多个值")
    print("=" * 60)
    process_csv_add_column()

#!/usr/bin/env python3
"""
脚本功能：
1. 获取Gene_01、Gene_02、Gene_03文件夹下所有txt文件名
2. 提取第一个"_"前面的数据作为样本ID
3. 将三个文件夹的样本ID整理成三列保存到CSV文件
4. 找出在至少两个文件夹中出现的重复样本ID并保存到另一个CSV文件
"""

import os
import csv
from collections import defaultdict

def extract_sample_id(filename):
    """从文件名中提取第一个'_'前面的样本ID"""
    if filename.endswith('.txt'):
        return filename.split('_')[0]
    return None

def get_sample_ids_from_folder(folder_path):
    """获取指定文件夹下所有txt文件的样本ID"""
    sample_ids = []
    if os.path.exists(folder_path):
        for filename in os.listdir(folder_path):
            if filename.endswith('.txt'):
                sample_id = extract_sample_id(filename)
                if sample_id:
                    sample_ids.append(sample_id)
    return sorted(list(set(sample_ids)))  # 去重并排序

def main():
    # 定义文件夹路径
    folders = ['Transcript_01', 'Transcript_02', 'Transcript_03']
    
    # 存储每个文件夹的样本ID
    folder_sample_ids = {}
    
    print("正在处理文件夹...")
    for folder in folders:
        print(f"处理 {folder}...")
        sample_ids = get_sample_ids_from_folder(folder)
        folder_sample_ids[folder] = sample_ids
        print(f"  找到 {len(sample_ids)} 个唯一样本ID")
    
    # 创建包含所有样本ID的CSV文件
    max_length = max(len(ids) for ids in folder_sample_ids.values())

    # 准备数据
    output_file = 'transcript_sample_ids.csv'
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # 写入表头
        writer.writerow(folders)

        # 写入数据行
        for i in range(max_length):
            row = []
            for folder in folders:
                ids = folder_sample_ids[folder]
                if i < len(ids):
                    row.append(ids[i])
                else:
                    row.append('')  # 用空字符串填充
            writer.writerow(row)

    print(f"\n所有样本ID已保存到: {output_file}")
    print(f"文件包含 {max_length} 行数据")
    
    # 显示每个文件夹的统计信息
    print("\n各文件夹样本ID统计:")
    for folder in folders:
        print(f"  {folder}: {len(folder_sample_ids[folder])} 个样本")
    
    # 找出重复的样本ID（在至少两个文件夹中出现）
    print("\n正在查找重复样本...")
    
    # 统计每个样本ID在哪些文件夹中出现
    sample_occurrence = defaultdict(list)
    
    for folder, sample_ids in folder_sample_ids.items():
        for sample_id in sample_ids:
            sample_occurrence[sample_id].append(folder)
    
    # 找出在至少两个文件夹中出现的样本ID
    duplicated_samples = []
    for sample_id, folders_list in sample_occurrence.items():
        if len(folders_list) >= 2:
            duplicated_samples.append({
                'Sample_ID': sample_id,
                'Folders': ', '.join(folders_list),
                'Count': len(folders_list)
            })
    
    # 按出现次数降序排序
    duplicated_samples.sort(key=lambda x: x['Count'], reverse=True)
    
    if duplicated_samples:
        # 保存重复样本到CSV
        duplicate_output_file = 'transcript_duplicated_samples.csv'
        with open(duplicate_output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            writer.writerow(['Sample_ID', 'Folders', 'Count'])

            # 写入数据
            for sample in duplicated_samples:
                writer.writerow([sample['Sample_ID'], sample['Folders'], sample['Count']])
        
        print(f"\n重复样本已保存到: {duplicate_output_file}")
        print(f"找到 {len(duplicated_samples)} 个重复样本")
        
        # 显示重复样本的统计信息
        print("\n重复样本统计:")
        count_stats = defaultdict(int)
        for sample in duplicated_samples:
            count_stats[sample['Count']] += 1
        
        for count, num_samples in sorted(count_stats.items(), reverse=True):
            print(f"  出现在 {count} 个文件夹中的样本: {num_samples} 个")
        
        # 显示前10个重复样本
        print("\n前10个重复样本:")
        for i, sample in enumerate(duplicated_samples[:10]):
            print(f"  {i+1}. {sample['Sample_ID']} (出现在: {sample['Folders']})")
    else:
        print("\n未找到重复样本")
    
    print("\n处理完成！")

if __name__ == "__main__":
    main()
